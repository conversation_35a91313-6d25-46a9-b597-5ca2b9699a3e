import { TimeSeriesStats, TimestampRecord } from '../../anomaly-detection.types';

export function usePopupChartData(dayData: { date: string; data: TimeSeriesStats; sensor: string } | null) {
  if (!dayData) return null;

  const { data: timeSeriesData, sensor } = dayData;
  const records = timeSeriesData.records;

  // Check if records exist and have data
  if (!records || records.length === 0) {
    return null;
  }

  // Extract timestamps and values from records
  const xAxis = records.map((record) => record.timestamp);

  // Get the actual sensor values from the records based on the selected sensor
  const yAxis = records.map((record) => {
    // Get the sensor value from the record using the sensor key
    const sensorValue = record[sensor as keyof TimestampRecord] as number;

    // If the sensor value exists and is a number, use it
    if (sensorValue !== undefined && sensorValue !== null && typeof sensorValue === 'number') {
      return sensorValue;
    }
  });

  return {
    xAxis,
    yAxis,
    records,
    sensor,
    date: dayData.date,
    anomalyCount: timeSeriesData.total_anomalies_count,
    frozenCount: timeSeriesData.total_frozen_count,
    outlierCount: timeSeriesData.total_outlier_count,
  };
}
