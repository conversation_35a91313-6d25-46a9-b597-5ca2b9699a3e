import { DataAnalyticsResponse } from '../../data-analytics.types';
import { frugalColorMap } from './data-analytics-chart-consumption.constants';

export function useChartData(data: DataAnalyticsResponse | null) {
  if (!data) return null;

  const xAxis = data.data_analytics.measurement_graphs.x_axis.calculated_categories;
  const xAxisRaw = data.data_analytics.measurement_graphs.x_axis.raw_categories;
  const mefcm = data.data_analytics.measurement_graphs.y_axis.mefcm.calculated_data;
  const boifcm = data.data_analytics.measurement_graphs.y_axis.boifcm.calculated_data;
  const auxfcm = data.data_analytics.measurement_graphs.y_axis.auxfcm.calculated_data;
  const mefcm_raw = data.data_analytics.measurement_graphs.y_axis.mefcm.raw_data;
  const boifcm_raw = data.data_analytics.measurement_graphs.y_axis.boifcm.raw_data;
  const auxfcm_raw = data.data_analytics.measurement_graphs.y_axis.auxfcm.raw_data;
  const frugalStatus = data.data_analytics.frugalStatusTime;

  const sorted = xAxis
    .map((date, i) => ({
      date,
      mefcm: mefcm[i],
      boifcm: boifcm[i],
      auxfcm: auxfcm[i],
      mefcm_raw: mefcm_raw?.[i] || null,
      boifcm_raw: boifcm_raw?.[i] || null,
      auxfcm_raw: auxfcm_raw?.[i] || null,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const mefcmSeries = sorted.map((d) => [new Date(d.date).getTime(), d.mefcm]);
  const boifcmSeries = sorted.map((d) => [new Date(d.date).getTime(), d.boifcm]);
  const auxfcmSeries = sorted.map((d) => [new Date(d.date).getTime(), d.auxfcm]);
  const mefcmRawSeries =
    mefcm_raw && xAxisRaw
      ? mefcm_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const boifcmRawSeries =
    boifcm_raw && xAxisRaw
      ? boifcm_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const auxfcmRawSeries =
    auxfcm_raw && xAxisRaw
      ? auxfcm_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const plotBands = frugalStatus.map((r) => ({
    from: new Date(r.from).getTime(),
    to: new Date(r.to).getTime(),
    color: frugalColorMap[r.frugal_mode] || 'rgba(0, 0, 0, 0.0)',
  }));

  return {
    mefcmSeries,
    boifcmSeries,
    auxfcmSeries,
    mefcmRawSeries,
    boifcmRawSeries,
    auxfcmRawSeries,
    plotBands,
    frugalStatus,
  };
}
