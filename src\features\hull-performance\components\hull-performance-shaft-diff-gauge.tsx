'use client';

import React from 'react';
import GaugeChart from '@/components/ui-extensions/gauge-chart';
import { useHullPerformanceStore } from '../hull-performance.store';

export default function ShaftDiffGauge() {
  const { shaftDiff, isLoading } = useHullPerformanceStore();

  return (
    <GaugeChart title='Shaft Power Change' value={shaftDiff} min={-200} max={200} unit='kW' isPositiveGood={false} isLoading={isLoading} />
  );
}
