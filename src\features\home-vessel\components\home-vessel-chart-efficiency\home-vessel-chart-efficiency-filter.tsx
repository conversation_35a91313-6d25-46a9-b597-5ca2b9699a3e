'use client';

import React, { useEffect, useState } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui-extensions/date-range-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select';
import { useHomeVesselStore } from '../../home-vessel.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { VesselEfficiencyPostDTO } from '../../home-vessel-efficiency.types';
import { DateRange } from 'react-day-picker';
import { endOfDay, formatToServerDateTime, startOfDay } from '@/lib/date-utils';

const getDefaultDateRange = (): DateRange => {
  const to = new Date();
  const from = new Date(to);
  from.setDate(to.getDate() - 7);
  return { from, to };
};

export default function HomeVesselChartEfficiencyFilter() {
  // State
  const [fpOn, setFpOn] = useState(true);
  const [fpOff, setFpOff] = useState(true);
  const [loadCondition, setLoadCondition] = useState('All');
  const [beaufort, setBeaufort] = useState('4');
  const [dateRange, setDateRange] = useState<DateRange>(getDefaultDateRange());

  // Store
  const { fetchVesselEfficiency } = useHomeVesselStore();
  const { currentVessel } = useVesselStore();

  // Handlers
  const handleSearch = () => {
    if (!currentVessel) return;

    const requestBody: VesselEfficiencyPostDTO = {
      request: {
        vessel_id: currentVessel.id,
        vessel_imo: currentVessel.imo,
        vessel_name: currentVessel.vessel_name,
        selected_owner_vat: currentVessel.assigned_owner_vat,
        from_date: formatToServerDateTime(startOfDay(dateRange.from)),
        to_date: formatToServerDateTime(endOfDay(dateRange.to)),
      },
      filters: {
        type: 'vessel',
        fpOn,
        fpOff,
        loadCondition,
        beaufort: Number(beaufort),
      },
      owner_vat: currentVessel.assigned_owner_vat,
    };

    fetchVesselEfficiency(requestBody);
  };

  // Auto-search when vessel changes
  useEffect(() => {
    if (currentVessel) handleSearch();
  }, [currentVessel]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline'>Filters</Button>
      </PopoverTrigger>
      <PopoverContent className='mr-2 w-69'>
        <div className='mb-2 text-sm font-medium'>Filter Options</div>
        <div className='space-y-4'>
          {/* Date Range */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Date Range</Label>
            <DateRangePicker value={dateRange} onChange={(value) => setDateRange(value ?? getDefaultDateRange())} />
          </div>

          {/* Load Condition */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Load Condition</Label>
            <Select value={loadCondition} onValueChange={setLoadCondition}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select...' />
              </SelectTrigger>
              <SelectContent>
                {['All', 'Laden', 'Partially Laden', 'Ballast'].map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Frugal Propulsion */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Frugal Propulsion On / Off</Label>
            <div className='flex gap-4'>
              {[
                { id: 'fp-on', label: 'FP On', checked: fpOn, onChange: setFpOn },
                { id: 'fp-off', label: 'FP Off', checked: fpOff, onChange: setFpOff },
              ].map(({ id, label, checked, onChange }) => (
                <div key={id} className='flex items-center space-x-2'>
                  <Checkbox id={id} checked={checked} onCheckedChange={(v) => onChange(!!v)} />
                  <Label htmlFor={id} className='text-xs'>
                    {label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Beaufort */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Beaufort</Label>
            <Select value={beaufort} onValueChange={setBeaufort}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select...' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All</SelectItem>
                {Array.from({ length: 13 }, (_, i) => (
                  <SelectItem key={i} value={i.toString()}>
                    {i}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className='mt-4 flex justify-end'>
          <Button size='sm' onClick={handleSearch}>
            Search
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
