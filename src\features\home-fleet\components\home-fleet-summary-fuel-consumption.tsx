'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useHomeFleetStore } from '../home-fleet.store';

export const HomeFleetSummaryFuelConsumption = () => {
  const { fleetData, isLoadingFleetData } = useHomeFleetStore();

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <h2 className='text-md mb-4 font-semibold tracking-tight'>Fuel Consumption Summary</h2>
      <div className='space-y-3'>
        <StatValue
          label='Main Engine'
          value={fleetData?.fleet_fc_yearly_overview.fuel_consumption.me ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Auxiliary Engines'
          value={fleetData?.fleet_fc_yearly_overview.fuel_consumption.aux ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Boilers'
          value={fleetData?.fleet_fc_yearly_overview.fuel_consumption.boiler ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Total'
          value={fleetData?.fleet_fc_yearly_overview.fuel_consumption.total ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
      </div>
    </div>
  );
};
