'use client';

import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { handleNoSeriesEnabled, handleShowAllSeries } from './hull-performance-ai-chart-no-series';
import { handleNoDataAvailable, handleHideNoDataPlaceholder } from './hull-performance-ai-chart-no-data';
import { useHullPerformanceStore } from '../../hull-performance.store';

interface ChartRendererProps {
  chartOptions: Highcharts.Options;
  chartKey: number;
  useSearchData: boolean;
  hasVisibleSeries: boolean;
  userDisabledSeries: boolean;
  hasData: boolean;
  onChartReady: (chart: Highcharts.Chart) => void;
  setHasVisibleSeries: (hasVisibleSeries: boolean) => void;
  setUserDisabledSeries: (userDisabledSeries: boolean) => void;
}

export default function ChartRenderer({
  chartOptions,
  chartKey,
  useSearchData,
  hasVisibleSeries,
  userDisabledSeries,
  hasData,
  onChartReady,
  setHasVisibleSeries,
  setUserDisabledSeries,
}: ChartRendererProps) {
  const [chartInstance, setChartInstance] = useState<Highcharts.Chart | null>(null);

  // Create enhanced chart options with event handlers
  const enhancedOptions: Highcharts.Options = {
    ...chartOptions,
    chart: {
      ...chartOptions.chart,
      events: {
        render: function (this: Highcharts.Chart) {
          const container = this.container;
          if (!container) return;

          if (!hasData) {
            // If no data is available, show the no data placeholder
            handleNoDataAvailable({ container });
          } else if (!hasVisibleSeries && userDisabledSeries) {
            // If user has disabled all series, show the no series enabled placeholder
            handleNoSeriesEnabled({ container });
          } else {
            // Otherwise show all chart elements and hide placeholders
            handleShowAllSeries({ container });
            handleHideNoDataPlaceholder({ container });
          }
        },
      },
    },
    plotOptions: {
      ...chartOptions.plotOptions,
      series: {
        ...chartOptions.plotOptions?.series,
        events: {
          // Track when user manually toggles series visibility
          hide: function () {
            setUserDisabledSeries(true);

            // Check if any series are still visible
            if (chartInstance) {
              try {
                const anySeriesVisible = chartInstance.series.some((s) => s.visible);
                setHasVisibleSeries(anySeriesVisible || false);
              } catch (_) {
                // If we can't check series visibility, assume there are visible series
                setHasVisibleSeries(true);
              }
            }
          },
          show: function () {
            // When a series is shown, we know at least one series is visible
            setHasVisibleSeries(true);
          },
        },
      },
    },
  };

  // Get the isLoading state from the store
  const { isLoading } = useHullPerformanceStore();

  return (
    <HighchartsReact
      key={`ai-chart-${chartKey}-${useSearchData ? 'search' : 'normal'}-${isLoading ? 'loading' : 'ready'}`}
      highcharts={Highcharts}
      options={enhancedOptions}
      callback={(chart: Highcharts.Chart) => {
        setChartInstance(chart);
        onChartReady(chart);
      }}
    />
  );
}
