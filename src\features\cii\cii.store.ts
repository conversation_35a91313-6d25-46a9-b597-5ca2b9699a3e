import { create } from 'zustand';
import { CiiPutDto, CiiResponse, CiiYearsResponse, WeeklyData, YearlyData } from './cii.types';
import { useVesselStore } from '../vessels/vessel.store';
import axios from 'axios';
import { toast } from 'sonner';

interface CIIStore {
  year: string | null;
  years: string[];
  isLoading: boolean;
  yearData: YearlyData | null;
  weeklyData: Record<string, WeeklyData>;
  weeklyDataManual: Record<string, WeeklyData>;
  weeklyDataMerged: Record<string, WeeklyData>;

  setYear: (year: string) => void;
  fetchAllCIIData: () => Promise<void>;
  resetAllCIIData: () => Promise<void>;
  resetWeeklyCIIData: (week: number) => Promise<void>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updateWeeklyCIIData: (data: any) => Promise<void>;
  setWeeklyData: (weeklyData: Record<string, WeeklyData>, weeklyDataManual: Record<string, WeeklyData>) => void;
}

function mergeDeep<T extends object, U extends object>(target: T, source: U): T & U {
  const output = { ...target } as T & U;

  for (const key in source) {
    const sourceValue = source[key as keyof U];
    const targetValue = target[key as unknown as keyof T];

    if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue) && targetValue && typeof targetValue === 'object') {
      output[key] = mergeDeep(targetValue as object, sourceValue as object) as (T & U)[Extract<keyof U, string>];
    } else {
      output[key] = sourceValue as (T & U)[Extract<keyof U, string>];
    }
  }

  return output;
}

export const useCIIStore = create<CIIStore>((set, get) => ({
  year: null,
  years: [],
  isLoading: false,
  yearData: null,
  weeklyData: {},
  weeklyDataManual: {},
  weeklyDataMerged: {},

  setYear: (year: string) => set({ year }),

  fetchAllCIIData: async () => {
    set({ isLoading: true, yearData: null, weeklyData: {}, weeklyDataManual: {}, weeklyDataMerged: {}, years: [] });

    try {
      const { currentVessel } = useVesselStore.getState();

      if (!currentVessel?.imo) {
        toast.error('No vessel selected');
        return;
      }

      const yearParams = {
        vessel_imo: currentVessel.imo.toString(),
        owner_vat: currentVessel.assigned_owner_vat,
      };

      const { data: ciiYearsResponse } = await axios.get<CiiYearsResponse>(`/api/cii/years?${new URLSearchParams(yearParams).toString()}`);

      const availableYears = ciiYearsResponse.cii_years;

      if (!availableYears.length) {
        toast.error('No CII data available.');
        return;
      }

      set({ years: availableYears });
      let selectedYear = get().year;
      if (!selectedYear || !availableYears.includes(selectedYear)) {
        selectedYear = availableYears[0];
        set({ year: selectedYear });
      }

      const params = {
        vessel_imo: currentVessel.imo.toString(),
        year: selectedYear,
        owner_vat: currentVessel.assigned_owner_vat,
      };

      const { data: ciiResponse } = await axios.get<CiiResponse>(`/api/cii?${new URLSearchParams(params).toString()}`);

      const yearSummary = ciiResponse.cii_summary.years[selectedYear];

      set({ yearData: yearSummary.yearly });
      get().setWeeklyData(yearSummary.weekly, yearSummary.manual_weekly);
    } catch (_error) {
      toast.error('Failed to fetch CII data.');
    } finally {
      set({ isLoading: false });
    }
  },

  resetAllCIIData: async () => {
    try {
      const { currentVessel } = useVesselStore.getState();

      const params = {
        vessel_imo: currentVessel!.imo.toString(),
        year: get().year ?? '',
        owner_vat: currentVessel!.assigned_owner_vat,
      };

      await axios.post(`/api/cii/reset?${new URLSearchParams(params).toString()}`);
      toast.success('CII data reset successfully.');
    } catch (_error) {
      toast.error('Failed to reset CII data.');
    }
  },

  resetWeeklyCIIData: async (week: number) => {
    try {
      const { currentVessel } = useVesselStore.getState();

      const params = {
        vessel_imo: currentVessel!.imo.toString(),
        year: get().year ?? '',
        week_numb: week.toString(),
        owner_vat: currentVessel!.assigned_owner_vat,
      };

      await axios.post(`/api/cii/reset?${new URLSearchParams(params).toString()}`);
      toast.success('CII data reset successfully.');
    } catch (_error) {
      toast.error('Failed to reset CII data.');
    }
  },

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updateWeeklyCIIData: async (data: any) => {
    const { currentVessel } = useVesselStore.getState();

    const formattedData: CiiPutDto = {
      distance: data.distance.toString(),
      fuel: data.fuel.map((fuel: { fuelType: string; fuelConsumption: number }) => ({
        fuelType: fuel.fuelType.toString(),
        fuelConsumption: fuel.fuelConsumption.toString(),
      })),
      selected_vessel: {
        vessel_name: currentVessel?.vessel_name ?? '',
        id: currentVessel?.id ?? 0,
        imo: currentVessel?.imo ?? 0,
        assigned_owner_vat: currentVessel?.assigned_owner_vat ?? '',
      },
      selected_year: get().year ?? '',
      week_numb: data.week_numb.toString(),
    };

    try {
      const { data: ciiResponse } = await axios.put<CiiResponse>(`/api/cii`, formattedData);
      toast.success('CII data updated successfully.');

      const yearSummary = ciiResponse.cii_summary.years[get().year ?? ''];

      set({ yearData: yearSummary.yearly });
      get().setWeeklyData(yearSummary.weekly, yearSummary.manual_weekly);
    } catch (_error) {
      toast.error('Failed to update CII data.');
    }
  },

  setWeeklyData: (weeklyData: Record<string, WeeklyData>, weeklyDataManual: Record<string, WeeklyData>) => {
    const merged: Record<string, WeeklyData> = {};

    for (const week in weeklyData) {
      const base = weeklyData[week];
      const override = weeklyDataManual[week];

      merged[week] = override ? mergeDeep(base, override) : base;
    }

    // Include manual-only weeks
    for (const week in weeklyDataManual) {
      if (!merged[week]) {
        merged[week] = weeklyDataManual[week];
      }
    }

    set({
      weeklyData,
      weeklyDataManual,
      weeklyDataMerged: merged,
    });
  },
}));
