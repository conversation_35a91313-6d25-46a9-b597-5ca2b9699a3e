import { colorMap, frugalColorMap, frugalModeLabels } from './data-analytics-chart-speed.constants';
import Highcharts from 'highcharts';
import { FrugalStatusTime } from '../../data-analytics.types';
import { PlotBands, Series } from './data-analytics-chart-speed.types';

export function getChartOptions({
  lspdSeries,
  gspdSeries,
  lspdRawSeries,
  gspdRawSeries,
  plotBands,
  frugalStatus,
  chartId,
  allChartRefs,
  isSyncing,
}: {
  lspdSeries: Series;
  gspdSeries: Series;
  lspdRawSeries?: Series | null;
  gspdRawSeries?: Series | null;
  plotBands: PlotBands[];
  frugalStatus: FrugalStatusTime[];
  chartId: string;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
  isSyncing: boolean;
}): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      name: 'Log Speed (kn)',
      data: lspdSeries,
      type: 'line',
      color: colorMap.lspd,
      yAxis: 0,
      marker: { enabled: false },
    },
    {
      name: 'GPS Speed (kn)',
      data: gspdSeries,
      type: 'line',
      color: colorMap.gspd,
      yAxis: 0,
      marker: { enabled: false },
    },
  ];

  if (lspdRawSeries) {
    series.push({
      name: 'Log Speed (kn) (Raw)',
      data: lspdRawSeries,
      type: 'line',
      color: colorMap.lspd_raw,
      yAxis: 0,
      dashStyle: 'ShortDot',
      stickyTracking: false,
      marker: { enabled: false },
    });
  }

  if (gspdRawSeries) {
    series.push({
      name: 'GPS Speed (kn) (Raw)',
      data: gspdRawSeries,
      type: 'line',
      color: colorMap.gspd_raw,
      yAxis: 0,
      dashStyle: 'ShortDot',
      stickyTracking: false,
      marker: { enabled: false },
    });
  }

  return {
    chart: {
      marginLeft: 78,
      marginRight: 108,
      zooming: {
        type: 'x',
      },
    },
    title: { text: '' },
    xAxis: {
      type: 'datetime',
      plotBands,
      max: new Date().getTime(),
      crosshair: true,
      labels: { style: { color: 'black' } },
      events: {
        setExtremes(e: Highcharts.AxisSetExtremesEventObject) {
          if (isSyncing) return;

          isSyncing = true;
          Object.entries(allChartRefs).forEach(([key, ref]) => {
            if (key !== chartId && ref.current) {
              ref.current.xAxis[0].setExtremes(e.min, e.max, true, false);
            }
          });
          isSyncing = false;
        },
      },
    },
    yAxis: [
      {
        title: { text: 'Speed (kn)', style: { color: 'black' } },
        labels: { style: { color: 'black' } },
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltip(this, frugalStatus);
      },
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          align: 'right',
          verticalAlign: 'top',
          menuItems: [
            'viewFullscreen',
            'printChart',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS',
          ],
        },
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
      x: -40,
      y: -4,
    },
    credits: { enabled: false },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
            },
          },
        },
      ],
    },
  };
}

export function formatTooltip(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number }[] },
  frugalStatusData: FrugalStatusTime[]
) {
  const x = ctx.x;
  const points = ctx.points;

  const frugal = frugalStatusData.find((r) => new Date(r.from).getTime() <= x && new Date(r.to).getTime() >= x);
  const mode = frugal?.frugal_mode ?? -1;
  const label = frugalModeLabels[mode] || 'Idle';
  const color = frugalColorMap[mode] || '#ccc';

  const lines = points.map((p) => `<span style="color:${p.color}">\u25CF</span> ${p.series.name}: <b>${p.y}</b>`);

  return `
    <div style="font-size: 13px">
      <div style="font-weight: bold; margin-bottom: 4px;">
        ${Highcharts.dateFormat('%A, %b %e, %H:%M', x)}
      </div>
      ${lines.join('<br/>')}
      <hr style="margin: 6px 0;" />
      <span style="color:${color}">\u25CF</span> <b>Frugal Mode:</b> ${label}
    </div>
  `;
}
