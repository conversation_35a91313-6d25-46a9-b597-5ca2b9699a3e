import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { CiiYearsResponse } from '@/features/cii/cii.types';
import { z } from 'zod';

// === GET QUERY SCHEMA ===
const getQuerySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
});

// === GET ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = getQuerySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/cii-years', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<CiiYearsResponse>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
