import { UserRole } from '@/features/users/user.types';
import { ensureMinRole } from '@/lib/auth-guard';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === DELETE PARAMS SCHEMA ===
const deleteParamsSchema = z.object({
  id: z
    .string()
    .transform((val) => parseInt(val, 10))
    .refine((val) => !isNaN(val), { message: 'id must be a valid number' }),
});

// === DELETE ENDPOINT ===
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // Validate params
  const { id } = await params;
  const validatedParams = deleteParamsSchema.safeParse({ id });
  if (!validatedParams.success) {
    return NextResponse.json({ errors: validatedParams.error.flatten().fieldErrors }, { status: 400 });
  }

  // Ensure min role
  const [forbiddenResponse] = await ensureMinRole(UserRole.Admin);
  if (forbiddenResponse) return forbiddenResponse;

  // Build URL
  const url = new URL(`/users/${validatedParams.data.id}`, process.env.API_URL);

  // Make request
  try {
    await httpClient.delete(request, url.toString());
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
