import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { z } from 'zod';

// === POST QUERY SCHEMA ===
const querySchema = z.object({
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  year: z.string().nonempty('year must be provided'),
  week_numb: z.string().optional().default('0'),
});

// === POST ENDPOINT ===
export async function POST(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const { owner_vat, vessel_imo, year, week_numb } = validatedQuery.data;
  const path =
    week_numb !== '0'
      ? `/cii/reset?vessel_imo=${vessel_imo}&year=${year}&week_numb=${week_numb}&owner_vat=${owner_vat}`
      : `/cii/reset/all?vessel_imo=${vessel_imo}&year=${year}&owner_vat=${owner_vat}`;
  const url = new URL(path, process.env.API_URL);

  // Make request
  try {
    await httpClient.get<void>(request, url.toString());
    return NextResponse.json({ message: 'CII data reset successfully' });
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
