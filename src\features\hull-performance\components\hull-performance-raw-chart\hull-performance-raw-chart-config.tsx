'use client';

import Highcharts from 'highcharts';
import { DataRaw, DataSearch } from '../../hull-performance.types';
import { generateSeriesData } from './hull-performance-raw-chart-data-processor';

export interface ChartConfigProps {
  chartData: DataRaw | DataSearch;
  selectedSpeed: number | 'All';
  hasVisibleSeries?: boolean;
}

export const createChartOptions = ({ chartData, selectedSpeed }: ChartConfigProps): Highcharts.Options => {
  if (!chartData) return {};

  // Generate series data
  const series = generateSeriesData(chartData, selectedSpeed);

  const options: Highcharts.Options = {
    chart: { zooming: { type: 'x' } },
    title: { text: '' },
    credits: { enabled: false },
    xAxis: {
      type: 'datetime',
      labels: {
        formatter: function () {
          // Cast to any to avoid TypeScript errors
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const value = (this as any).value;
          if (!value) return '';

          const date = new Date(value);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const isFirstTick = (this as any).isFirst;

          // Check if this is the first day of a year
          const isFirstDayOfYear = date.getMonth() === 0 && date.getDate() === 1;

          // Format the date - using month name and day side by side
          const month = Highcharts.dateFormat('%b', value);
          const day = Highcharts.dateFormat('%e', value);
          const year = date.getFullYear();

          // Check if this is the first day of the month
          const isFirstDayOfMonth = date.getDate() === 1;

          // For first day of month, show only month name; for other days, show month and day
          const monthDay = isFirstDayOfMonth
            ? `<span style="white-space: nowrap;">${month}</span>`
            : `<span style="white-space: nowrap;">${month} ${day}</span>`;

          // Show year for first tick or first day of a new year
          if (isFirstTick || isFirstDayOfYear) {
            return `<div style="text-align: center;">
              <div style="color: #2b5797;">${year}</div>
              <div>${monthDay}</div>
            </div>`;
          }

          // Format without year for other ticks
          return monthDay;
        },
        useHTML: true,
      },
      events: {
        setExtremes: function (e: Highcharts.AxisSetExtremesEventObject) {
          // Prevent zooming in to less than two weeks (14 days)
          const minRangeMs = 14 * 24 * 60 * 60 * 1000; // 14 days in milliseconds

          if (e.min !== undefined && e.max !== undefined) {
            const currentRange = e.max - e.min;

            // If the user is trying to zoom in to less than 14 days, adjust the range
            if (currentRange < minRangeMs) {
              const center = (e.min + e.max) / 2;
              const halfMinRange = minRangeMs / 2;

              // Set new extremes that maintain the minimum range
              const newMin = center - halfMinRange;
              const newMax = center + halfMinRange;

              // Prevent the default zoom and set our custom range
              e.preventDefault?.();
              this.setExtremes(newMin, newMax, true, false);
              return false;
            }
          }
        },
      },
    },
    yAxis: {
      title: { text: 'Shaft Power (kW)' },
      min: 0,
      labels: { style: { fontWeight: 'normal' } },
    },
    tooltip: {
      shared: false,
      useHTML: true,
      headerFormat: '{point.x:%Y-%m-%d %H:%M}<br/>',
      pointFormat: '<span style="color:{series.color}">●</span> Shaft Power: <b>{point.y:.2f} kW</b>',
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
      useHTML: true,
      labelFormatter: function () {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const series = this as any;

        // Check if this is a Draft series and has custom data
        if (series.name.startsWith('Draft ') && series.options && series.options.data && series.options.data.length > 0) {
          // Get the first data point that has the additional information
          const dataPoint = series.options.data.find(
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (point: any) => point.overconsumption_fuel !== undefined && point.daily_rate_of_shaft_change !== undefined
          );

          if (dataPoint) {
            // Format the tooltip text with the additional information
            const tooltipText = `Overconsumption: ${dataPoint.overconsumption_fuel.toFixed(2)} kg/day
              Daily Rate of Shaft Change: ${dataPoint.daily_rate_of_shaft_change.toFixed(2)} kW/day`;

            // Return the span with title attribute for tooltip
            return `<div style="display: flex; align-items: center;">
              <span title="${tooltipText}">${series.name}</span>
            </div>`;
          }
        }

        // Default return for other series
        return `<div style="display: flex; align-items: center;">
          <span>${series.name}</span>
        </div>`;
      },
    },
    plotOptions: {
      scatter: {
        marker: { radius: 4 },
        tooltip: {
          headerFormat: '{point.x:%Y-%m-%d %H:%M}<br/>',
          pointFormat: '<span style="color:{series.color}">●</span> Shaft Power: <b>{point.y:.2f} kW</b>',
        },
        stickyTracking: false,
        visible: true, // Force all scatter series to be visible by default
      },
      line: {
        marker: { enabled: false },
        visible: true, // Force all line series to be visible by default
      },
      spline: {
        marker: { enabled: false },
        tooltip: {
          headerFormat: '{point.x:%Y-%m-%d %H:%M}<br/>',
          pointFormat: '<span style="color:{series.color}">●</span> Shaft Power: <b>{point.y:.2f} kW</b>',
        },
        visible: true, // Force all spline series to be visible by default
        connectNulls: false, // Don't connect across null points (gaps)
        gapUnit: 'value',
        gapSize: 1,
      },
      series: {
        connectNulls: false,
        visible: true, // Force all series to be visible by default
      },
    },
    series: series as Highcharts.SeriesOptionsType[],
  };

  return options;
};
