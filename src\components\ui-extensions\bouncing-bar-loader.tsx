'use client';

import { cn } from '@/lib/utils';

type BouncingBarLoaderProps = {
  barWidth?: string;
  absolute?: boolean;
  className?: string;
};

export default function BouncingBarLoader({ barWidth = '220px', absolute = false, className }: BouncingBarLoaderProps) {
  const containerClass = absolute ? 'absolute top-0 left-0 w-full overflow-hidden' : 'relative w-full overflow-hidden';

  return (
    <>
      <div
        className={cn(containerClass, className)}
        style={{
          height: '4px',
          background: 'var(--muted)',
          borderRadius: 'var(--radius)',
        }}
      >
        <div
          className='bouncing-bar'
          style={{
            width: barWidth,
            height: '4px',
            background: 'var(--primary)',
            borderRadius: 'var(--radius)',
            animation: `bounceLoader 0.8s ease-in-out infinite alternate`,
          }}
        />
      </div>

      <style>{`
        @keyframes bounceLoader {
          0% {
            left: 0;
            transform: translateX(-1%);
          }
          100% {
            left: 100%;
            transform: translateX(-99%);
          }
        }

        .bouncing-bar {
          position: absolute;
          top: 0;
          left: 0;
          box-sizing: border-box;
        }
      `}</style>
    </>
  );
}
