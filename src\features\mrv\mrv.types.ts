export interface MrvGetDto {
  mrv_data: Record<string, MrvVoyage>;
  mrv_data_copy: Record<string, MrvVoyage>;
  year: string;
}

export interface MrvVoyage {
  aft_draft: number;
  anomaly_count: {
    total: number;
  };
  approved_mrv_voyage: boolean;
  arrival_location: string;
  aux_consumption: number;
  boiler_consumption: number;
  cargo_type: string | null;
  departure_location: string;
  departure_port: string;
  description: string | null;
  destination_port: string;
  distance: number;
  emission_percentage_for_mrv: number;
  end_sailing_event: number;
  end_time: string;
  voyage_type: string;
  errors: string[];
  eu_heading: string;
  eu_mrv_count: number;
  forward_draft: number;
  fuel_consumption: number;
  fuel_consumption_HFO?: number;
  fuel_consumption_LFO?: number;
  fuel_consumption_MDO?: number;
  fuel_consumption_at_berth: number;
  fuel_consumption_at_sea: number;
  fuel_types: string[];
  invalid_keys: string[];
  mrv_fuel_consumption: number;
  navigation_statuses: string;
  ongoing: boolean;
  start_sailing_event: number;
  start_time: string;
  time_at_berth_days: number;
  time_at_berth_hours: number;
  time_at_berth_minutes: number;
  time_at_sea_days: number;
  time_at_sea_hours: number;
  time_at_sea_minutes: number;
  time_in_port_days: number | null;
  time_in_port_hours: number | null;
  time_in_port_minutes: number | null;
  time_sailed_days: number;
  time_sailed_hours: number;
  time_sailed_minutes: number;
  transport_work: number | null;
  uk_heading: string | null;
  uk_mrv_count: number;
  voyage_id: string;
  voyage_status: MrvVoyageStatus | null;
  cargo: number;
}

export type MrvVoyagePostDto = {
  departure_port: string;
  destination_port: string;
  distance: number;
  eu_heading: string;
  start_time: string;
  end_time: string;
  cargo: number;
  transport_work: number | null;
  fuel_consumption_MDO?: number;
  fuel_consumption_LFO?: number;
  fuel_consumption_HFO?: number;
  approved_mrv_voyage: boolean;
};

export type MrvVoyagePutDto = {
  voyage_id: string;
  departure_port: string;
  destination_port: string;
  distance: number;
  eu_heading: string;
  start_time: string;
  end_time: string;
  cargo: number;
  transport_work: number | null;
  fuel_consumption_MDO?: number;
  fuel_consumption_LFO?: number;
  fuel_consumption_HFO?: number;
  approved_mrv_voyage: boolean;
};

export type MrvVoyageStatus = 'Complete' | 'Incomplete' | 'Error' | 'Approved';
