'use client';

import { Button } from '@/components/ui/button';
import React from 'react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { toast } from 'sonner';
import { useVoyageStore } from '../voyage.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { FileDown } from 'lucide-react';

export function ReportGenerateYearly() {
  const [isLoading, setIsLoading] = React.useState(false);
  const { isLoading: isLoadingStore, year } = useVoyageStore();

  const handleGenerateReport = async () => {
    setIsLoading(true);

    try {
      // Get the current vessel after ensuring it's loaded
      const currentVessel = useVesselStore.getState().currentVessel;

      if (!currentVessel?.imo) {
        throw new Error('No vessel selected or vessel has no IMO');
      }

      // Generate the report URL with query parameters
      const reportUrl = `/api/mrv/report?vessel_imo=${currentVessel.imo}&vessel_name=${encodeURIComponent(
        currentVessel.vessel_name || ''
      )}&year=${year}&owner_vat=${currentVessel.assigned_owner_vat}`;

      // Fetch the PDF file
      const response = await fetch(reportUrl);

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `${year}-MRV-${currentVessel.vessel_name || 'vessel'}.pdf`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Report download successful');
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('An unexpected error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button variant='default' onClick={handleGenerateReport} disabled={isLoading || isLoadingStore}>
      {isLoading ? (
        <span className='mr-2'>
          <CircleSpinner size='xs' variant='default' />
        </span>
      ) : (
        <FileDown className='mr-2 h-4 w-4' />
      )}
      Download Report
    </Button>
  );
}
