import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { CiiResponse } from '@/features/cii/cii.types';
import { z } from 'zod';

// === GET QUERY SCHEMA ===
const getQuerySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  year: z.string().nonempty('year must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
});

// === PUT BODY SCHEMA ===
const putBodySchema = z.object({
  fuel: z.array(
    z.object({
      fuelType: z.string(),
      fuelConsumption: z.string(),
    })
  ),
  distance: z.string(),
  selected_vessel: z.object({
    vessel_name: z.string(),
    id: z.number(),
    imo: z.number(),
    assigned_owner_vat: z.string(),
  }),
  selected_year: z.string(),
  week_numb: z.string(),
});

// === GET ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = getQuerySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL with query params
  const url = new URL('/cii', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<CiiResponse>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}

// === PUT ENDPOINT ===
export async function PUT(request: NextRequest) {
  // Validate body
  const validatedBody = putBodySchema.safeParse(await request.json());
  if (!validatedBody.success) {
    return NextResponse.json({ errors: validatedBody.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/cii', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.put<{ status: string; redis: CiiResponse; msg: string }>(request, url.toString(), validatedBody.data);
    return NextResponse.json(res.redis);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
