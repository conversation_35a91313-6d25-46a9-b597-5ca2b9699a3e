/**
 * Utility function to format date to 'YYYY-MM-DD HH:MM:SS' format
 * This format matches the '%Y-%m-%d %H:%M:%S' format requested for export APIs
 */
export function formatDateTime(date: Date): string {
  const pad = (n: number) => String(n).padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
