import { SignalAnomalySummary, TimeSeriesStats } from '../../anomaly-detection.types';

export function useChartData(data: SignalAnomalySummary | null) {
  if (!data) return null;

  const xAxis = Object.keys(data.data);
  const yAxis = Object.values(data.data).map((d) => d.mean_value);
  const yAxisDetails: TimeSeriesStats[] = Object.values(data.data);

  return {
    xAxis,
    yAxis,
    yAxisDetails,
  };
}
