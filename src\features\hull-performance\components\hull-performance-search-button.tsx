'use client';

import { Button } from '@/components/ui/button';
import { useHullPerformanceStore } from '../hull-performance.store';
import { Search } from 'lucide-react';
import { toast } from 'sonner';
import { useVesselStore } from '@/features/vessels/vessel.store';

export function SearchButton() {
  const { isLoading, setSearchData, fromValue, toValue, hasAnyChartData } = useHullPerformanceStore();
  const { currentVessel } = useVesselStore();

  const handleClick = () => {
    if (!currentVessel?.imo || !currentVessel?.assigned_owner_vat) return;
    if (!fromValue || !toValue) {
      toast.error('Please select both from and to dates before searching');
      return;
    }
    // Let setSearchData handle the loading state
    setSearchData(currentVessel.imo, currentVessel.assigned_owner_vat, fromValue, toValue)
      .then(() => {
        toast.success('Successful search');
      })
      .catch((error) => {
        console.error('Error during search process:', error);
        toast.error(`Search failed: ${error.message || 'Unknown error'}`);
      });
  };

  const isDisabled = isLoading || !hasAnyChartData() || !fromValue || !toValue;

  return (
    <Button variant='default' size='icon' onClick={handleClick} disabled={isDisabled}>
      {isLoading ? (
        <div className='relative flex items-center justify-center'>
          <Search className='h-4 w-4 opacity-50' />
        </div>
      ) : (
        <Search className='h-4 w-4' />
      )}
    </Button>
  );
}
