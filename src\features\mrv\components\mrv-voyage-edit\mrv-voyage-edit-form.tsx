import { FormProvider, useForm } from 'react-hook-form';
import { useMrvStore } from '@/features/mrv/mrv.store';
import { MrvVoyagePutDto } from '@/features/mrv/mrv.types';
import { DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { Separator } from '@/components/ui/separator';
import { MrvVoyageFieldPort } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-port';
import { MrvVoyageFieldDistance } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-distance';
import { MrvVoyageFieldType } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-type';
import { MrvVoyageFieldTime } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-time';
import { MrvVoyageFieldCargo } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-cargo';
import { MrvVoyageFieldTransportWork } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-transport-work';
import { MrvVoyageFieldFuel } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-fuel';
import { MrvVoyageFieldApprove } from '@/features/mrv/components/mrv-voyage-fields/mrv-voyage-field-approve';
import { useMrvVoyageEditStore } from '@/features/mrv/components/mrv-voyage-edit/mrv-voyage-edit.store';
import { formatToServerDateTime } from '@/lib/date-utils';

interface MrvVoyageEditFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function MrvVoyageEditForm({ onSuccess, onCancel }: MrvVoyageEditFormProps) {
  // Store
  const { updateVoyage } = useMrvStore();
  const { voyage } = useMrvVoyageEditStore();

  // Form
  const form = useForm<MrvVoyagePutDto>({
    mode: 'onChange',
    defaultValues: {
      voyage_id: voyage?.voyage_id,
      departure_port: voyage?.departure_port,
      destination_port: voyage?.destination_port,
      distance: voyage?.distance,
      eu_heading: voyage?.eu_heading,
      start_time: voyage?.start_time ? new Date(voyage.start_time).toISOString() : '',
      end_time: voyage?.end_time ? new Date(voyage.end_time).toISOString() : '',
      cargo: voyage?.cargo,
      transport_work: voyage?.transport_work,
      fuel_consumption_MDO: voyage?.fuel_consumption_MDO,
      fuel_consumption_LFO: voyage?.fuel_consumption_LFO,
      fuel_consumption_HFO: voyage?.fuel_consumption_HFO,
      approved_mrv_voyage: voyage?.approved_mrv_voyage,
    },
  });

  // Render if no voyage
  if (!voyage) return null;

  // Submit
  const onSubmit = async (data: MrvVoyagePutDto) => {
    const body = {
      ...data,
      start_time: formatToServerDateTime(new Date(data.start_time)),
      end_time: formatToServerDateTime(new Date(data.end_time)),
    };
    await updateVoyage(data.voyage_id, body);
    onSuccess();
    form.reset();
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Edit Voyage</DialogTitle>
        <DialogDescription>Fill out the form to edit the voyage.</DialogDescription>
      </DialogHeader>
      <Separator />

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          {/* Routing */}
          <section className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <MrvVoyageFieldPort name='departure_port' label='Departure Port' control={form.control} />
            <MrvVoyageFieldPort name='destination_port' label='Destination Port' control={form.control} />
            <MrvVoyageFieldDistance name='distance' label='Distance' control={form.control} />
            <MrvVoyageFieldType name='eu_heading' label='Voyage Type' control={form.control} />
          </section>

          {/* Timing */}
          <section className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormDateTimePicker name='start_time' label='Departure Time' control={form.control} />
            <FormDateTimePicker name='end_time' label='Arrival Time' control={form.control} />
            <MrvVoyageFieldTime fromDate={new Date(form.watch('start_time'))} toDate={new Date(form.watch('end_time'))} />
          </section>

          {/* Cargo & Transport */}
          <section className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <MrvVoyageFieldCargo name='cargo' label='Cargo' control={form.control} />
            <MrvVoyageFieldTransportWork name='transport_work' label='Transport Work' control={form.control} />
          </section>

          {/* Fuel */}
          <section className='rounded-lg border p-4'>
            <MrvVoyageFieldFuel control={form.control} />
          </section>
          <Separator />

          {/* Footer */}
          <DialogFooter>
            <div className='mr-auto'>
              <MrvVoyageFieldApprove name='approved_mrv_voyage' control={form.control} />
            </div>
            <Button variant='outline' type='button' onClick={onCancel}>
              Cancel
            </Button>
            <Button type='submit' disabled={form.formState.isSubmitting}>
              Submit
            </Button>
          </DialogFooter>
        </form>
      </FormProvider>
    </>
  );
}
