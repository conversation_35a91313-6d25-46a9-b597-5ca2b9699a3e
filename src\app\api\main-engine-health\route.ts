import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { MainEngineHealthResponse } from '@/features/main-engine-health/main-engine-health.types';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/engine-health', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<MainEngineHealthResponse>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
