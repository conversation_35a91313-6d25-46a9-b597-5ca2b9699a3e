import { create } from 'zustand';
import { DataAnalyticsResponse } from './data-analytics.types';
import axios from 'axios';
import { useVesselStore } from '../vessels/vessel.store';
import { toast } from 'sonner';

interface DataAnalyticsStore {
  dataAnalytics: DataAnalyticsResponse | null;
  fromDate: string;
  toDate: string;
  isLoading: boolean;
  fetchDataAnalytics: (fromDate: string, toDate: string) => Promise<void>;
}

export const useDataAnalyticsStore = create<DataAnalyticsStore>((set) => ({
  dataAnalytics: null,
  isLoading: false,
  fromDate: '',
  toDate: '',

  fetchDataAnalytics: async (fromDate: string, toDate: string) => {
    set({ isLoading: true, dataAnalytics: null });

    const { currentVessel } = useVesselStore.getState();

    if (!currentVessel) {
      toast.error('No vessel selected.');
      set({ isLoading: false });
      return;
    }

    const { imo, id, vessel_name, assigned_owner_vat } = currentVessel;

    if (!imo || !id || !vessel_name || !assigned_owner_vat) {
      toast.error('Missing required vessel information.');
      set({ isLoading: false });
      return;
    }

    try {
      const params = {
        vessel_imo: imo.toString(),
        vessel_id: id.toString(),
        vessel_name,
        owner_vat: assigned_owner_vat,
        from_date: fromDate,
        to_date: toDate,
      };

      const res = await axios.get<DataAnalyticsResponse>(`/api/data-analytics?${new URLSearchParams(params).toString()}`);

      set({ dataAnalytics: res.data, fromDate, toDate });
    } catch {
      toast.error('Failed to fetch data analytics.');
    } finally {
      set({ isLoading: false });
    }
  },
}));
