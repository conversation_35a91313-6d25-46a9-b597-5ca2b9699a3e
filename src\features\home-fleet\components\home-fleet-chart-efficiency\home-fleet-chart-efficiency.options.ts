import Highcharts from 'highcharts';
import { colorMap } from './home-fleet-chart-efficiency.constants';
import { Series } from './home-fleet-chart-efficiency.types';

export function getChartOptions(chartData: Series): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      type: 'column',
      name: 'SFOC (g/kWh)',
      data: chartData.sfoc,
      color: colorMap.sfoc,
      yAxis: 0,
    },
    {
      type: 'column',
      name: 'Propulsion Efficiency (kg/nm)',
      data: chartData.propulsionEfficiency,
      color: colorMap.efficiency,
      yAxis: 1,
    },
    {
      type: 'column',
      name: 'Average Speed (kn)',
      data: chartData.sfocSpeed,
      color: colorMap.speed,
      yAxis: 2,
    },
  ];

  return {
    chart: { type: 'column' },
    title: { text: '' },
    xAxis: {
      categories: chartData.vesselNames,
      labels: { style: { color: 'black' } },
      crosshair: true,
    },
    yAxis: [
      {
        min: 0,
        title: {
          text: 'SFOC (g/kWh)',
          style: { color: colorMap.sfoc },
        },
        labels: {
          style: { color: colorMap.sfoc },
        },
      },
      {
        title: {
          text: 'Propulsion Efficiency (kg/nm)',
          style: { color: colorMap.efficiency },
        },
        labels: {
          style: { color: colorMap.efficiency },
        },
        opposite: true,
      },
      {
        title: {
          text: 'Average Speed (kn)',
          style: { color: colorMap.speed },
        },
        labels: {
          style: { color: colorMap.speed },
        },
        opposite: true,
      },
    ],
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltipBar(this, chartData);
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    series,
  };
}

export function formatTooltipBar(
  ctx: {
    x: number;
    points: {
      color: string;
      series: { name: string };
      y: number;
      index: number;
    }[];
  },
  chartData: Series
): string {
  const index = ctx?.points?.[0]?.index ?? 0;
  const vesselName = chartData.vesselNames[index];
  const sfoc = chartData.sfoc[index];
  const propulsionEff = chartData.propulsionEfficiency[index];
  const speed = chartData.sfocSpeed[index];
  const sfocTime = chartData.sfocHours[index] ?? 'N/A';
  const propulsionTime = chartData.propulsionHours[index] ?? 'N/A';

  let tooltip = `<div style="font-family: Arial, sans-serif; font-size: 13px; color: #222;">`;

  tooltip += `<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">${vesselName}</div>`;
  tooltip += `<hr style="margin:8px 0; border-color: #ddd;" />`;
  tooltip += `<table style="font-size: 12px; color: #555; width: 100%;">`;
  tooltip += `<tbody>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">SFOC:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.sfoc};">${sfoc} g/kWh</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Propulsion Efficiency:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.efficiency};">${propulsionEff} kg/nm</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Average Speed:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.speed};">${speed.toFixed(2)} kn</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Accumulated Time (SFOC):</td><td style="padding: 2px 4px; color: ${colorMap.sfoc};">${sfocTime}</td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Accumulated Time (Propulsion):</td><td style="padding: 2px 4px; color: ${colorMap.efficiency};">${propulsionTime}</td></tr>`;
  tooltip += `</tbody>`;
  tooltip += `</table>`;
  tooltip += `</div>`;

  return tooltip;
}
