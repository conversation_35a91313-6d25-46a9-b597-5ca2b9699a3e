export type VoyageStatus = 'Complete' | 'Incomplete' | 'Error' | 'Approved';

export type VoyageGetDTO = {
  id?: number; // We'll generate this from the UUID
  uuid: string;
  aft_draft?: number;
  arrival_count?: number;
  arrival_location?: string;
  aux_consumption?: number;
  boiler_consumption?: number;
  cargo_type: string | null;
  departure_location: string;
  departure_port: string;
  description: string | null;
  destination_port: string;
  distance: number;
  emission_percentage_for_mrv?: number;
  end_sailing_event?: number;
  end_time: string;
  errors: string[];
  eu_heading?: string;
  eu_mrv_count?: number;
  forward_draft?: number;
  fuel_consumption: number;
  fuel_consumption_HFO: number;
  fuel_consumption_LFO: number;
  fuel_consumption_MDO: number;
  fuel_consumption_at_berth: number;
  fuel_consumption_at_sea: number;
  fuel_types: string[];
  invalid_keys?: string[];
  mrv_fuel_consumption: number;
  navigation_statuses?: Record<string, string>;
  ongoing: boolean;
  start_sailing_event?: number;
  start_time: string;
  time_at_berth_days: number;
  time_at_berth_hours: number;
  time_at_berth_minutes: number;
  time_at_sea_days: number;
  time_at_sea_hours: number;
  time_at_sea_minutes: number;
  time_in_port_days?: number | null;
  time_in_port_hours?: number | null;
  time_in_port_minutes?: number | null;
  time_sailed_days: number;
  time_sailed_hours: number;
  time_sailed_minutes: number;
  transport_work?: number | null;
  uk_heading?: string | null;
  uk_mrv_count?: number;
  voyage_id?: string;
  voyage_status: VoyageStatus;
  voyage_type?: string | null;
  approved_mrv_voyage: boolean;

  // Additional fields for form compatibility
  cargo?: number; // Cargo amount in tonnes
  emissions?: number; // Total emissions
  emissions_hfo?: number; // Emissions for HFO
  emissions_lfo?: number; // Emissions for LFO
  emissions_mdo?: number; // Emissions for MDO
  emissions_mgo?: number; // Emissions for MGO
  fuel_consumption_hfo?: number; // Lowercase version for form
  fuel_consumption_lfo?: number; // Lowercase version for form
  fuel_consumption_mdo?: number; // Lowercase version for form
  fuel_consumption_mgo?: number; // Lowercase version for form
  voyage_number?: number;
  approved_mrv?: boolean; // Used in form instead of approved_mrv_voyage
};

export type VoyagePostDTO = Omit<VoyageGetDTO, 'id' | 'uuid'>;

export type VoyagePutDTO = Partial<VoyagePostDTO>;
