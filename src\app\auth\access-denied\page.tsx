'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { signOut } from 'next-auth/react';

export default function AccessDeniedPage() {
  return (
    <div className='grid h-dvh place-items-center bg-white px-6 py-24 sm:py-32 lg:px-8'>
      <div className='text-center'>
        <h1 className='mt-4 text-5xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl'>Access Denied</h1>
        <p className='mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8'>
          Your account does not have access to our platform. Please contact our customer support request access.
        </p>
        <div className='mt-6 flex items-center justify-center gap-x-6'>
          <Button
            variant='outline'
            type='button'
            className='bg-primary cursor-pointer text-white'
            onClick={() => signOut({ callbackUrl: '/auth/login' })}
          >
            <ChevronLeft />
            <div className='pr-0.5'>Sign in with another account</div>
          </Button>
        </div>
      </div>
    </div>
  );
}
