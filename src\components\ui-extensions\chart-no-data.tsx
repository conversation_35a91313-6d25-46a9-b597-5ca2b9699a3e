'use client';

import React from 'react';
import clsx from 'clsx';

export interface NoDataPlaceholderProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  propertiesText?: string;
  className?: string;
}

const sizeMap = {
  sm: { iconSize: 'w-12 h-12', barHeights: [0.2, 0.4, 0.6, 0.8], fontSize: 'text-sm' },
  md: { iconSize: 'w-24 h-24', barHeights: [0.2, 0.4, 0.6, 0.8], fontSize: 'text-lg' },
  lg: { iconSize: 'w-32 h-32', barHeights: [0.2, 0.45, 0.7, 1], fontSize: 'text-xl' },
};

export const NoDataPlaceholder: React.FC<NoDataPlaceholderProps> = ({
  size = 'md',
  message = 'No Data Series Enabled',
  propertiesText,
  className,
}) => {
  const { iconSize, barHeights, fontSize } = sizeMap[size];

  return (
    <div className={clsx('absolute top-[50px] left-0 flex h-[calc(100%-50px)] w-full items-center justify-center', className)}>
      <div className='text-muted-foreground flex flex-col items-center'>
        <div className={clsx('relative mb-4', iconSize)}>
          {barHeights.map((height, index) => (
            <div
              key={index}
              className='bg-muted absolute bottom-0 rounded-sm opacity-90'
              style={{
                left: `${index * 25}%`,
                height: `${height * 100}%`,
                width: '15%',
              }}
            />
          ))}
        </div>
        <p className={clsx('text-center', fontSize)}>{message}</p>
        {propertiesText && <p className='mt-1 text-center text-xs'>{propertiesText}</p>}
      </div>
    </div>
  );
};
