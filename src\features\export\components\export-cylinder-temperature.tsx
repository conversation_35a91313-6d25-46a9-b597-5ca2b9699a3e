'use client';

import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { Button } from '@/components/ui/button';
import { Thermometer } from 'lucide-react';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import axios from 'axios';
import { DotLoader } from '@/components/ui-extensions/dot-loader';
import { useForm } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { formatDateTime } from '../utils/date-formatter';

interface CylinderTemperatureDateRange {
  from_date: string | null;
  to_date: string | null;
}

interface ExportCylinderTemperatureFormData {
  fromDate: Date;
  toDate: Date;
}

export default function ExportCylinderTemperature() {
  const { currentVessel } = useVesselStore();
  const [availableDateRange, setAvailableDateRange] = useState<CylinderTemperatureDateRange | null>(null);
  const [isLoadingDateRange, setIsLoadingDateRange] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);

  const form = useForm<ExportCylinderTemperatureFormData>({
    defaultValues: {
      fromDate: new Date(new Date().setDate(new Date().getDate() - 7)), // 7 days ago
      toDate: new Date(),
    },
  });

  // Fetch available date range when component mounts or vessel changes
  useEffect(() => {
    const fetchDateRange = async () => {
      if (!currentVessel?.imo || !currentVessel?.assigned_owner_vat) {
        setIsDisabled(true);
        return;
      }

      setIsLoadingDateRange(true);
      try {
        const { data } = await axios.get<CylinderTemperatureDateRange>('/api/cylinder-temperature/date-range', {
          params: {
            vessel_imo: currentVessel.imo,
            owner_vat: currentVessel.assigned_owner_vat,
          },
        });

        setAvailableDateRange(data);
        setIsDisabled(false);

        // Set initial date range to the available range
        if (data.from_date && data.to_date) {
          form.setValue('fromDate', new Date(data.from_date));
          form.setValue('toDate', new Date(data.to_date));
        }
      } catch (error) {
        console.error('Error fetching cylinder temperature date range:', error);
        setIsDisabled(true);
        setAvailableDateRange(null);
        toast.error('No cylinder temperature data available for this vessel');
      } finally {
        setIsLoadingDateRange(false);
      }
    };

    fetchDateRange();
  }, [currentVessel?.imo, currentVessel?.assigned_owner_vat]);

  const handleExport = async (data: ExportCylinderTemperatureFormData) => {
    if (!currentVessel?.imo || !currentVessel?.assigned_owner_vat || !data.fromDate || !data.toDate) {
      toast.error('Please select a valid date range');
      return;
    }

    setIsExporting(true);
    try {
      // Format dates for API call using the new format
      const fromDate = String(formatDateTime(data.fromDate));
      const toDate = String(formatDateTime(data.toDate));

      // Create download URL - using date-only format for this specific API
      const downloadUrl = `/api/cylinder-temperature/download?vessel_imo=${currentVessel.imo}&owner_vat=${encodeURIComponent(
        currentVessel.assigned_owner_vat
      )}&from_date=${fromDate}&to_date=${toDate}`;

      // Fetch the JSON file
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error('Failed to download cylinder temperature data');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Format dates for filename
      const fromDateOnly = data.fromDate.toISOString().split('T')[0];
      const toDateOnly = data.toDate.toISOString().split('T')[0];

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `Cylinder-Temperature-Data-${currentVessel.vessel_name || 'vessel'}-${fromDateOnly}-to-${toDateOnly}.json`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Cylinder temperature data downloaded successfully');
    } catch (error) {
      console.error('Error exporting cylinder temperature data:', error);
      toast.error('Failed to export cylinder temperature data');
    } finally {
      setIsExporting(false);
    }
  };

  const minDate = availableDateRange?.from_date ? new Date(availableDateRange.from_date) : undefined;
  const maxDate = availableDateRange?.to_date ? new Date(availableDateRange.to_date) : undefined;

  // Show dot loader while loading date range
  if (isLoadingDateRange) {
    return (
      <div className='bg-card text-card-foreground flex min-h-[120px] items-center justify-center rounded-md border p-4'>
        <DotLoader />
      </div>
    );
  }

  return (
    <div className={`bg-card text-card-foreground rounded-md border p-4 ${isDisabled ? 'opacity-50' : ''}`}>
      <div className='mb-2 flex items-center gap-2'>
        <div className='text-lg font-semibold tracking-tight'>Cylinder Temperature</div>
        <Thermometer className='h-4 w-4' />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleExport)} className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormDateTimePicker
              control={form.control}
              name='fromDate'
              label='From'
              disabled={isDisabled}
              fromDate={minDate}
              toDate={maxDate}
            />
            <FormDateTimePicker control={form.control} name='toDate' label='To' disabled={isDisabled} fromDate={minDate} toDate={maxDate} />
          </div>
          <div className='flex justify-end'>
            <Button type='submit' disabled={isDisabled || isExporting}>
              {isExporting ? <>Exporting...</> : <>Export</>}
            </Button>
          </div>
        </form>
      </Form>
      {isDisabled && (
        <div className='text-muted-foreground mt-2 text-sm'>No cylinder temperature data available for the selected vessel</div>
      )}
    </div>
  );
}
