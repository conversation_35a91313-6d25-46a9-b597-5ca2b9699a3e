import axios from 'axios';
import { create } from 'zustand';
import { useVesselStore } from '../vessels/vessel.store';
import { VesselEfficiencyGetDTO, VesselEfficiencyPostDTO } from './home-vessel-efficiency.types';
import { toast } from 'sonner';
import { HomeVesselResponse } from './home-vessel.types';

interface HomeVesselStore {
  vesselData: HomeVesselResponse | null;
  isLoadingVesselData: boolean;
  vesselEfficiency: VesselEfficiencyGetDTO | null;
  isLoadingVesselEfficiency: boolean;
  fetchVesselData: () => Promise<void>;
  fetchVesselEfficiency: (params: VesselEfficiencyPostDTO) => Promise<void>;
}

export const useHomeVesselStore = create<HomeVesselStore>((set) => ({
  vesselData: null,
  isLoadingVesselData: false,
  vesselEfficiency: null,
  isLoadingVesselEfficiency: false,

  fetchVesselData: async () => {
    set({ isLoadingVesselData: true, vesselData: null });
    const { currentVessel } = useVesselStore.getState();

    const params = {
      vessel_imo: currentVessel!.imo.toString(),
      owner_vat: currentVessel!.assigned_owner_vat,
      from_date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] + ' 00:00:00',
      to_date: new Date().toISOString().split('T')[0] + ' 23:59:59',
      vessel_id: currentVessel!.id.toString(),
      vessel_name: currentVessel!.vessel_name,
    };

    try {
      const res = await axios.get<HomeVesselResponse>(`/api/home?${new URLSearchParams(params).toString()}`);
      set({ vesselData: res.data });
    } catch (_error) {
      toast.error('Failed to fetch vessel data');
    } finally {
      set({ isLoadingVesselData: false });
    }
  },

  fetchVesselEfficiency: async (params: VesselEfficiencyPostDTO) => {
    set({ isLoadingVesselEfficiency: true, vesselEfficiency: null });
    try {
      const res = await axios.post<VesselEfficiencyGetDTO>(`/api/home/<USER>
      set({ vesselEfficiency: res.data });
    } catch (_error) {
      toast.error('Failed to fetch vessel efficiency');
    } finally {
      set({ isLoadingVesselEfficiency: false });
    }
  },
}));
