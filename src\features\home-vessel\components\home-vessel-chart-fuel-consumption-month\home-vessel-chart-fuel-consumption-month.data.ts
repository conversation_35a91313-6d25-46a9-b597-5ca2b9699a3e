import { MonthlyFuelReport } from '../../home-vessel.types';

export function useChartDataDaily(
  data: MonthlyFuelReport,
  type: 'main' | 'aux' | 'boiler',
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown'
): {
  dayLabels: string[];
  yValues: number[];
  beaufortValues: number[];
  speedValues: number[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  detailValues: any[];
} | null {
  if (!data) return null;

  const days = data.Days;
  const dayLabels: string[] = Object.keys(days);
  const beaufortValues: number[] = Object.values(days).map((value) => value.bf ?? 0);

  const speedValues =
    speedType === 'Log Speed'
      ? Object.values(days).map((value) => value.log_speed ?? 0)
      : speedType === 'GPS Speed'
        ? Object.values(days).map((value) => value.gps_speed ?? 0)
        : [];

  let yValues: number[];
  switch (type) {
    case 'main':
      yValues = Object.values(days).map((value) => value.ME?.Tons ?? 0);
      break;
    case 'aux':
      yValues = Object.values(days).map((value) => value.AUX?.Tons ?? 0);
      break;
    case 'boiler':
      yValues = Object.values(days).map((value) => value.Boiler?.Tons ?? 0);
      break;
    default:
      yValues = [];
  }

  return {
    dayLabels,
    yValues,
    beaufortValues,
    speedValues,
    detailValues: Object.values(days),
  };
}
