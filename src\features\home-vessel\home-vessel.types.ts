export interface HomeVesselResponse {
  available_dates: AvailableDate[];
  fleet_available_dates: AvailableDate[];
  latest_ais_data: LatestAisData;
  latest_row: Row[];
  latest_cylinder_data: LatestCylinderData;
  reports: Reports;
  measurements: Row[];
  summaryData: SummaryData;
  chartData: ChartData;
}

export interface AvailableDate {
  timestamp: string;
}

export interface LatestAisData {
  id: number;
  vessel: number;
  timestamp: string;
  last_update_utc: string;
  navigation_status: string;
  latitude: number;
  longitude: number;
  vessel_type: string;
  destination: string;
  dest_port_unlo: string;
  dest_port_name: string;
  dep_port_unlo: string;
  dep_port_name: string;
  atd_epoch: number;
  atd_utc: string;
  eta_epoch: number;
  eta_utc: string;
  draught: string;
  heading: number;
  speed: number;
}

export interface Row {
  id: number;
  vessel: number;
  timestamp: string;
  frugal_status: number;
  frugal_mode: number;
  frugal_alarms: number;
  draft_fwd: number;
  draft_aft: number;
  log_speed: number;
  gps_speed: number;
  fuel_density: number;
  fuel_temperature: number;
  fuel_type: string | null;
  latitude: number;
  longitude: number;
  heading: number;
  shaft_1_rpm: number;
  boiler_1_fc_mass: number;
  boiler_2_fc_mass: number;
  boiler_1_fc_volume: number;
  boiler_2_fc_volume: number;
  flow_totals: string;
  shaft_1_torque: number;
  shaft_1_thrust: number;
  shaft_1_power: number;
  prop_1_pitch: number;
  me_1_fc_mass: number;
  me_1_fc_volume: number;
  me_1_load: number;
  me_1_rpm: number;
  gyro_x: number;
  gyro_y: number;
  gyro_z: number;
  accel_x: number;
  accel_y: number;
  accel_z: number;
  thrust_cmd: number;
  rpm_cmd: number;
  pitch_cmd: number;
  state_regs: string;
  aux_1_fc_mass: number;
  aux_1_fc_volume: number;
  aux_2_fc_mass: number;
  aux_2_fc_volume: number;
  destination: string;
  eta: string;
  nav_status: string;
  consumption: string;
}

export interface LatestCylinderData {
  cylinder1A: number;
  cylinder2A: number;
  cylinder3A: number;
  cylinder4A: number;
  cylinder5A: number;
  cylinder6A: number;
  cylinder1B: number;
  cylinder2B: number;
  cylinder3B: number;
  cylinder4B: number;
  cylinder5B: number;
  cylinder6B: number;
  timestamp: string;
}

export interface Reports {
  efficiencies_daily: Efficiencies;
  efficiencies_monthly: Efficiencies;
  monthly_fuel_report: Record<string, MonthlyFuelReport>;
  monthly_fuel_report_me_data: { data: number[]; name: string }[];
  monthly_fuel_report_aux_data: { data: number[]; name: string }[];
  monthly_fuel_report_boiler_data: { data: number[]; name: string }[];
  months_to_use: string[];
}

export interface Efficiencies {
  fuel_efficiency: Record<
    string,
    {
      [key: string]: string | number;
      speed: number;
      time: string;
    }
  >[];
  propulsion_efficiency: Record<
    string,
    {
      [key: string]: string | number;
      speed: number;
      time: string;
    }
  >[];
  speed_type: 'log_speed' | 'gps_speed';
}

export interface MonthlyFuelReport {
  ME: {
    Tons: number;
  };
  AUX: {
    Tons: number;
  };
  Boiler: {
    Tons: number;
  };
  Days: Record<
    number,
    {
      AUX: { Tons: number };
      Boiler: { Tons: number };
      ME: { Tons: number };
      HoursSailed: string;
      PctData: number;
      bf: number;
      log_speed?: number;
      gps_speed?: number;
    }
  >;
  HoursSailed: string;
  PctData: number;
  bf: number;
  log_speed?: number;
  gps_speed?: number;
}

export interface SummaryData {
  percentage: number;
  latest_timestamp: string;
  status: string;
  vessel_eta: string;
  port_eta: string;
  gps_speed: number;
}

export interface ChartData {
  on_percentage: number;
  off_percentage: number;
  frugal_usage: string;
  total_hours: number[];
  on_hours: number[];
  off_hours: number[];
}
