'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { CalendarIcon, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Path, FieldValues, Control } from 'react-hook-form';
import { Input } from '@/components/ui/input';

type FormDateTimePickerProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label: string;
  fromDate?: Date;
  toDate?: Date;
  disabled?: boolean;
  readOnly?: boolean;
};

export function FormDateTimePicker<T extends FieldValues>({
  control,
  name,
  label,
  fromDate,
  toDate,
  disabled = false,
  readOnly = false,
}: FormDateTimePickerProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        // Extract hours, minutes, and seconds from the date value
        const hours = field.value ? new Date(field.value).getHours() : 0;
        const minutes = field.value ? new Date(field.value).getMinutes() : 0;
        const seconds = field.value ? new Date(field.value).getSeconds() : 0;

        // Function to update the time part of the date
        const updateTime = (newHours: number, newMinutes: number, newSeconds: number) => {
          if (!field.value) return;

          const date = new Date(field.value);
          date.setHours(newHours);
          date.setMinutes(newMinutes);
          date.setSeconds(newSeconds);
          field.onChange(date);
        };

        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormControl>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant={'outline'} className='w-full justify-start font-normal' disabled={disabled}>
                    <CalendarIcon className='mr-2 h-4 w-4' />
                    {field.value ? (
                      format(new Date(field.value), 'LLL dd, yyyy HH:mm:ss') // 'LLL' for short month name, 'HH:mm:ss' for 24-hour format with seconds
                    ) : (
                      <span>Pick a date and time</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-auto p-0' align='start'>
                  <div className='p-3'>
                    <Calendar
                      mode='single'
                      onSelect={(date) => {
                        if (date) {
                          // Preserve the current time when selecting a new date
                          const newDate = new Date(date);
                          if (field.value) {
                            const currentDate = new Date(field.value);
                            newDate.setHours(currentDate.getHours());
                            newDate.setMinutes(currentDate.getMinutes());
                            newDate.setSeconds(currentDate.getSeconds());
                          }
                          field.onChange(newDate);
                        }
                      }}
                      selected={field.value ? new Date(field.value) : undefined}
                      defaultMonth={field.value ? new Date(field.value) : new Date()}
                      initialFocus
                      fromDate={fromDate}
                      toDate={toDate}
                      disabled={disabled}
                    />
                    <div className='border-t px-3 py-2'>
                      <div className='flex items-center justify-between'>
                        <div className='text-sm font-medium'>Time</div>
                        <Clock className='text-muted-foreground h-4 w-4' />
                      </div>
                      <div className='mt-2 flex items-center gap-2'>
                        <div className='grid grid-cols-3 gap-2'>
                          <div>
                            <FormLabel className='text-xs'>Hours</FormLabel>
                            <Input
                              type='number'
                              min={0}
                              max={23}
                              value={hours}
                              onChange={(e) => {
                                const newHours = parseInt(e.target.value);
                                if (!isNaN(newHours) && newHours >= 0 && newHours <= 23) {
                                  updateTime(newHours, minutes, seconds);
                                }
                              }}
                              className='h-8'
                              disabled={disabled || readOnly}
                              readOnly={readOnly}
                            />
                          </div>
                          <div>
                            <FormLabel className='text-xs'>Minutes</FormLabel>
                            <Input
                              type='number'
                              min={0}
                              max={59}
                              value={minutes}
                              onChange={(e) => {
                                const newMinutes = parseInt(e.target.value);
                                if (!isNaN(newMinutes) && newMinutes >= 0 && newMinutes <= 59) {
                                  updateTime(hours, newMinutes, seconds);
                                }
                              }}
                              className='h-8'
                              disabled={disabled || readOnly}
                              readOnly={readOnly}
                            />
                          </div>
                          <div>
                            <FormLabel className='text-xs'>Seconds</FormLabel>
                            <Input
                              type='number'
                              min={0}
                              max={59}
                              value={seconds}
                              onChange={(e) => {
                                const newSeconds = parseInt(e.target.value);
                                if (!isNaN(newSeconds) && newSeconds >= 0 && newSeconds <= 59) {
                                  updateTime(hours, minutes, newSeconds);
                                }
                              }}
                              className='h-8'
                              disabled={disabled || readOnly}
                              readOnly={readOnly}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
