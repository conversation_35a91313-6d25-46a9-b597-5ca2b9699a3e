/* eslint-disable @typescript-eslint/no-explicit-any */
export type DataAnalyticsResponse = {
  reports: Record<string, Report>;
  data_analytics: DataAnalytics;
  data_send_date: string;
};

export type Report = {
  on_percent_fc: number;
  off_percent_fc: number;
  total_hours: number[];
  on_hours: number[];
  off_hours: number[];
  on_propulsion_fc: number;
  off_propulsion_fc: number;
  avg_speed_on: number;
  avg_speed_off: number;
};

export type DataAnalytics = {
  measurement_graphs: MeasurementGraph;
  lspdMefcm: LSPDMefcm;
  lspdMefcmBestFit: LSPDMefcmBestFit;
  frugalStatusTime: FrugalStatusTime[];
};

export type MeasurementGraph = {
  show_raw_data: boolean[];
  y_axis: YAxis;
  x_axis: XAxisData;
};

export type YAxis = {
  mefcm: YAxisData;
  merpm: YAxisData;
  spow: YAxisData;
  lspd: YAxisData;
  gspd: YAxisData;
  boifcm: YAxisData;
  auxfcm: YAxisData;
};

export type YAxisData = {
  calculated_data: number[];
  max_raw_y: number;
  max_calculated_y: number;
  raw_data?: number[];
};

export type XAxisData = {
  calculated_categories: string[];
  max_timestamp: string;
  min_timestamp: string;
  raw_categories: string[];
};

export type LSPDMefcm = {
  lspd: Record<number, any>;
  gspd: Record<number, any>;
  lspdMin: number;
  gspdMin?: number;
};

export type LSPDMefcmBestFit = {
  lspd: Record<number, any>;
  gspd: Record<number, any>;
};

export type FrugalStatusTime = {
  from: string;
  to: string;
  frugal_mode: number;
  total_time: string;
};
