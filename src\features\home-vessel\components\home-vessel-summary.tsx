'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useHomeVesselStore } from '../home-vessel.store';

export const HomeVesselSummary = () => {
  const { vesselData, isLoadingVesselData } = useHomeVesselStore();

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <h2 className='text-md mb-4 font-semibold tracking-tight'>Vessel Summary</h2>
      <div className='space-y-3'>
        <StatValue label='Frugal Propulsion Status' value={vesselData?.summaryData.status ?? 'N/A'} isLoading={isLoadingVesselData} />
        <StatValue label='GPS Speed' value={vesselData?.summaryData.gps_speed ?? 'N/A'} units='kn' isLoading={isLoadingVesselData} />
        <StatValue label='Navigation Status' value={vesselData?.latest_row[0].nav_status ?? 'N/A'} isLoading={isLoadingVesselData} />
        <StatValue
          label='Latest Timestamp (Frugal Systems)'
          value={vesselData?.summaryData.latest_timestamp ?? 'N/A'}
          isLoading={isLoadingVesselData}
          className='text-green-500'
        />
      </div>
    </div>
  );
};
