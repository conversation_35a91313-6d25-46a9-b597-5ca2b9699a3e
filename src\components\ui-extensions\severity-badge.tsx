import React from 'react';
import { Badge } from '@/components/ui/badge';

// Define types for possible severity options
export type Severity = 'error' | 'warning' | 'info' | 'success';

// Map severities to colors
const severityColors: Record<Severity, string> = {
  error: 'border-red-400 text-red-800 bg-red-50',
  warning: 'border-yellow-400 text-yellow-800 bg-yellow-50',
  info: 'border-blue-400 text-blue-800 bg-blue-50',
  success: 'border-green-400 text-green-800 bg-green-50',
};

interface SeverityBadgeProps {
  severity: Severity;
  children: React.ReactNode;
}

export const SeverityBadge: React.FC<SeverityBadgeProps> = ({ severity, children }) => {
  return (
    <Badge variant='outline' className={`${severityColors[severity]} rounded-md border px-3 py-1`}>
      {children}
    </Badge>
  );
};
