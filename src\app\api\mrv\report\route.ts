import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo is required'),
  owner_vat: z.string().nonempty('owner_vat is required'),
  vessel_name: z.string().nonempty('vessel_name is required'),
  year: z.string().nonempty('year is required'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/mrv/report', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.getBinary(request, url.toString());

    // Get the PDF data as an array buffer
    const pdfData = await res.arrayBuffer();

    // Build filename
    const filename = `${validatedQuery.data.year}-MRV-${validatedQuery.data.vessel_name}.pdf`;

    // Create a new response with the PDF data and appropriate headers
    return new NextResponse(pdfData, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    if (error instanceof HttpError) {
      const message = error.status === 403 ? 'None of the voyages are completed' : error.message;
      return NextResponse.json({ error: message }, { status: error.status });
    }

    return NextResponse.json({ error: 'Error fetching MRV report' }, { status: 500 });
  }
}
