import React from 'react';
import DataAnalyticsSearch from '@/features/data-analytics/components/data-analytics-search';
import { PageHeader } from '@/components/layout/page-header';
import { DataAnalyticsCardFuelConsumption } from '@/features/data-analytics/components/data-analytics-card-fuel-consumption';
import { DataAnalyticsCardHours } from '@/features/data-analytics/components/data-analytics-card-hours';
import { DataAnalyticsCardSpeed } from '@/features/data-analytics/components/data-analytics-card-speed';
import { DataAnalyticsCharts } from '@/features/data-analytics/components/data-analytics-charts';
import DataAnalyticsChartConSpeed from '@/features/data-analytics/components/data-analytics-chart-con-speed/data-analytics-chart-con-speed';
export default function DataAnalyticsPage() {
  return (
    <div className='grid grid-cols-1 gap-4'>
      <div className='flex items-center justify-between'>
        <PageHeader title='Data Analytics' />
        <DataAnalyticsSearch />
      </div>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
        <DataAnalyticsCardFuelConsumption />
        <DataAnalyticsCardHours />
        <DataAnalyticsCardSpeed />
      </div>
      <DataAnalyticsCharts />
      <DataAnalyticsChartConSpeed />
    </div>
  );
}
