'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { FilterIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export type MultiSelectOption = {
  label: string;
  value: string;
};

type MultiSelectPopoverProps = {
  options: MultiSelectOption[];
  selectedValues: string[];
  setSelectedValues: (values: string[]) => void;
  placeholder?: string;
  title?: string;
  className?: string;
  disabled?: boolean;
};

export function MultiSelectPopover({
  options,
  selectedValues,
  setSelectedValues,
  placeholder = 'Select items',
  title = 'Items',
  className,
  disabled,
}: MultiSelectPopoverProps) {
  const selectAllRef = React.useRef<HTMLButtonElement>(null);

  const allSelected = selectedValues.length === options.length;
  const isPartial = selectedValues.length > 0 && !allSelected;

  const toggleItem = (value: string) => {
    setSelectedValues(selectedValues.includes(value) ? selectedValues.filter((v) => v !== value) : [...selectedValues, value]);
  };

  const toggleSelectAll = () => {
    if (allSelected) {
      setSelectedValues([]);
    } else {
      setSelectedValues(options.map((o) => o.value));
    }
  };

  React.useEffect(() => {
    if (selectAllRef.current) {
      (selectAllRef.current as unknown as HTMLInputElement).indeterminate = isPartial;
    }
  }, [isPartial]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline' className={cn('flex items-center gap-2', className)} disabled={disabled}>
          <FilterIcon className='h-4 w-4' />
          <span className='text-sm'>{selectedValues.length > 0 ? `${selectedValues.length} selected` : placeholder}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-64 p-4'>
        <h4 className='mb-2 text-sm font-medium'>{title}</h4>
        <div className='mb-2 flex items-center gap-2'>
          <Checkbox id='select-all' ref={selectAllRef} checked={allSelected} onCheckedChange={toggleSelectAll} disabled={disabled} />
          <label htmlFor='select-all' className='text-sm font-medium'>
            Select All
          </label>
        </div>
        <div className='max-h-64 space-y-2 overflow-y-auto pr-1'>
          {options.map((opt) => (
            <div key={opt.value} className='flex items-center gap-2'>
              <Checkbox
                id={opt.value}
                checked={selectedValues.includes(opt.value)}
                onCheckedChange={() => toggleItem(opt.value)}
                disabled={disabled}
              />
              <label htmlFor={opt.value} className='text-sm'>
                {opt.label}
              </label>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
