export type AnomalyReport = {
  anomalies: AnomalyMetricsBySignal[];
};

export type AnomalyMetricsBySignal = {
  aux_1_fc_mass: SignalAnomalySummary;
  aux_2_fc_mass: SignalAnomalySummary;
  gps_speed: SignalAnomalySummary;
  log_speed: SignalAnomalySummary;
  me_1_fc_mass: SignalAnomalySummary;
  me_1_rpm: SignalAnomalySummary;
  shaft_1_power: SignalAnomalySummary;
  shaft_1_rpm: SignalAnomalySummary;
  last_sanity_timestamp: string;
};

export type SignalAnomalySummary = {
  data: Record<string, TimeSeriesStats>;
  name: string;
  overall_anomalies_count: number;
  overall_frozen_count: number;
  overall_max: number;
  overall_mean: number;
  overall_min: number;
  overall_outlier_count: number;
  overall_row_count: number;
};

export type TimeSeriesStats = {
  max_value: number;
  mean_value: number;
  min_value: number;
  records: TimestampRecord[];
  total_anomalies_count: number;
  total_frozen_count: number;
  total_outlier_count: number;
};

export type TimestampRecord = {
  // All possible sensor fields (optional since not all sensors will have all fields)
  aux_1_fc_mass?: number;
  aux_2_fc_mass?: number;
  gps_speed?: number;
  log_speed?: number;
  me_1_fc_mass?: number;
  me_1_rpm?: number;
  shaft_1_power?: number;
  shaft_1_rpm?: number;
  // Anomaly detection fields
  frozen_count: number;
  has_frozen: boolean;
  has_outlier: boolean;
  is_anomalies: boolean;
  outlier_count: number;
  timestamp: string;
};
