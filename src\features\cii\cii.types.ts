export interface CiiResponse {
  status: 'SUCCESS' | 'FAILURE';
  cii_summary: CiiSummary;
}

export interface CiiSummary {
  years: Record<string, YearData>;
}

export interface YearData {
  yearly: YearlyData;
  weekly: Record<string, WeeklyData>;
  manual_weekly: Record<string, WeeklyData>;
}

export interface YearlyData {
  fuel_consumption: Record<string, number>;
  emissions: number;
  distance: number;
  capacity: Capacity;
  cii: Cii;
}

export interface Capacity {
  type: string;
  capacity: number;
}

export interface Cii {
  year: number;
  required_cii: number;
  attained_cii: number;
  adjusted_boundaries: number[];
  cii_letter_rating: string;
  required_cii_letter_rating: string;
  cii_slope: string | boolean;
  projected_cii: ProjectedCii;
}

export interface ProjectedCii {
  next_year: string;
  deterioration_year: number;
}

export interface WeeklyData {
  fuel_consumption: Record<string, number>;
  frugal_status_percentage: FrugalStatus;
  emissions: number;
  start: string;
  end: string;
  week: number;
  time_diff: string;
  avg_speed: number;
  distance: number;
  time_sailed: string;
  missing_time: string;
  attained_cii_snapshot: number;
}

export interface FrugalStatus {
  on?: number;
  off?: number;
}

export interface CiiYearsResponse {
  cii_years: string[];
}

export interface CiiPutDto {
  fuel: {
    fuelType: string;
    fuelConsumption: string;
  }[];
  distance: string;
  selected_vessel: {
    vessel_name: string;
    id: number;
    imo: number;
    assigned_owner_vat: string;
  };
  selected_year: string;
  week_numb: string;
}
