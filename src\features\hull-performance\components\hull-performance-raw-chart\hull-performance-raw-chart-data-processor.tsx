'use client';

import { DataRaw, DataSearch } from '../../hull-performance.types';
import { COLORS, formatTimestamp } from './hull-performance-raw-chart-utils';

// Process data with gap detection
export const processDataWithGaps = (timestamps: string[], values: number[]) => {
  // Process timestamps to identify gaps in the time series
  const processedData = [];

  // Define a fixed gap threshold of 30 days in milliseconds
  // 30 days * 24 hours * 60 minutes * 60 seconds * 1000 milliseconds
  const gapThreshold = 30 * 24 * 60 * 60 * 1000;

  // Process the data with gap detection
  for (let i = 0; i < timestamps.length; i++) {
    const currentTime = formatTimestamp(timestamps[i]);
    const value = values[i];

    // Check if there's a gap between this point and the previous one
    if (i > 0) {
      const prevTime = formatTimestamp(timestamps[i - 1]);
      const timeDiff = currentTime - prevTime;

      // If the time difference is larger than our threshold (30 days), insert a null point to create a gap
      if (timeDiff > gapThreshold) {
        // Add a null point right after the previous point
        processedData.push({
          x: prevTime + 1,
          y: null,
          timestamp: timestamps[i - 1],
        });

        // Add another null point right before the current point
        processedData.push({
          x: currentTime - 1,
          y: null,
          timestamp: timestamps[i],
        });
      }
    }

    // Add the actual data point
    processedData.push({
      x: currentTime,
      y: value,
      timestamp: timestamps[i],
    });
  }

  return processedData;
};

// Generate series data based on selected speed
export const generateSeriesData = (chartData: DataRaw | DataSearch, selectedSpeed: number | 'All') => {
  const series = [];

  // Include the "All" series only when "All" is selected
  if (chartData.all && selectedSpeed === 'All') {
    const allData = chartData.all.all_timestamps.map((timestamp, index) => {
      return {
        x: formatTimestamp(timestamp),
        y: chartData.all.all_shaft_power[index],
        timestamp,
      };
    });

    // Create the scatter series first
    const scatterSeries = {
      name: 'All Data Points',
      type: 'scatter',
      data: allData,
      color: COLORS.blue,
      zIndex: 2,
      marker: {
        enabled: true,
        radius: 4,
        symbol: 'circle',
      },
      lineWidth: 0,
      enableMouseTracking: true,
      stickyTracking: false,
      tooltip: {
        headerFormat: '{point.x:%Y-%m-%d %H:%M}<br/>',
        pointFormat: '<span style="color:{series.color}">●</span> Shaft Power: <b>{point.y:.2f} kW</b>',
        useHTML: true,
        style: {
          fontSize: '12px',
          fontFamily: 'Arial, sans-serif',
        },
      },
    };

    // Add the scatter series first
    series.push(scatterSeries);

    // Store the trend line series to add it later
    let trendLineSeries = null;

    // Create trend line for "All" if available
    if (chartData.all.trend_line) {
      // Process the trend line data with gap detection
      const processedTrendData = processDataWithGaps(chartData.all.all_timestamps, chartData.all.trend_line);

      trendLineSeries = {
        name: 'All Trend Line',
        type: 'spline',
        data: processedTrendData,
        color: COLORS.yellow,
        dashStyle: 'ShortDash',
        marker: {
          enabled: false,
        },
        lineWidth: 2,
        connectNulls: false,
        gapUnit: 'value',
        gapSize: 1,
      };

      // We'll add this to the series array at the end
    }

    // Store the trend line to add it at the end
    if (trendLineSeries) {
      // Instead of using setTimeout, just add the trend line series at the end
      // This ensures it will be the last series in the chart
      series.push(trendLineSeries);
    }
  }

  // If a specific speed is selected (not "All"), add those series
  if (selectedSpeed !== 'All') {
    // Add the speed trendline from speed_trendlines if available
    // Try different formats of the speed value to find a match
    const speedKey = selectedSpeed.toString();
    const speedKeyWithDecimal = selectedSpeed.toFixed(1);

    let speedTrendlineData: { combined_resampled_timestamps: string[]; trend_line: number[] } | undefined;
    if (chartData.speed_trendlines) {
      if (chartData.speed_trendlines[speedKey]) {
        speedTrendlineData = chartData.speed_trendlines[speedKey];
      } else if (chartData.speed_trendlines[speedKeyWithDecimal]) {
        speedTrendlineData = chartData.speed_trendlines[speedKeyWithDecimal];
      }

      if (speedTrendlineData && speedTrendlineData.combined_resampled_timestamps && speedTrendlineData.trend_line) {
        // Add the overall trendline for this speed
        const trendData = speedTrendlineData; // Create a non-nullable reference

        // Process the trend line data with gap detection
        const processedTrendData = processDataWithGaps(trendData.combined_resampled_timestamps, trendData.trend_line);

        series.push({
          name: `Trend Line Shaft Power`,
          type: 'spline',
          data: processedTrendData,
          color: COLORS.yellow,
          dashStyle: 'ShortDash',
          marker: {
            enabled: false,
          },
          lineWidth: 2,
          zIndex: 3,
          connectNulls: false,
          gapUnit: 'value',
          gapSize: 1,
        });
      }
    }

    // Find all keys that match the selected speed
    const speedKeys = Object.keys(chartData).filter((key) => {
      const value = chartData[key];
      return 'speed' in value && value.speed === selectedSpeed;
    });

    // Add a series for each matching key
    speedKeys.forEach((key, keyIndex) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const speedData = chartData[key] as any;
      if (speedData.resampled_timestamps && speedData.resampled_shaft_power) {
        const seriesData = speedData.resampled_timestamps.map((timestamp: string, index: number) => {
          // Ensure all properties are properly set for tooltip display
          return {
            x: formatTimestamp(timestamp),
            y: speedData.resampled_shaft_power[index],
            timestamp,
            draft: speedData.draft,
            overconsumption_fuel: speedData.overconsumption_fuel,
            daily_rate_of_shaft_change: speedData.daily_rate_of_shaft_change,
          };
        });

        // Use Tailwind draft colors for different series
        // We have 5 draft colors (draft1 through draft5)
        const draftColorIndex = (keyIndex % 5) + 1; // 1-based index for draft colors (1-5)
        const color = COLORS[`draft${draftColorIndex}` as keyof typeof COLORS];

        series.push({
          name: `Draft ${speedData.draft} m`,
          type: 'scatter',
          data: seriesData,
          color,
          zIndex: 2,
          marker: {
            enabled: true,
            radius: 4,
            symbol: 'circle',
          },
          lineWidth: 0,
          enableMouseTracking: true,
          stickyTracking: false,
          tooltip: {
            headerFormat: '{point.x:%Y-%m-%d %H:%M}<br/>',
            pointFormat: '<span style="color:{series.color}">●</span> Shaft Power: <b>{point.y:.2f} kW</b>',
            useHTML: true,
            style: {
              fontSize: '12px',
              fontFamily: 'Arial, sans-serif',
            },
          },
        });

        // Add trend line if available
        if (speedData.trend_line) {
          // Process the trend line data with gap detection
          const processedTrendData = processDataWithGaps(speedData.resampled_timestamps, speedData.trend_line);

          series.push({
            name: `Draft ${speedData.draft} Trend`,
            type: 'spline',
            data: processedTrendData,
            color, // Use the same color as the scatter points for consistency
            dashStyle: 'ShortDash',
            marker: {
              enabled: false,
            },
            lineWidth: 2,
            connectNulls: false,
            gapUnit: 'value',
            gapSize: 1,
          });
        }
      }
    });
  }

  return series;
};
