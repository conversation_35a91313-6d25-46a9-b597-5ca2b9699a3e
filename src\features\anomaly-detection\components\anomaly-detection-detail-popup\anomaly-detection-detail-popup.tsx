'use client';

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { TimeSeriesStats } from '../../anomaly-detection.types';
import { usePopupChartData } from './anomaly-detection-detail-popup.data';
import { getPopupChartOptions } from './anomaly-detection-detail-popup.options';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

interface AnomalyDetectionDetailPopupProps {
  isOpen: boolean;
  onClose: () => void;
  dayData: { date: string; data: TimeSeriesStats; sensor: string } | null;
}

export default function AnomalyDetectionDetailPopup({ isOpen, onClose, dayData }: AnomalyDetectionDetailPopupProps) {
  const chartData = usePopupChartData(dayData);

  const options = chartData ? getPopupChartOptions(chartData.sensor, chartData.xAxis, chartData.yAxis, chartData.records) : undefined;

  if (!dayData || !chartData) {
    return null;
  }

  const formattedDate = new Date(dayData.date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogTitle></DialogTitle>
      <DialogContent className='max-h-[80vh] w-[75vw] max-w-[75vw] overflow-y-auto sm:max-w-[75vw]'>
        {/* Chart */}
        <div>
          <div className='text-lg font-semibold'>Anomalies - {formattedDate}</div>
          <div className='text-sm text-gray-600'>{chartData.anomalyCount} anomalies</div>
        </div>
        {options ? (
          <div style={{ minHeight: '300px' }}>
            <HighchartsReact highcharts={Highcharts} options={options} />
          </div>
        ) : (
          <div className='flex h-48 items-center justify-center text-gray-500'>
            <div className='text-center'>
              <p>No chart data available</p>
              <p className='text-sm'>Records: {chartData.records.length}</p>
              <p className='text-sm'>Data points: {chartData.yAxis.length}</p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
