import { NextAuthOptions, Session, User } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import GoogleProvider from 'next-auth/providers/google';

interface Owner {
  owner_vat: string;
  role: number;
}

interface VerifiedUser {
  user_id: number;
  email: string;
  name: string;
  owners: Owner[];
  is_verified: boolean;
}

interface ExtendedUser extends User {
  user_id: number;
  owners: Owner[];
}

interface ExtendedToken extends JWT {
  user: ExtendedUser;
  current_owner: Owner;
}

export interface ExtendedSession extends Session {
  user: ExtendedUser;
  current_owner: Owner;
}

export const authConfig: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          prompt: 'select_account',
        },
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async signIn({ user }: { user: User }) {
      try {
        const verifiedUser = await verifyUser(user.email as string);

        if (verifiedUser && verifiedUser.email === user.email) {
          const extendedUser = user as ExtendedUser;
          extendedUser.user_id = verifiedUser.user_id;
          extendedUser.owners = verifiedUser.owners;

          return true;
        }

        return '/auth/access-denied';
      } catch (error) {
        console.error('Verification error:', error);
        return '/auth/access-denied';
      }
    },

    async jwt({ token, user, trigger, session }) {
      const extendedToken = token as ExtendedToken;

      if (user) {
        const extendedUser = user as ExtendedUser;
        extendedToken.user = extendedUser;
        extendedToken.current_owner = extendedUser.owners?.[0];
      }
      if (trigger === 'update' && session) {
        const match = extendedToken.user.owners?.find((owner) => owner.owner_vat === session.vat);

        if (match) {
          extendedToken.current_owner = match;
        }
      }

      return extendedToken;
    },

    async session({ session, token }) {
      const extendedToken = token as ExtendedToken;
      const extendedSession = session as ExtendedSession;

      extendedSession.user = extendedToken.user;
      extendedSession.current_owner = extendedToken.current_owner;

      return extendedSession;
    },
  },
};

async function verifyUser(email: string): Promise<VerifiedUser> {
  const res = await fetch(`${process.env.API_URL}/auth/verify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': process.env.INTERNAL_API_TOKEN || '',
    },
    body: JSON.stringify({ email }),
  });

  if (!res.ok) {
    throw new Error(`Failed to verify user: ${res.statusText}`);
  }
  const data = await res.json();
  return data;
}
