'use client';

import { useUserStore } from '../user.store';
import { PageSizeSelector } from '@/components/ui-extensions/page-size-selector';
import { PaginationControls } from '@/components/ui-extensions/pagination-controls';

export function UserTablePagination() {
  const { filteredUsers, pageSize, pageIndex, setPageSize, setPageIndex, isLoading } = useUserStore();

  return (
    <div className='flex justify-end gap-4'>
      <PageSizeSelector
        pageSize={pageSize}
        onPageSizeChange={(size) => {
          setPageSize(size);
          setPageIndex(0);
        }}
        pageSizeOptions={[5, 10, 25, 50]}
        disabled={isLoading}
      />
      <PaginationControls
        pageIndex={pageIndex}
        pageCount={Math.ceil(filteredUsers.length / pageSize)}
        onPreviousPage={() => setPageIndex(Math.max(pageIndex - 1, 0))}
        onNextPage={() => setPageIndex(Math.min(pageIndex + 1, Math.ceil(filteredUsers.length / pageSize) - 1))}
      />
    </div>
  );
}
