'use client';

import * as React from 'react';
import { SortingState, useReactTable, getCoreRowModel, getSortedRowModel, flexRender } from '@tanstack/react-table';
import { useVoyageStore } from '../voyage.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { columns } from './voyage-table-columns';
import { VoyageTablePagination } from './voyage-table-pagination';
import BouncingBarLoader from '@/components/ui-extensions/bouncing-bar-loader';

export function VoyageTable() {
  const { filteredVoyages, pageSize, pageIndex, loadVoyages, isLoading, year, loadYears } = useVoyageStore();
  const { currentVessel } = useVesselStore();

  const [sorting, setSorting] = React.useState<SortingState>([]);

  React.useEffect(() => {
    if (currentVessel?.imo && !year) {
      loadYears();
    } else if (currentVessel?.imo && year) {
      loadVoyages();
    } else {
      return;
    }
  }, [year, currentVessel?.imo]);

  const table = useReactTable({
    data: filteredVoyages,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    state: { sorting, pagination: { pageIndex, pageSize } },
  });

  const sortedRows = table.getSortedRowModel().rows;
  const paginatedData = React.useMemo(() => {
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    return sortedRows.slice(start, end);
  }, [sortedRows, pageIndex, pageSize]);

  return (
    <div className='flex flex-col gap-2'>
      <div className='relative rounded-md border'>
        {isLoading && <BouncingBarLoader absolute={true} className='z-1' />}
        <Table>
          <TableHeader className='bg-muted'>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className='text-muted-foreground'>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row) => (
                <TableRow key={row.original.uuid}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  {isLoading ? 'Loading...' : 'No results.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell colSpan={columns.length} className='text-muted-foreground text-sm'>
                Displayed voyages: {paginatedData.length} of {filteredVoyages.length}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>

      <VoyageTablePagination />
    </div>
  );
}
