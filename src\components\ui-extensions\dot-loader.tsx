'use client';

import React from 'react';
import { cn } from '@/lib/utils';

type DotLoaderProps = {
  size?: string;
  className?: string;
};

export function DotLoader({ size = '0.5rem', className }: DotLoaderProps) {
  return (
    <div className={cn('flex items-center justify-center space-x-2', className)}>
      <span className='dot animate-bounce' style={{ animationDelay: '0ms' }}></span>
      <span className='dot animate-bounce' style={{ animationDelay: '150ms' }}></span>
      <span className='dot animate-bounce' style={{ animationDelay: '300ms' }}></span>

      <style>{`
        .dot {
          width: ${size};
          height: ${size};
          background-color: var(--ring);
          border-radius: 50%;
          display: inline-block;
        }

        @keyframes bounce {
          0%,
          80%,
          100% {
            transform: scale(0);
            opacity: 0.3;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        .animate-bounce {
          animation: bounce 1.4s infinite ease-in-out both;
        }
      `}</style>
    </div>
  );
}
