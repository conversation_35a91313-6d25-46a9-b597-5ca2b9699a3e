export interface FleetVesselsResponse {
  active_alarms: ActiveAlarm[];
  fleet: number[];
  fleet_efficiency: FleetEfficiency[];
  fleet_fc_yearly_overview: FleetYearlyOverview;
  id: number;
  name: string;
  status: string;
  vat: string;
  vessels_fp_usage: VesselsFpUsage[];
}

export interface ActiveAlarm {
  alarm: number;
  name: string;
  timestamp: string;
  type: string;
  vessel: number;
}

export interface FleetEfficiency {
  [key: string]: {
    fuel: {
      efficiency?: number;
      hours_sailed?: string;
      speed?: number;
    };
    propulsion: {
      efficiency?: number;
      hours_sailed?: string;
      speed?: number;
    };
    vessel_imo: number;
  };
}
export interface FleetYearlyOverview {
  co2_emissions: FleetFuelConsumption;
  distance: number;
  fuel_consumption: FleetFuelConsumption;
}

export interface FleetFuelConsumption {
  aux: number;
  boiler: number;
  me: number;
  total: number;
}

export interface VesselsFpUsage {
  hours_off: number[];
  hours_on: number[];
  hours_total: number[];
  key: string;
  percent_off: number;
  vessel: string;
}
