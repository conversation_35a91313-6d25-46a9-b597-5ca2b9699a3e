'use client';

import Highcharts from 'highcharts';
import { DataAiObj } from '../../hull-performance.types';
import { COLORS, formatTooltipHTML, formatXAxisLabel } from './hull-performance-ai-chart-utils';
import { ProcessedChartData } from './hull-performance-ai-chart-data-processor';

export interface ChartConfigProps {
  dataAi: DataAiObj;
  processedData: ProcessedChartData;
  hasVisibleSeries: boolean;
  userDisabledSeries: boolean;
}

export const createChartOptions = ({ dataAi, processedData }: ChartConfigProps): Highcharts.Options => {
  const { realData, predictedRangeData, plotLines, plotBands } = processedData;

  // Calculate y-axis min and max based on both series data
  const calculateYAxisBounds = () => {
    const allValues: number[] = [];

    // Extract values from real data series (excluding null values)
    realData.forEach(([, value]) => {
      if (value !== null && typeof value === 'number' && !isNaN(value)) {
        allValues.push(value);
      }
    });

    // Extract values from predicted range data series (both lower and upper bounds, excluding null values)
    predictedRangeData.forEach(([, lower, upper]) => {
      if (lower !== null && typeof lower === 'number' && !isNaN(lower)) {
        allValues.push(lower);
      }
      if (upper !== null && typeof upper === 'number' && !isNaN(upper)) {
        allValues.push(upper);
      }
    });

    // If no valid values found, fallback to original logic
    if (allValues.length === 0) {
      return {
        min: 0,
        max: Math.max(...dataAi.upper_bounds) * 1.05,
      };
    }

    const minValue = Math.min(...allValues);
    const maxValue = Math.max(...allValues);

    // Add 5% padding to both min and max for better visualization
    const range = maxValue - minValue;
    const padding = range * 0.05;

    return {
      min: minValue - padding, // Allow negative values if they exist in the data
      max: maxValue + padding,
    };
  };

  const yAxisBounds = calculateYAxisBounds();

  return {
    chart: {
      height: 400,
      marginTop: 50, // Margin for annotations
      marginBottom: 75, // Increased margin for x-axis title and labels
      spacingBottom: 0, // Remove extra spacing at the bottom
      marginRight: 40, // Increased right margin to ensure last label is fully visible
      backgroundColor: 'transparent',
      zooming: {
        type: 'x', // Enable horizontal zooming
      },
    },
    title: {
      text: undefined, // No title
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
      buttons: {
        contextButton: {
          align: 'right',
          verticalAlign: 'top',
          y: -10,
          x: 12,
          menuItems: [
            'viewFullscreen',
            'printChart',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS',
          ],
        },
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
      x: -45, // Move legend further to the left to avoid overlapping with tools menu
      y: -10, // Keep on the same line as tools menu
      itemStyle: {
        fontWeight: 'normal',
      },
      floating: true, // Allow legend to float over the chart
    },
    xAxis: {
      type: 'datetime',
      title: {
        text: 'Week Number',
        style: {
          color: '#666',
          fontWeight: 'normal',
          fontSize: '14px',
        },
      },
      labels: {
        formatter: formatXAxisLabel,
        useHTML: true, // Enable HTML for colored year labels
        style: {
          color: '#000000',
          fontSize: '12px',
          fontWeight: 'normal',
        },
      },
      crosshair: true,
      plotLines: plotLines,
      plotBands: plotBands,
      events: {
        setExtremes: function (e: Highcharts.AxisSetExtremesEventObject) {
          // Prevent zooming in to less than twelve weeks (84 days)
          const minRangeMs = 84 * 24 * 60 * 60 * 1000; // 84 days in milliseconds

          if (e.min !== undefined && e.max !== undefined) {
            const currentRange = e.max - e.min;

            // If the user is trying to zoom in to less than 84 days, adjust the range
            if (currentRange < minRangeMs) {
              const center = (e.min + e.max) / 2;
              const halfMinRange = minRangeMs / 2;

              // Set new extremes that maintain the minimum range
              const newMin = center - halfMinRange;
              const newMax = center + halfMinRange;

              // Prevent the default zoom and set our custom range
              e.preventDefault?.();
              this.setExtremes(newMin, newMax, true, false);
              return false;
            }
          }
        },
      },
    },
    yAxis: {
      title: {
        text: 'Shaft Power (kW)',
      },
      min: yAxisBounds.min,
      max: yAxisBounds.max,
      gridLineColor: '#f5f5f5',
    },
    tooltip: {
      shared: true,
      useHTML: true,
      formatter: formatTooltipHTML,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 1,
      borderRadius: 5,
      shadow: true,
      style: {
        fontSize: '12px',
        fontFamily: 'Arial, sans-serif',
      },
    },
    // Remove annotations as we're using plotLines instead
    series: [
      {
        name: 'Predicted Shaft Power Range',
        type: 'arearange',
        data: predictedRangeData,
        color: COLORS.yellow,
        lineWidth: 1,
        marker: {
          enabled: false,
        },
        fillOpacity: 0.3,
        zIndex: 1,
        lineColor: COLORS.yellow,
        connectNulls: false,
        gapUnit: 'value',
        gapSize: 1,
        tooltip: {
          pointFormat: '<span style="color:{series.color}">●</span> {series.name}: <b>{point.low:.2f} - {point.high:.2f} kW</b><br/>',
        },
      },
      {
        name: 'Real Shaft Power',
        type: 'spline',
        data: realData,
        color: COLORS.blue,
        zIndex: 2,
        marker: {
          enabled: true,
          radius: 3,
          symbol: 'circle',
        },
        lineWidth: 2,
        connectNulls: false,
        gapUnit: 'value',
        gapSize: 1,
        tooltip: {
          pointFormat: '<span style="color:{series.color}">●</span> {series.name}: <b>{point.y:.2f} kW</b><br/>',
        },
      },
    ],
    plotOptions: {
      series: {
        animation: {
          duration: 1000,
        },
        marker: {
          enabled: false,
        },
        states: {
          hover: {
            lineWidthPlus: 0,
          },
        },
      },
      arearange: {
        marker: {
          enabled: false,
        },
        tooltip: {
          pointFormat: '<span style="color:{series.color}">●</span> {series.name}: <b>{point.low:.2f} - {point.high:.2f} kW</b><br/>',
        },
      },
      spline: {
        marker: {
          enabled: true,
          radius: 3,
          symbol: 'circle',
        },
        tooltip: {
          pointFormat: '<span style="color:{series.color}">●</span> {series.name}: <b>{point.y:.2f} kW</b><br/>',
        },
      },
    },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
              marginBottom: 75, // Match the main chart margin
              spacingBottom: 0, // Match the main chart spacing
              marginRight: 40, // Match the main chart right margin
            },
            legend: {
              layout: 'horizontal',
              align: 'center',
              verticalAlign: 'bottom',
              x: 0, // Reset x position for smaller screens
              y: 0, // Reset y position for smaller screens
              floating: false, // Don't float on smaller screens
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
              marginBottom: 75, // Match the main chart margin
              spacingBottom: 0, // Match the main chart spacing
              marginRight: 40, // Match the main chart right margin
            },
            legend: {
              layout: 'horizontal',
              align: 'center',
              verticalAlign: 'bottom',
              x: 0, // Reset x position for smaller screens
              y: 0, // Reset y position for smaller screens
              floating: false, // Don't float on smaller screens
            },
            yAxis: {
              title: {
                text: null,
              },
            },
          },
        },
      ],
    },
  };
};
