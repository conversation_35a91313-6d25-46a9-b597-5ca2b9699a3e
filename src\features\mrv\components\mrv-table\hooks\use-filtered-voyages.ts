import { useMemo } from 'react';
import { useMrvStore } from '@/features/mrv/mrv.store';
import { useMrvTableStore } from '../mrv-table.store';
import { MrvVoyage } from '@/features/mrv/mrv.types';

export function useFilteredVoyages(): MrvVoyage[] {
  // Store
  const { voyages } = useMrvStore();
  const { searchQuery, selectedTab } = useMrvTableStore();

  // Memo
  const filteredVoyages = useMemo(() => {
    let allVoyages = Object.values(voyages);
    const query = searchQuery.trim().toLowerCase();

    // Search filter
    if (query) {
      allVoyages = allVoyages.filter(
        (voyage) => voyage.departure_port?.toLowerCase().includes(query) || voyage.destination_port?.toLowerCase().includes(query)
      );
    }

    // Tab filter
    switch (selectedTab) {
      case 'incomplete':
        allVoyages = allVoyages.filter((voyage) => voyage.voyage_status === 'Incomplete');
        break;
      case 'error':
        allVoyages = allVoyages.filter((voyage) => voyage.voyage_status === 'Error');
        break;
      case 'approved':
        allVoyages = allVoyages.filter((voyage) => voyage.approved_mrv_voyage === true);
        break;
      case 'complete':
        allVoyages = allVoyages.filter((voyage) => voyage.voyage_status === 'Complete');
        break;
      case 'all':
      default:
        break;
    }

    return allVoyages;
  }, [voyages, searchQuery, selectedTab]);

  return filteredVoyages;
}
