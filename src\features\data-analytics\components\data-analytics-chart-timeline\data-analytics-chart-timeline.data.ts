import { DataAnalyticsResponse } from '../../data-analytics.types';
import { frugalColorMap } from './data-analytics-chart-timeline.constants';
import { Series } from './data-analytics-chart-timeline.types';

export function useChartData(data: DataAnalyticsResponse | null) {
  if (!data) return null;

  const frugalStatus = data.data_analytics.frugalStatusTime;

  const series: Series[] = frugalStatus.map((item) => ({
    x: new Date(item.from).getTime(),
    x2: new Date(item.to).getTime(),
    y: item.frugal_mode === 0 ? 0 : 1,
    color: frugalColorMap[item.frugal_mode],
  }));

  return {
    series,
    frugalStatus,
  };
}
