import { useVesselStore } from '@/features/vessels/vessel.store';
import { create } from 'zustand';

interface MrvTableStore {
  searchQuery: string;
  selectedTab: string;
  pageSize: number;
  pageIndex: number;
  year: number | null;
  availableYears: number[];
  isLoading: boolean;

  setSearchQuery: (query: string) => void;
  setSelectedTab: (tab: string) => void;
  setPageSize: (size: number) => void;
  setPageIndex: (index: number) => void;
  setYear: (year: number) => void;
  loadYears: () => Promise<void>;
}

export const useMrvTableStore = create<MrvTableStore>((set) => ({
  searchQuery: '',
  selectedTab: 'all',
  pageSize: 10,
  pageIndex: 0,
  year: null,
  availableYears: [],
  isLoading: false,

  setSearchQuery: (query) => {
    set({ searchQuery: query, pageIndex: 0 });
  },
  setSelectedTab: (tab) => {
    set({ selectedTab: tab, pageIndex: 0 });
  },
  setPageSize: (size) => {
    set({ pageSize: size });
  },
  setPageIndex: (index) => {
    set({ pageIndex: index });
  },

  setYear: (year) => set({ year }),

  loadYears: async () => {
    // Reset state
    set({ pageIndex: 0, availableYears: [], year: null, isLoading: true });

    // Vessel check
    const currentVessel = useVesselStore.getState().currentVessel;
    if (!currentVessel) return;

    // Get years from available dates
    const fromYear = Number(new Date(currentVessel.available_dates[1].timestamp).getFullYear());
    const toYear = Number(new Date(currentVessel.available_dates[0].timestamp).getFullYear());

    // Build years array
    const years: number[] = [];
    for (let year = toYear; year >= fromYear; year--) {
      years.push(year);
    }

    // Set state
    if (years.length > 0) {
      set({ availableYears: years, year: years[0], isLoading: false });
    } else {
      set({ availableYears: [], isLoading: false });
    }
  },
}));
