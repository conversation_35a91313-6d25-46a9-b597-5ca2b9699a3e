'use client';

import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';

interface PaginationControlsProps {
  pageIndex: number;
  pageCount: number;
  onPreviousPage: () => void;
  onNextPage: () => void;
}

export function PaginationControls({ pageIndex, pageCount, onPreviousPage, onNextPage }: PaginationControlsProps) {
  return (
    <div>
      <Pagination className='flex items-center gap-4'>
        <div className='text-muted-foreground text-sm'>
          Page {pageIndex + 1} of {pageCount}
        </div>
        <PaginationContent className='flex space-x-2'>
          <PaginationItem>
            <PaginationPrevious
              aria-disabled={pageIndex <= 0}
              tabIndex={pageIndex <= 0 ? -1 : undefined}
              className={pageIndex <= 0 ? 'pointer-events-none opacity-50' : undefined}
              onClick={onPreviousPage}
              isActive={true}
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationNext
              aria-disabled={pageIndex >= pageCount - 1}
              tabIndex={pageIndex >= pageCount - 1 ? -1 : undefined}
              className={pageIndex >= pageCount - 1 ? 'pointer-events-none opacity-50' : undefined}
              onClick={onNextPage}
              isActive={true}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
