'use client';

import { Column, ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UserGetDTO, UserRole } from '../user.types';
import { UserDelete } from './user-delete';
import { UserEdit } from './user-edit';
import { Severity, SeverityBadge } from '@/components/ui-extensions/severity-badge';
import { cn } from '@/lib/utils';
import SessionWrapper from '@/components/wrappers/session-wrapper';

export const statusToSeverity: Record<string, Severity> = {
  Super_Admin: 'success',
  Admin: 'success',
  Basic: 'info',
};

function SortableHeader({ column, title }: { column: Column<UserGetDTO, unknown>; title: string }) {
  return (
    <Button variant='ghost' className='!p-0' onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
      {title} <ArrowUpDown />
    </Button>
  );
}

export const columns: ColumnDef<UserGetDTO>[] = [
  {
    accessorKey: 'email',
    header: ({ column }) => <SortableHeader column={column} title='Email' />,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <SortableHeader column={column} title='Name' />,
  },
  {
    accessorKey: 'is_verified',
    header: ({ column }) => <SortableHeader column={column} title='Status' />,
    cell: ({ row }) => {
      const verified = row.getValue('is_verified') as boolean;
      return (
        <div className='flex items-center gap-2'>
          <div className={cn('h-2 w-2 rounded-full', verified ? 'bg-green-500' : 'bg-yellow-500')} />
          <span>{verified ? 'Verified' : 'Invited'}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }) => <SortableHeader column={column} title='Role' />,
    cell: ({ row }) => {
      const roleId = row.getValue('role') as UserRole;
      const role = UserRole[roleId].replace(' ', '_');
      const severity = statusToSeverity[role];
      return <SeverityBadge severity={severity}>{role.replace('_', ' ')}</SeverityBadge>;
    },
    sortingFn: 'text',
  },

  {
    id: 'actions',
    header: '',
    cell: ({ row }) => (
      <div className='flex justify-center'>
        <SessionWrapper>
          <UserEdit user={row.original} />
          <UserDelete user={row.original} />
        </SessionWrapper>
      </div>
    ),
  },
];
