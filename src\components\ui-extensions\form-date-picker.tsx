'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Path, FieldValues, Control } from 'react-hook-form';

type FormDatePickerProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label: string;
  fromDate?: Date;
  toDate?: Date;
  disabled?: boolean;
};

export function FormDatePicker<T extends FieldValues>({
  control,
  name,
  label,
  fromDate,
  toDate,
  disabled = false,
}: FormDatePickerProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant={'outline'} className='justify-start font-normal' disabled={disabled}>
                  <CalendarIcon />
                  {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='start'>
                <Calendar
                  mode='single'
                  onSelect={field.onChange}
                  selected={field.value}
                  initialFocus
                  fromDate={fromDate}
                  toDate={toDate}
                  disabled={disabled}
                />
              </PopoverContent>
            </Popover>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
