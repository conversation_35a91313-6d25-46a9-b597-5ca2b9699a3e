'use client';

import React from 'react';
import { useChartData } from './home-fleet-chart-efficiency.data';
import { getChartOptions } from './home-fleet-chart-efficiency.options';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { useHomeFleetStore } from '../../home-fleet.store';
import HomeFleetChartEfficiencyFilter from './home-fleet-chart-efficiency-filter';
import { useVesselStore } from '@/features/vessels/vessel.store';

export default function HomeFleetChartEfficiency() {
  const { fleetEfficiency, isLoadingFleetEfficiency } = useHomeFleetStore();
  const { vessels } = useVesselStore();

  const chartData = useChartData(fleetEfficiency ?? [], vessels);

  const isEmpty = !isLoadingFleetEfficiency && !chartData;

  const options = chartData ? getChartOptions(chartData) : undefined;

  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='relative text-lg font-semibold tracking-tight'>Fleet Efficiency</div>
        <div className='flex items-center gap-2'>
          <HomeFleetChartEfficiencyFilter />
        </div>
      </div>
      {isLoadingFleetEfficiency ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        <HighchartsReact key={JSON.stringify(options)} highcharts={Highcharts} options={options} />
      )}
    </div>
  );
}
