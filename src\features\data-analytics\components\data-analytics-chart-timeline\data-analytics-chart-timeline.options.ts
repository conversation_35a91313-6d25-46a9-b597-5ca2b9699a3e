import Highcharts from 'highcharts';
import { FrugalStatusTime } from '../../data-analytics.types';
import { Series } from './data-analytics-chart-timeline.types';
import { frugalColorMap, frugalModeLabels } from './data-analytics-chart-timeline.constants';

export function getChartOptions({
  series,
  frugalStatus,
  fromDate,
  chartId,
  allChartRefs,
  isSyncing,
}: {
  series: Series[];
  frugalStatus: FrugalStatusTime[];
  fromDate: string;
  chartId: string;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
  isSyncing: boolean;
}): Highcharts.Options {
  return {
    chart: {
      type: 'xrange',
      marginLeft: 90,
      marginRight: 108,
      zooming: { type: 'x' },
      height: 200,
    },
    xAxis: {
      type: 'datetime',
      title: { text: '' },
      min: new Date(fromDate).getTime(),
      max: new Date().getTime(),
      events: {
        setExtremes(e: Highcharts.AxisSetExtremesEventObject) {
          if (isSyncing) return;

          isSyncing = true;
          Object.entries(allChartRefs).forEach(([key, ref]) => {
            if (key !== chartId && ref.current) {
              ref.current.xAxis[0].setExtremes(e.min, e.max, true, false);
            }
          });
          isSyncing = false;
        },
      },
    },
    yAxis: {
      categories: ['Off', 'On'],
      title: { text: '' },
    },
    series: [
      {
        name: 'Frugal Mode',
        type: 'xrange',
        data: series,
      },
    ],
    tooltip: {
      useHTML: true,

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltip(this.point as Highcharts.XrangePointOptionsObject, frugalStatus);
      },
    },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 160,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 120,
            },
          },
        },
      ],
    },
    title: { text: '' },
    legend: { enabled: false },
    exporting: { enabled: false },
    credits: { enabled: false },
  };
}

export function formatTooltip(point: Highcharts.XrangePointOptionsObject, frugalStatusData: FrugalStatusTime[]): string {
  const match = frugalStatusData.find((p) => new Date(p.from).getTime() === point.x && new Date(p.to).getTime() === point.x2);

  const mode = match?.frugal_mode ?? -1;
  const label = frugalModeLabels[mode] || 'Idle';
  const color = frugalColorMap[mode] || '#ccc';

  return `
    <div style="font-size: 13px; line-height: 1.5;">
      <b>Frugal Mode:</b> ${label} <span style="color:${color}">\u25CF</span><br/>
      <hr style="margin: 6px 0;" />
      <b>From:</b> ${Highcharts.dateFormat('%A, %b %e, %H:%M:%S', point.x ?? 0)} (UTC)<br/>
      <b>To:</b> ${Highcharts.dateFormat('%A, %b %e, %H:%M:%S', point.x2 ?? 0)} (UTC)<br/>
      <hr style="margin: 6px 0;" />
      <b>Duration:</b> ${match?.total_time || 'N/A'}
    </div>
  `;
}
