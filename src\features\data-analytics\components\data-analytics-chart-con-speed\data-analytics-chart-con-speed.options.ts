import Highcharts from 'highcharts';
import { Series } from './data-analytics-chart-con-speed.types';
import { colorMap } from './data-analytics-chart-con-speed.constants';
export function getChartOptions(chartData: Series): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [];

  for (let i = 0; i < chartData.yValues.length; i++) {
    series.push({
      name: chartData.labels[i] + 'm',
      data: chartData.yValues[i],
      type: 'line',
      color: colorMap[i as keyof typeof colorMap],
    });
  }

  return {
    chart: {
      zooming: {
        type: 'x',
      },
    },
    title: { text: '' },
    xAxis: {
      title: { text: 'Speed (kn)' },
      crosshair: true,
      categories: chartData.categories,
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        min: 0,
        title: { text: 'Avg. Main Engine Consumption (mt/24hrs)' },
        labels: { style: { color: 'black' } },
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      formatter: formatTooltip,
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          align: 'right',
          verticalAlign: 'top',
          menuItems: [
            'viewFullscreen',
            'printChart',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS',
          ],
        },
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
      x: -40,
      y: -5,
    },
    credits: { enabled: false },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
            },
          },
        },
      ],
    },
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function formatTooltip(this: any) {
  if (!this.points || typeof this.x === 'undefined') return '';

  const chart = this.points[0].series.chart;
  const categories = chart.options.xAxis?.[0]?.categories as string[] | undefined;
  const xLabel = categories?.[this.x as number] ?? this.x;

  const sortedPoints = [...this.points].sort((a, b) => b.y - a.y);

  const lines = sortedPoints.map(
    (point: { color: string; series: { name: string }; y: number }) => `
      <hr style="margin: 6px 0;" />
      <span style="color:${point.color}">\u25CF</span> <b>Draft Alt: ${point.series.name}</b>: ${point.y} kn
    `
  );

  return `
    <div style="font-size: 13px; padding: 4px 0">
      <div style="font-weight: bold; margin-bottom: 6px;">
        Speed: <span style="color: #333;">${xLabel} kn</span>
      </div>
      ${lines.join('<br/>')}
    </div>
  `;
}
