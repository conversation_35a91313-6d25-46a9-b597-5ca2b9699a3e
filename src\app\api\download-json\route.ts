import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  from_date: z.string().nonempty('from_date must be provided'),
  to_date: z.string().nonempty('to_date must be provided'),
  vessel_name: z.string().nonempty('vessel_name must be provided'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/export/json', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const response = await httpClient.getBinary(request, url.toString());

    // Get the JSON data as an array buffer
    const jsonData = await response.arrayBuffer();

    // Return the response with appropriate headers for JSON download
    return new NextResponse(jsonData, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': 'attachment; filename="data-export.json"',
      },
    });
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
