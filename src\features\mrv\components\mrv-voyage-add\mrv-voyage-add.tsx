'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { MrvVoyageAddForm } from '@/features/mrv/components/mrv-voyage-add/mrv-voyage-add-form';
import { useMrvStore } from '@/features/mrv/mrv.store';

export function MrvVoyageAdd() {
  const [open, setOpen] = useState(false);
  const { isLoading } = useMrvStore();

  return (
    <>
      <Button variant='outline' onClick={() => setOpen(true)} disabled={isLoading}>
        Add Voyage
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <MrvVoyageAddForm onSuccess={() => setOpen(false)} onCancel={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </>
  );
}
