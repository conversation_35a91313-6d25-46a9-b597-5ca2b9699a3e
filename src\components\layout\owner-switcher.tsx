'use client';

import React from 'react';
import { ChevronsUpDown, Loader2, ShipWheel } from 'lucide-react';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { useOwnerStore } from '@/features/owners/owner.store';
import { useSession } from 'next-auth/react';
import { OwnerDTO } from '@/features/owners/owner.types';
import { ExtendedSession } from '@/lib/auth';

export function OwnerSwitcher() {
  const { isMobile } = useSidebar();
  const { owners, isLoading, loadOwners } = useOwnerStore();
  const { data: session, update } = useSession();

  React.useEffect(() => {
    loadOwners();
  }, [loadOwners]);

  // Handle unauthenticated or loading session
  if (isLoading || !session) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size='lg' disabled>
            <div className='text-muted-foreground flex aspect-square size-8 items-center justify-center rounded-lg border'>
              <Loader2 className='size-4 animate-spin' />
            </div>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <span className='truncate font-semibold'>Loading...</span>
              <span className='truncate text-xs'>---</span>
            </div>
            <ChevronsUpDown className='ml-auto' />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  const extendedSession = session as ExtendedSession;
  const sessionOwner = extendedSession.current_owner;
  const currentOwner = owners.find((owner) => owner.vat === sessionOwner?.owner_vat);

  const handleOwnerSwitch = async (owner: OwnerDTO) => {
    await update({ vat: owner.vat });
    window.location.reload();
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton size='lg' className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'>
              <div className='text-muted-foreground flex aspect-square size-8 items-center justify-center rounded-lg border'>
                {isLoading ? <Loader2 className='size-4 animate-spin' /> : <ShipWheel className='size-4' />}
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>{currentOwner?.name ?? 'Unknown Owner'}</span>
                <span className='truncate text-xs'>{currentOwner?.vat ?? '—'}</span>
              </div>
              <ChevronsUpDown className='ml-auto' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='z-401 max-h-[400px] w-[--radix-dropdown-menu-trigger-width] min-w-56 overflow-y-auto rounded-lg'
            align='start'
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className='text-muted-foreground text-xs'>Ship Owners</DropdownMenuLabel>
            {owners.map((owner) => (
              <DropdownMenuItem
                key={owner.id}
                onClick={() => handleOwnerSwitch(owner)}
                className={cn('cursor-pointer gap-2 p-2', currentOwner?.id === owner.id && 'bg-accent')}
              >
                <div className='flex size-6 items-center justify-center rounded-sm border'>
                  <ShipWheel className='size-4 shrink-0' />
                </div>
                {owner.name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
