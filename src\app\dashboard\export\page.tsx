'use client';

import { PageHeader } from '@/components/layout/page-header';
import ExportExcel from '@/features/export/components/export-excel';
import ExportJson from '@/features/export/components/export-json';
import ExportCylinderTemperature from '@/features/export/components/export-cylinder-temperature';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { useOwnerStore } from '@/features/owners/owner.store';
import { useMemo, useEffect, useState } from 'react';
import { DotLoader } from '@/components/ui-extensions/dot-loader';
import axios from 'axios';

interface CylinderTemperatureDateRange {
  from_date: string | null;
  to_date: string | null;
}

export default function ExportPage() {
  const { currentVessel, isLoading: isLoadingVessels } = useVesselStore();
  const { owners, isLoading: isLoadingOwners } = useOwnerStore();
  const [hasCylinderData, setHasCylinderData] = useState<boolean>(false);
  const [isCheckingCylinderData, setIsCheckingCylinderData] = useState<boolean>(false);

  // Check if current vessel's owner is Wilson Ship Management
  const isWilsonShipManagement = useMemo(() => {
    if (!currentVessel?.assigned_owner_vat || !owners.length) return false;

    const vesselOwner = owners.find((owner) => owner.vat === currentVessel.assigned_owner_vat);
    return vesselOwner?.name === 'Wilson Ship Management';
  }, [currentVessel?.assigned_owner_vat, owners]);

  // Check for cylinder temperature data availability when vessel is Wilson Ship Management
  useEffect(() => {
    const checkCylinderData = async () => {
      if (!isWilsonShipManagement || !currentVessel?.imo || !currentVessel?.assigned_owner_vat) {
        setHasCylinderData(false);
        return;
      }

      setIsCheckingCylinderData(true);
      try {
        const { data } = await axios.get<CylinderTemperatureDateRange>('/api/cylinder-temperature/date-range', {
          params: {
            vessel_imo: currentVessel.imo,
            owner_vat: currentVessel.assigned_owner_vat,
          },
        });

        // Check if both from_date and to_date are available
        setHasCylinderData(data.from_date !== null && data.to_date !== null);
      } catch (error) {
        console.error('Error checking cylinder temperature data:', error);
        setHasCylinderData(false);
      } finally {
        setIsCheckingCylinderData(false);
      }
    };

    checkCylinderData();
  }, [isWilsonShipManagement, currentVessel?.imo, currentVessel?.assigned_owner_vat]);

  const isLoading = isLoadingVessels || isLoadingOwners;
  const shouldShowCylinderComponent = isWilsonShipManagement && hasCylinderData;

  return (
    <div className='flex h-full flex-col gap-4'>
      <PageHeader title='Export' />
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
        <ExportJson />
        <ExportExcel />
        {(isLoading || isCheckingCylinderData) && isWilsonShipManagement ? (
          <div className='bg-card text-card-foreground flex min-h-[120px] items-center justify-center rounded-md border p-4'>
            <DotLoader />
          </div>
        ) : (
          shouldShowCylinderComponent && <ExportCylinderTemperature />
        )}
      </div>
    </div>
  );
}
