import { NextResponse, NextRequest } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { z } from 'zod';
import { AnomalyReport } from '@/features/anomaly-detection/anomaly-detection.types';

// === SCHEMA ===
const querySchema = z.object({
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL with query params
  const url = new URL('/anomaly-detection', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<AnomalyReport>(request, url.toString());
    return NextResponse.json(res.anomalies[0]);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
