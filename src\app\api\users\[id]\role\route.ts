import { UserGetDTO, UserRole } from '@/features/users/user.types';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { ensureMinRole } from '@/lib/auth-guard';
import { z } from 'zod';

// === PATCH PARAMS SCHEMA ===
const patchParamsSchema = z.object({
  id: z.string().nonempty('id must be provided'),
});

// === PATCH BODY SCHEMA ===
const patchBodySchema = z.object({
  role: z.nativeEnum(UserRole, { errorMap: () => ({ message: 'Invalid role' }) }),
});

// === PATCH ENDPOINT ===
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // Validate params
  const { id } = await params;
  const validatedParams = patchParamsSchema.safeParse({ id });
  if (!validatedParams.success) {
    return NextResponse.json({ errors: validatedParams.error.flatten().fieldErrors }, { status: 400 });
  }

  // Validate body
  const validatedBody = patchBodySchema.safeParse(await request.json());
  if (!validatedBody.success) {
    return NextResponse.json({ errors: validatedBody.error.flatten().fieldErrors }, { status: 400 });
  }

  // Ensure min role
  const [forbiddenResponse] = await ensureMinRole(UserRole.Admin);
  if (forbiddenResponse) return forbiddenResponse;

  // Build URL
  const url = new URL(`/users/${validatedParams.data.id}/role`, process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.patch<UserGetDTO>(request, url.toString(), validatedBody.data);
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
