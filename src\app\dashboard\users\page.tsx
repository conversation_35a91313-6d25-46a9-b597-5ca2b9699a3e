import { PageHeader } from '@/components/layout/page-header';
import { UserAdd } from '@/features/users/components/user-add';
import { UserSearch } from '@/features/users/components/user-search';
import { UserTable } from '@/features/users/components/user-table';

export default function UsersPage() {
  return (
    <>
      <PageHeader title='User Management' className='mb-2' />

      <div className='mb-2 flex gap-2'>
        <UserSearch />
        <UserAdd />
      </div>

      <UserTable />
    </>
  );
}
