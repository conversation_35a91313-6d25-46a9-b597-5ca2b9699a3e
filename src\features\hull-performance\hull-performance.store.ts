import { create } from 'zustand';
import axios from 'axios';
import { DataAi, DataRaw, DataSearch } from './hull-performance.types';

interface HullPerformanceStore {
  selectedOwnerVat: string;
  vesselImo: number;
  dataRaw: DataRaw;
  dataAi: DataAi;
  isLoading: boolean;
  searchData: DataSearch;
  useSearchData: boolean;
  searchPerformed: boolean;
  dailyRateOfShaftChange: number;
  overConsumptionFuel: number;
  shaftDiff: number;
  shaftDiffPercentage: number;
  speedsList: (number | 'All')[];
  selectedSpeed: number | 'All';
  fromValue: string;
  toValue: string;
  modelTrainFrom: string;
  modelTrainTo: string;
  errorRate: number;

  setInitialHullData: (vessel_imo: number, owner_vat: string) => Promise<void>;
  setSearchData: (vessel_imo: number, owner_vat: string, start_date: string, end_date: string) => Promise<void>;
  setGaugeValues: () => void;
  setSelectedSpeed: (selectedSpeed: number | 'All') => void;
  setFromValue: (fromValue: string) => void;
  setToValue: (toValue: string) => void;
  clearDateRange: () => void;
  setModelTrainFrom: () => void;
  setModelTrainTo: () => void;
  setErrorRate: () => void;
  setInitialSpeedsList: () => void;
  updateSpeedsListFromSearchData: () => void;
  setInitialDateRange: () => void;

  // Helper functions to check data availability
  hasRawChartData: () => boolean;
  hasAiChartData: () => boolean;
  hasAiChartDataForCurrentState: () => boolean;
  hasAnyChartData: () => boolean;
}

export const useHullPerformanceStore = create<HullPerformanceStore>((set, get) => ({
  // Default values for testing - replace with actual values when connecting to real API
  selectedOwnerVat: 'default-vat',
  vesselImo: 9999999,
  dataRaw: {} as DataRaw,
  dataAi: {} as DataAi,
  isLoading: true,
  searchData: {} as DataSearch,
  useSearchData: false,
  searchPerformed: false,
  dailyRateOfShaftChange: 0,
  overConsumptionFuel: 0,
  shaftDiff: 0,
  shaftDiffPercentage: 0,
  speedsList: [],
  selectedSpeed: 0,
  fromValue: '',
  toValue: '',
  modelTrainFrom: '',
  modelTrainTo: '',
  errorRate: 0,

  setInitialHullData: async (vessel_imo: number, owner_vat: string) => {
    // Clear date range when changing vessels before fetching new data
    get().clearDateRange();

    // Always set loading state to true at the beginning
    set({
      vesselImo: vessel_imo,
      selectedOwnerVat: owner_vat,
      dataRaw: {} as DataRaw,
      dataAi: {} as DataAi,
      isLoading: true,
    });

    try {
      const [{ data: dataRaw }, { data: dataAi }] = await Promise.all([
        axios.get<DataRaw>('/api/hull-performance/raw', {
          params: {
            vessel_imo,
            owner_vat,
          },
        }),
        axios.get<DataAi>('/api/hull-performance/ai', {
          params: {
            vessel_imo,
            owner_vat,
          },
        }),
      ]);

      // Set loading to false only after data is loaded
      set({
        dataRaw,
        dataAi,
        useSearchData: false,
        searchPerformed: false,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  setSearchData: async (vessel_imo: number, owner_vat: string, start_date: string, end_date: string) => {
    // Set loading state and clear data in one update
    set({
      vesselImo: vessel_imo,
      dataRaw: {} as DataRaw,
      isLoading: true,
      searchPerformed: true,
    });

    try {
      const { data: dataSearch } = await axios.get<DataSearch>('/api/hull-performance/search', {
        params: {
          vessel_imo,
          owner_vat,
          start_date,
          end_date,
        },
      });

      // Default values if data is missing
      const defaultGaugeValues = {
        dailyRateOfShaftChange: 0,
        overConsumptionFuel: 0,
        shaftDiff: 0,
        shaftDiffPercentage: 0,
      };

      // Check if we received valid data
      if (dataSearch && Object.keys(dataSearch).length > 0) {
        // If we have valid search data with gages, extract the gauge values
        if (dataSearch.gages && typeof dataSearch.gages === 'object') {
          const gagesData = dataSearch.gages;

          // Set all data at once to minimize state updates
          set({
            searchData: dataSearch,
            useSearchData: true,
            isLoading: false,
            dailyRateOfShaftChange:
              typeof gagesData.average_daily_rate_of_change === 'number' ? Number(gagesData.average_daily_rate_of_change.toFixed(2)) : 0,
            overConsumptionFuel: typeof gagesData.average_fuel_changes === 'number' ? Number(gagesData.average_fuel_changes.toFixed(2)) : 0,
            shaftDiff:
              typeof gagesData.average_shaft_power_changes === 'number' ? Number(gagesData.average_shaft_power_changes.toFixed(2)) : 0,
            shaftDiffPercentage:
              typeof gagesData.average_shaft_power_changes_percentage === 'number'
                ? Number(gagesData.average_shaft_power_changes_percentage.toFixed(2))
                : 0,
          });
        } else {
          // If we have search data but no gauge data, set default gauge values
          set({
            searchData: dataSearch,
            useSearchData: true,
            isLoading: false,
            ...defaultGaugeValues,
          });
        }

        // Update the speeds list from the search data
        get().updateSpeedsListFromSearchData();
      } else {
        // If no valid data is returned, reset to default state
        set({
          searchData: {} as DataSearch,
          useSearchData: false,
          isLoading: false,
          ...defaultGaugeValues,
        });
      }
    } catch (error) {
      console.error('Error in setSearchData:', error);
      set({
        isLoading: false,
        useSearchData: false,
        dailyRateOfShaftChange: 0,
        overConsumptionFuel: 0,
        shaftDiff: 0,
        shaftDiffPercentage: 0,
      });
      throw error;
    }
  },

  // Gages
  setGaugeValues: () => {
    try {
      const { dataAi } = get();

      // Default values if data is missing
      const defaultGaugeValues = {
        dailyRateOfShaftChange: 0,
        overConsumptionFuel: 0,
        shaftDiff: 0,
        shaftDiffPercentage: 0,
      };

      if (dataAi && dataAi.all) {
        const aiData = dataAi.all;

        set({
          dailyRateOfShaftChange:
            typeof aiData.average_daily_rate_of_change === 'number' ? Number(aiData.average_daily_rate_of_change.toFixed(2)) : 0,
          overConsumptionFuel: typeof aiData.average_fuel_changes === 'number' ? Number(aiData.average_fuel_changes.toFixed(2)) : 0,
          shaftDiff: typeof aiData.average_shaft_power_changes === 'number' ? Number(aiData.average_shaft_power_changes.toFixed(2)) : 0,
          shaftDiffPercentage:
            typeof aiData.average_shaft_power_changes_percentage === 'number'
              ? Number(aiData.average_shaft_power_changes_percentage.toFixed(2))
              : 0,
        });
      } else {
        // If no valid data is found, set default values
        set(defaultGaugeValues);
      }
    } catch (error) {
      console.error('Error in setGaugeValues:', error);
      // Keep the default values we set at the beginning
    }
  },

  // Speed selection on speed dial
  setSelectedSpeed: (selectedSpeed: number | 'All') => set({ selectedSpeed }),
  setInitialSpeedsList: () => {
    const { dataRaw } = get();

    if (!dataRaw) {
      set({ speedsList: [], selectedSpeed: 'All' });
      return;
    }

    const uniqueSpeeds = new Set<number | 'All'>(['All']);

    // Extract speeds from dataRaw
    Object.values(dataRaw).forEach((value) => {
      if ('speed' in value && typeof value.speed === 'number') {
        uniqueSpeeds.add(value.speed);
      }
    });

    // Convert to array and sort
    let uniqueSpeedArray = Array.from(uniqueSpeeds);
    uniqueSpeedArray = uniqueSpeedArray.filter((speed) => speed !== 'All') as number[];
    uniqueSpeedArray.sort((a, b) => {
      if (typeof a === 'number' && typeof b === 'number') {
        return a - b;
      }
      return 0;
    });

    // Add "All" back at the beginning
    uniqueSpeedArray.unshift('All');

    // Update the store
    set({
      speedsList: uniqueSpeedArray,
      selectedSpeed: uniqueSpeedArray[0],
    });
  },

  updateSpeedsListFromSearchData: () => {
    const { searchData } = get();

    if (!searchData || Object.keys(searchData).length === 0) {
      return;
    }

    const uniqueSpeeds = new Set<number | 'All'>(['All']);

    // Extract speeds from searchData
    Object.keys(searchData).forEach((key) => {
      // Skip non-speed entries like 'all', 'gages', etc.
      if (key !== 'all' && key !== 'gages' && key !== 'speed_trendlines') {
        // Keys are in format "speed_draft" like "10.0_5.5"
        const speedValue = parseFloat(key.split('_')[0]);
        if (!isNaN(speedValue)) {
          uniqueSpeeds.add(speedValue);
        }
      }
    });

    // Convert to array and sort
    let uniqueSpeedArray = Array.from(uniqueSpeeds);
    uniqueSpeedArray = uniqueSpeedArray.filter((speed) => speed !== 'All') as number[];
    uniqueSpeedArray.sort((a, b) => {
      if (typeof a === 'number' && typeof b === 'number') {
        return a - b;
      }
      return 0;
    });

    // Add "All" back at the beginning
    uniqueSpeedArray.unshift('All');

    // Update the store
    set({
      speedsList: uniqueSpeedArray,
      selectedSpeed: uniqueSpeedArray[0],
    });
  },

  // Time selector
  setFromValue: (fromValue: string) => set({ fromValue: fromValue }),
  setToValue: (toValue: string) => set({ toValue: toValue }),
  clearDateRange: () => set({ fromValue: '', toValue: '' }),
  setInitialDateRange: () => {
    const { dataRaw, dataAi } = get();

    let firstDateManual, lastDateManual;
    if (dataRaw?.all?.all_timestamps?.length > 0) {
      firstDateManual = dataRaw.all.all_timestamps.reduce((min, ts) => (ts < min ? ts : min));
      lastDateManual = dataRaw.all.all_timestamps.reduce((max, ts) => (ts > max ? ts : max));
    }

    let firstDateAI, lastDateAI;
    if (dataAi?.all?.timestamps?.length > 0) {
      firstDateAI = dataAi.all.timestamps.reduce((min, ts) => (ts < min ? ts : min));
      lastDateAI = dataAi.all.timestamps.reduce((max, ts) => (ts > max ? ts : max));
    } else {
      // If no data is available, leave the date range empty
      set({
        fromValue: '',
        toValue: '',
      });
      return;
    }

    const earliestDate = firstDateManual
      ? new Date(firstDateManual) < new Date(firstDateAI)
        ? firstDateManual
        : firstDateAI
      : firstDateAI;

    const latestDate = lastDateManual ? (new Date(lastDateManual) > new Date(lastDateAI) ? lastDateManual : lastDateAI) : lastDateAI;

    set({
      fromValue: earliestDate,
      toValue: latestDate,
    });
  },

  // Model train period and error
  setModelTrainFrom: () => {
    const { dataAi } = get();
    const referenceFrom = dataAi?.all?.model_train_from ? new Date(dataAi.all.model_train_from).toISOString().split('T')[0] : '';
    set({ modelTrainFrom: referenceFrom });
  },
  setModelTrainTo: () => {
    const { dataAi } = get();
    const referenceTo = dataAi?.all?.model_train_to ? new Date(dataAi.all.model_train_to).toISOString().split('T')[0] : '';
    set({ modelTrainTo: referenceTo });
  },
  setErrorRate: () => {
    const { dataAi } = get();
    const errorRateCalc = dataAi?.all?.error_rate ? Math.round(dataAi.all.error_rate) : 0;
    set({ errorRate: errorRateCalc });
  },

  // Helper functions to check data availability
  hasRawChartData: () => {
    const { dataRaw, searchData, useSearchData } = get();

    // Determine which data to use based on the useSearchData flag
    const hasSearchData = searchData && Object.keys(searchData).length > 0;
    const chartData = useSearchData && hasSearchData ? searchData : dataRaw;

    // Check if we have valid data to display
    if (!chartData || Object.keys(chartData).length === 0) return false;

    // Check if 'all' data exists and has values
    return (
      chartData.all &&
      chartData.all.all_timestamps &&
      chartData.all.all_timestamps.length > 0 &&
      chartData.all.all_shaft_power &&
      chartData.all.all_shaft_power.length > 0
    );
  },

  hasAiChartData: () => {
    const { dataAi } = get();

    // Check if we have a valid dataAi object
    if (!dataAi || !dataAi.all) return false;

    // Check if we have actual data to display
    return (
      dataAi.all.timestamps && dataAi.all.timestamps.length > 0 && dataAi.all.real_shaft_powers && dataAi.all.real_shaft_powers.length > 0
    );
  },

  hasAiChartDataForCurrentState: () => {
    const { dataAi, searchPerformed, searchData, useSearchData } = get();

    // If a search was performed, check if search returned data
    if (searchPerformed) {
      // If search returned data, show AI chart (search includes AI data)
      if (useSearchData && searchData && Object.keys(searchData).length > 0) {
        // Check if we have AI data to display (either from search or original)
        if (!dataAi || !dataAi.all) return false;

        return (
          dataAi.all.timestamps &&
          dataAi.all.timestamps.length > 0 &&
          dataAi.all.real_shaft_powers &&
          dataAi.all.real_shaft_powers.length > 0
        );
      } else {
        // If search returned no data, show "No Data Available"
        return false;
      }
    }

    // If no search was performed, check regular AI data
    if (!dataAi || !dataAi.all) return false;

    return (
      dataAi.all.timestamps && dataAi.all.timestamps.length > 0 && dataAi.all.real_shaft_powers && dataAi.all.real_shaft_powers.length > 0
    );
  },

  hasAnyChartData: () => {
    const { hasRawChartData, hasAiChartData } = get();
    return hasRawChartData() || hasAiChartData();
  },
}));
