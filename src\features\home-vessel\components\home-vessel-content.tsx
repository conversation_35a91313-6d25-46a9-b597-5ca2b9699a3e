'use client';

import HomeVesselFpUsage from './home-vessel-fp-usage';
import dynamic from 'next/dynamic';
import React from 'react';
import { useHomeVesselStore } from '../home-vessel.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { HomeVesselSummary } from './home-vessel-summary';
import HomeVesselChartFuelConsumption from './home-vessel-chart-fuel-consumption/home-vessel-chart-fuel-consumption';
import HomeVesselChartEfficiency from './home-vessel-chart-efficiency/home-vessel-chart-efficiency';

const VesselMap = dynamic(() => import('./home-vessel-map'), {
  ssr: false,
});

export default function HomeVesselContent() {
  const { fetchVesselData } = useHomeVesselStore();
  const { currentVessel } = useVesselStore();

  React.useEffect(() => {
    if (currentVessel?.imo) {
      fetchVesselData();
    }
  }, [currentVessel, fetchVesselData]);

  return (
    <div className='flex flex-col gap-4'>
      <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
        <div className='flex flex-col gap-4'>
          <HomeVesselSummary />
          <HomeVesselFpUsage />
        </div>
        <VesselMap />
      </div>

      <div className='grid grid-cols-1 gap-4 xl:grid-cols-2'>
        <HomeVesselChartFuelConsumption />
        <HomeVesselChartEfficiency />
      </div>
    </div>
  );
}
