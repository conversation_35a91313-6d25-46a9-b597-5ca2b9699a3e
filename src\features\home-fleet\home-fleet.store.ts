import axios from 'axios';
import { create } from 'zustand';
import { FleetEfficiencyGetDTO, FleetEfficiencyPostDTO } from './home-fleet-efficiency.types';
import { toast } from 'sonner';
import { FleetVesselsResponse } from './fleet.types';

interface HomeFleetStore {
  fleetData: FleetVesselsResponse | null;
  isLoadingFleetData: boolean;
  fleetEfficiency: FleetEfficiencyGetDTO | null;
  isLoadingFleetEfficiency: boolean;
  fetchFleetData: () => Promise<void>;
  fetchFleetEfficiency: (params: FleetEfficiencyPostDTO) => Promise<void>;
}

export const useHomeFleetStore = create<HomeFleetStore>((set) => ({
  fleetData: null,
  isLoadingFleetData: false,
  fleetEfficiency: null,
  isLoadingFleetEfficiency: false,

  fetchFleetData: async () => {
    set({ isLoadingFleetData: true, fleetData: null });
    try {
      const res = await axios.get<FleetVesselsResponse>(`/api/home/<USER>
      set({ fleetData: res.data });
    } catch (_error) {
      toast.error('Failed to fetch fleet data');
    } finally {
      set({ isLoadingFleetData: false });
    }
  },

  fetchFleetEfficiency: async (params: FleetEfficiencyPostDTO) => {
    set({ isLoadingFleetEfficiency: true, fleetEfficiency: null });
    try {
      const res = await axios.post<FleetEfficiencyGetDTO>(`/api/home/<USER>
      set({ fleetEfficiency: res.data });
    } catch (_error) {
      toast.error('Failed to fetch fleet efficiency');
    } finally {
      set({ isLoadingFleetEfficiency: false });
    }
  },
}));
