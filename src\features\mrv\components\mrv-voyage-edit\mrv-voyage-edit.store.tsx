import { create } from 'zustand';

import { MrvVoyage } from '../../mrv.types';

interface MrvVoyageEditStore {
  voyage: MrvVoyage | null;
  isOpen: boolean;

  openDialog: (voyage: MrvVoyage) => void;
  closeDialog: () => void;
}

export const useMrvVoyageEditStore = create<MrvVoyageEditStore>((set) => ({
  voyage: null,
  isOpen: false,

  openDialog: (voyage: MrvVoyage) => set({ voyage, isOpen: true }),
  closeDialog: () => set({ voyage: null, isOpen: false }),
}));
