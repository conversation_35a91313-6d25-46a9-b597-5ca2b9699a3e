import { create } from 'zustand';
import { UserGetDTO, UserRole } from './user.types';
import { toast } from 'sonner';
import axios from 'axios';

interface UserStore {
  users: UserGetDTO[];
  filteredUsers: UserGetDTO[];
  searchQuery: string;
  pageSize: number;
  pageIndex: number;
  isLoading: boolean;

  setSearchQuery: (query: string) => void;
  setPageSize: (size: number) => void;
  setPageIndex: (index: number) => void;
  setUsers: (users: UserGetDTO[]) => void;

  loadUsers: () => Promise<void>;
  createUser: (email: string, role: UserRole) => Promise<void>;
  updateUser: (user: UserGetDTO, role: UserRole) => Promise<void>;
  deleteUser: (user: UserGetDTO) => Promise<void>;
}

export const useUserStore = create<UserStore>((set, get) => ({
  users: [],
  filteredUsers: [],
  searchQuery: '',
  pageSize: 10,
  pageIndex: 0,
  isLoading: false,

  setSearchQuery: (query) => {
    set({ searchQuery: query, pageIndex: 0 });

    const { users } = get();
    const lowerCaseQuery = query.toLowerCase();

    const filtered = users.filter(
      (user) => user.email.toLowerCase().includes(lowerCaseQuery) || user.name.toLowerCase().includes(lowerCaseQuery)
    );

    set({ filteredUsers: filtered });
  },

  setPageSize: (size) => set({ pageSize: size, pageIndex: 0 }),
  setPageIndex: (index) => set({ pageIndex: index }),
  setUsers: (users) => set({ users, filteredUsers: users }),

  loadUsers: async () => {
    set({ isLoading: true, filteredUsers: [], users: [] });

    try {
      const { data: users } = await axios.get<UserGetDTO[]>('/api/users');

      const { searchQuery } = get();

      const filtered = users.filter(
        (user) =>
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) || user.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

      set({ users, filteredUsers: filtered });
    } catch (_error) {
      toast.error('Error loading users');
      throw new Error('Error loading users');
    } finally {
      set({ isLoading: false });
    }
  },

  createUser: async (email, role) => {
    try {
      // TODO: Remove this when the feature is implemented
      toast.info('This feature is coming soon');
      return;

      const { data: newUser } = await axios.post<UserGetDTO>('/api/users', { email, role });

      set((state) => ({
        users: [newUser, ...state.users],
        filteredUsers: [newUser, ...state.filteredUsers],
      }));
      toast.success(`User ${newUser.email} invited successfully`);
    } catch (_error) {
      toast.error('Error inviting user');
      throw new Error('Error inviting user');
    }
  },

  updateUser: async (user, role) => {
    try {
      // TODO: Remove this when the feature is implemented
      toast.info('This feature is coming soon');
      return;

      const { data: updatedUser } = await axios.patch<UserGetDTO>(`/api/users/${user.user_id}/role`, { role });

      set((state) => ({
        users: state.users.map((item) => (item.user_id === user.user_id ? updatedUser : item)),
        filteredUsers: state.filteredUsers.map((item) => (item.user_id === user.user_id ? updatedUser : item)),
      }));
      toast.success(`User ${user.email} role updated successfully`);
    } catch (_error) {
      toast.error(`Error updating ${user.email} role`);
      throw new Error(`Error updating ${user.email} role`);
    }
  },

  deleteUser: async (user) => {
    try {
      // TODO: Remove this when the feature is implemented
      toast.info('This feature is coming soon');
      return;

      await axios.delete(`/api/users/${user.user_id}`);

      set((state) => ({
        users: state.users.filter((item) => item.user_id !== user.user_id),
        filteredUsers: state.filteredUsers.filter((item) => item.user_id !== user.user_id),
      }));
      toast.success(`User ${user.email} deleted successfully`);
    } catch (_error) {
      toast.error(`Error deleting ${user.email}`);
      throw new Error(`Error deleting ${user.email}`);
    }
  },
}));
