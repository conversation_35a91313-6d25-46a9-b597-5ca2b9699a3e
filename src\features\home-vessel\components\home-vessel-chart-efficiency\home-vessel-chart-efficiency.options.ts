import Highcharts from 'highcharts';
import { Series } from './home-vessel-chart-efficiency.types';
import { colorMap } from './home-vessel-chart-efficiency.constants';

export function getChartOptions(chartData: Series): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      type: 'line',
      name: 'SFOC',
      data: chartData.sfocValues,
      yAxis: 0,
      color: colorMap.sfoc,
    },
    {
      type: 'line',
      name: 'Propulsion Efficiency',
      data: chartData.propulsionValues,
      yAxis: 1,
      color: colorMap.efficiency,
    },
    {
      type: 'line',
      name: chartData.speedType,
      data: chartData.speedValues,
      yAxis: 2,
      color: colorMap.speed,
    },
  ];

  return {
    chart: { zooming: { type: 'x' } },
    exporting: { enabled: false },
    credits: { enabled: false },
    title: { text: '' },
    xAxis: {
      title: { text: '' },
      crosshair: true,
      categories: chartData.dayLabels,
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        min: 0,
        title: { text: 'SFOC (g/kWh)', style: { color: colorMap.sfoc } },
        labels: { style: { color: colorMap.sfoc } },
      },
      {
        title: { text: 'Propulsion Efficiency (kg/nm)', style: { color: colorMap.efficiency } },
        opposite: true,
        labels: { style: { color: colorMap.efficiency } },
      },
      {
        title: { text: chartData.speedType + ' (kn)', style: { color: colorMap.speed } },
        opposite: true,
        labels: { style: { color: colorMap.speed } },
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltipDaily(this, chartData);
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
  };
}

export function formatTooltipDaily(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number; index: number }[] },
  chartData: Series
): string {
  const index = ctx?.points?.[0]?.index ?? 0;
  const day = chartData.dayLabels[index];
  const speed = chartData.speedValues[index];
  const sfoc = chartData.sfocValues[index];
  const sfocTime = chartData.sfocTimeValues?.[index] ?? 'N/A';
  const propulsion = chartData.propulsionValues[index];
  const speedType = chartData.speedType;

  let tooltip = `<div style="font-family: Arial, sans-serif; font-size: 13px; color: #222;">`;

  tooltip += `<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">${day}</div>`;
  tooltip += `<hr style="margin:8px 0; border-color: #ddd;" />`;
  tooltip += `<table style="font-size: 12px; color: #555; width: 100%;">`;
  tooltip += `<tbody>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">SFOC:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.sfoc};">${sfoc} g/kWh</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Propulsion Efficiency:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.efficiency};">${propulsion} kg/nm</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Average ${speedType}:</td><td style="padding: 2px 4px;"><span style="color: ${colorMap.speed};">${speed.toFixed(2)} kn</span></td></tr>`;
  tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Accumulated Time:</td><td style="padding: 2px 4px;">${sfocTime}</td></tr>`;
  tooltip += `</tbody>`;
  tooltip += `</table>`;
  tooltip += `</div>`;

  return tooltip;
}
