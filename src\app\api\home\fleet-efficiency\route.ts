import { FleetEfficiencyGetDTO } from '@/features/home-fleet/home-fleet-efficiency.types';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === POST BODY SCHEMAS ===
const vesselSchema = z.object({
  id: z.number(),
  imo: z.number(),
  name: z.string().nonempty('Vessel name is required'),
  assigned_owner_vat: z.string().nonempty('Owner VAT is required'),
});

const filtersSchema = z.object({
  vessels: z.array(vesselSchema).nonempty('At least one vessel must be selected'),
  type: z.literal('fleet'),
  fpOn: z.boolean(),
  fpOff: z.boolean(),
  loadCondition: z.string().nonempty('Load condition is required'),
  beaufort: z.number(),
});

const requestSchema = z.object({
  vessel_id: z.number(),
  vessel_imo: z.number(),
  vessel_name: z.string().nonempty('Vessel name is required'),
  selected_owner_vat: z.string().nonempty('Selected owner VAT is required'),
  from_date: z.string().nonempty('From date is required'),
  to_date: z.string().nonempty('To date is required'),
});

const bodySchema = z.object({
  request: requestSchema,
  filters: filtersSchema,
  owner_vat: z.string().nonempty('Owner VAT is required'),
});

// === POST ENDPOINT ===
export async function POST(request: NextRequest) {
  // Validate body
  const validatedBody = bodySchema.safeParse(await request.json());
  if (!validatedBody.success) {
    return NextResponse.json({ errors: validatedBody.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/efficiency', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.post<FleetEfficiencyGetDTO>(request, url.toString(), validatedBody.data);
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
