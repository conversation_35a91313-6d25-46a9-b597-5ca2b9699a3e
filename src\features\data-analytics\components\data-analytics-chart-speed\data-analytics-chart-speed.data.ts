import { DataAnalyticsResponse } from '../../data-analytics.types';
import { frugalColorMap } from './data-analytics-chart-speed.constants';

export function useChartData(data: DataAnalyticsResponse | null) {
  if (!data) return null;

  const xAxis = data.data_analytics.measurement_graphs.x_axis.calculated_categories;
  const xAxisRaw = data.data_analytics.measurement_graphs.x_axis.raw_categories;
  const lspd = data.data_analytics.measurement_graphs.y_axis.lspd.calculated_data;
  const gspd = data.data_analytics.measurement_graphs.y_axis.gspd.calculated_data;
  const lspd_raw = data.data_analytics.measurement_graphs.y_axis.lspd.raw_data;
  const gspd_raw = data.data_analytics.measurement_graphs.y_axis.gspd.raw_data;
  const frugalStatus = data.data_analytics.frugalStatusTime;

  const sorted = xAxis
    .map((date, i) => ({
      date,
      lspd: lspd[i],
      gspd: gspd[i],
      lspd_raw: lspd_raw?.[i] || null,
      gspd_raw: gspd_raw?.[i] || null,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const lspdSeries = sorted.map((d) => [new Date(d.date).getTime(), d.lspd]);
  const gspdSeries = sorted.map((d) => [new Date(d.date).getTime(), d.gspd]);
  const lspdRawSeries =
    lspd_raw && xAxisRaw
      ? lspd_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const gspdRawSeries =
    gspd_raw && xAxisRaw
      ? gspd_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const plotBands = frugalStatus.map((r) => ({
    from: new Date(r.from).getTime(),
    to: new Date(r.to).getTime(),
    color: frugalColorMap[r.frugal_mode] || 'rgba(0, 0, 0, 0.0)',
  }));

  return {
    lspdSeries,
    gspdSeries,
    lspdRawSeries,
    gspdRawSeries,
    plotBands,
    frugalStatus,
  };
}
