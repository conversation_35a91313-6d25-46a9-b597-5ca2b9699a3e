'use client';

import React from 'react';
import { ChevronsUpDown, Loader2, Ship } from 'lucide-react';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { useVesselStore } from '@/features/vessels/vessel.store';

export function VesselSwitcher() {
  const { isMobile } = useSidebar();
  const { vessels, currentVessel, isLoading, loadVessels, setCurrentVessel } = useVesselStore();

  React.useEffect(() => {
    loadVessels();
  }, []);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton size='lg' className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'>
              <div className='text-muted-foreground flex aspect-square size-8 items-center justify-center rounded-lg border'>
                {isLoading ? <Loader2 className='size-4 animate-spin' /> : <Ship className='size-4' />}
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>{currentVessel?.vessel_name}</span>
                <span className='truncate text-xs'>{currentVessel?.imo}</span>
              </div>
              <ChevronsUpDown className='ml-auto' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='z-401 max-h-[400px] w-[--radix-dropdown-menu-trigger-width] min-w-56 overflow-y-auto rounded-lg'
            align='start'
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className='text-muted-foreground text-xs'>Vessels</DropdownMenuLabel>
            {vessels.map((vessel) => (
              <DropdownMenuItem
                key={vessel.id}
                onClick={() => setCurrentVessel(vessel)}
                className={cn('cursor-pointer gap-2 p-2', currentVessel?.id === vessel.id && 'bg-accent')}
              >
                <div className='flex size-6 items-center justify-center rounded-sm border'>
                  <Ship className='size-4 shrink-0' />
                </div>
                {vessel.vessel_name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
