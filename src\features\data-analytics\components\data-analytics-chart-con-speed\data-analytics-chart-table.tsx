import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export default function DataAnalyticsChartTable({ xValues, yValues, data }: { xValues: string[]; yValues: string[]; data: number[][] }) {
  if (!Array.isArray(xValues) || !Array.isArray(yValues) || !Array.isArray(data)) {
    return <div>Invalid table data</div>;
  }

  return (
    <div className='min-h-100 w-full overflow-x-auto'>
      <Table className='min-w-max'>
        <TableHeader>
          <TableRow>
            <TableHead className='whitespace-nowrap'>Draft</TableHead>
            {xValues.map((x) => (
              <TableHead key={x} className='whitespace-nowrap'>
                {x} kn
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {yValues.map((y, rowIndex) => (
            <TableRow key={y}>
              <TableCell className='font-medium whitespace-nowrap'>{y} m</TableCell>
              {xValues.map((_, colIndex) => (
                <TableCell key={colIndex} className='whitespace-nowrap'>
                  {data[rowIndex]?.[colIndex] ?? '-'} mt
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
