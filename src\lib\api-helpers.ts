import { NextResponse } from 'next/server';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function validateQueryParams(params: Record<string, any>): NextResponse | null {
  const missing = Object.entries(params)
    .filter(([, value]) => value === undefined || value === null || value === '')
    .map(([key]) => key);

  if (missing.length > 0) {
    return NextResponse.json({ error: `Missing required query parameters: ${missing.join(', ')}` }, { status: 400 });
  }

  return null;
}
