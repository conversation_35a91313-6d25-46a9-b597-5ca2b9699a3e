'use client';

import { PageHeader } from '@/components/layout/page-header';
import { HullPerformanceTimeSelector } from '@/features/hull-performance/components/hull-performance-time-selector';
import { SearchButton } from '@/features/hull-performance/components/hull-performance-search-button';
import DailyRateGauge from '@/features/hull-performance/components/hull-performance-daily-rate-gauge';
import FuelConsumptionGauge from '@/features/hull-performance/components/hull-performance-fuel-consumption-gauge';
import ShaftDiffGauge from '@/features/hull-performance/components/hull-performance-shaft-diff-gauge';
import ShaftDiffPercentageGauge from '@/features/hull-performance/components/hull-performance-shaft-diff-percentage-gauge';
import HullPerformanceAiChart from '@/features/hull-performance/components/hull-performance-ai-chart/hull-performance-ai-chart';
import HullPerformanceRawChart from '@/features/hull-performance/components/hull-performance-raw-chart/hull-performance-raw-chart';
import { useHullPerformanceStore } from '@/features/hull-performance/hull-performance.store';
import { useEffect } from 'react';
import { useVesselStore } from '@/features/vessels/vessel.store';

export default function HullPerformancePage() {
  const { currentVessel } = useVesselStore();

  const {
    setInitialHullData,
    setGaugeValues,
    setModelTrainFrom,
    setModelTrainTo,
    setErrorRate,
    setInitialSpeedsList,
    setInitialDateRange,
  } = useHullPerformanceStore();

  // Initialize data on component mount
  useEffect(() => {
    // don’t do anything until we actually know both the IMO and VAT
    if (!currentVessel?.imo || !currentVessel?.assigned_owner_vat) return;

    setInitialHullData(currentVessel.imo, currentVessel.assigned_owner_vat).then(() => {
      setGaugeValues();
      setModelTrainFrom();
      setModelTrainTo();
      setErrorRate();
      setInitialSpeedsList();
      setInitialDateRange();
    });
  }, [
    currentVessel?.imo,
    currentVessel?.assigned_owner_vat,
    setInitialHullData,
    setGaugeValues,
    setModelTrainFrom,
    setModelTrainTo,
    setErrorRate,
    setInitialSpeedsList,
    setInitialDateRange,
  ]);

  return (
    <div className='grid grid-cols-1 gap-4'>
      <div className='flex items-center justify-between'>
        <PageHeader title='Hull Performance' />
        <div className='flex items-center gap-4'>
          <HullPerformanceTimeSelector />
          <SearchButton />
        </div>
      </div>

      {/* Gauges Section */}
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <FuelConsumptionGauge />
        <ShaftDiffGauge />
        <ShaftDiffPercentageGauge />
        <DailyRateGauge />
      </div>

      {/* AI Chart Section */}
      <HullPerformanceAiChart />

      {/* Raw Chart Section */}
      <HullPerformanceRawChart />
    </div>
  );
}
