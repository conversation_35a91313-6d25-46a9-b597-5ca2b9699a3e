'use client';

import { cn } from '@/lib/utils';

type CircleSpinnerProps = {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';
  speed?: string;
};

export default function CircleSpinner({ size = 'md', variant = 'default', speed = '1s' }: CircleSpinnerProps) {
  const sizeClasses = {
    xs: 'w-4 h-4 border-[2px]',
    sm: 'w-6 h-6 border-[3px]',
    md: 'w-12 h-12 border-[5px]',
    lg: 'w-16 h-16 border-[6px]',
    xl: 'w-24 h-24 border-[8px]',
  };

  const variantClasses = {
    default: 'border-white border-b-transparent',
    primary: 'border-primary border-b-transparent',
    info: 'border-blue-500 border-b-transparent',
    success: 'border-green-500 border-b-transparent',
    warning: 'border-yellow-500 border-b-transparent',
    error: 'border-red-500 border-b-transparent',
  };

  return (
    <>
      <div
        className={cn('animate-spin-custom inline-block rounded-full', sizeClasses[size], variantClasses[variant])}
        style={{ animationDuration: speed }}
      />
      <style>{`
        @keyframes spin-custom {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        .animate-spin-custom {
          animation-name: spin-custom;
          animation-timing-function: linear;
          animation-iteration-count: infinite;
        }
      `}</style>
    </>
  );
}
