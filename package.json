{"name": "mydata-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx", "format": "pnpm exec prettier . --write"}, "dependencies": {"@hookform/resolvers": "^5.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.76.0", "@tanstack/react-table": "^8.21.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "leaflet": "^1.9.4", "lucide-react": "^0.482.0", "next": "15.2.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-leaflet": "^5.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.25.1", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.22.0", "eslint-config-next": "15.2.2", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5", "typescript-eslint": "^8.31.0"}}