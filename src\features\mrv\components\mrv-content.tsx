'use client';

import React from 'react';
import { MrvTableSearch } from '@/features/mrv/components/mrv-table/mrv-table-search';
import { MrvTableYearSelector } from '@/features/mrv/components/mrv-table/mrv-table-year-selector';
import { MrvTableTabs } from '@/features/mrv/components/mrv-table/mrv-table-tabs';
import { MrvReportGenerateYearly } from '@/features/mrv/components/mrv-report/mrv-report-generate-yearly';
import { MrvVoyageAdd } from '@/features/mrv/components/mrv-voyage-add/mrv-voyage-add';
import { MrvTable } from '@/features/mrv/components/mrv-table/mrv-table';
import { MrvVoyageDelete } from '@/features/mrv/components/mrv-voyage-delete.ts/mrv-voyage-delete';
import { MrvVoyageEdit } from '@/features/mrv/components/mrv-voyage-edit/mrv-voyage-edit';
import { useMrvStore } from '@/features/mrv/mrv.store';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { MrvReportGenerateExcel } from './mrv-report/mrv-report-generate-excel';

export function MrvContent() {
  // Store
  const { loadVoyages, clearVoyages } = useMrvStore();
  const { year, loadYears } = useMrvTableStore();
  const { currentVessel } = useVesselStore();

  // Load Years
  React.useEffect(() => {
    if (currentVessel?.imo) {
      clearVoyages();
      loadYears();
    }
  }, [currentVessel?.imo]);

  // Load Voyages
  React.useEffect(() => {
    if (currentVessel?.imo && year) {
      clearVoyages();
      loadVoyages();
    }
  }, [year, currentVessel?.imo]);

  return (
    <>
      {/* Top-bar utilities */}
      <div className='mb-2 flex justify-between'>
        <div className='flex gap-2'>
          <MrvTableSearch />
          <MrvTableTabs />
          <MrvTableYearSelector />
        </div>
        <div className='flex gap-2'>
          <MrvReportGenerateExcel />
          <MrvReportGenerateYearly />
          <MrvVoyageAdd />
        </div>
      </div>

      {/* Table */}
      <MrvTable />

      {/* Edit and Delete Modals */}
      <MrvVoyageEdit />
      <MrvVoyageDelete />
    </>
  );
}
