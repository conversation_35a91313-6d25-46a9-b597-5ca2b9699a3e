import { VoyageGetDTO } from './voyage.types';

/**
 * Checks if a voyage has all required fields to be considered complete for MRV reporting
 * Required fields include:
 * - departure_port, destination_port, start_time, end_time
 * - distance > 0
 * - time_sailed (days, hours, or minutes > 0)
 * - fuel consumption (at least one fuel type > 0)
 * - total fuel_consumption > 0
 * - emissions (>= 0)
 * - cargo > 0 (required for MRV reporting)
 * - transport_work > 0 (required for MRV reporting)
 * - voyage_type (non-empty string, required for MRV reporting)
 * @param voyage The voyage to check
 * @returns True if the voyage is complete, false otherwise
 */
export function isVoyageComplete(voyage: Partial<VoyageGetDTO>): boolean {
  // Check for required fields
  if (!voyage.departure_port || voyage.departure_port.trim() === '') {
    return false;
  }

  if (!voyage.destination_port || voyage.destination_port.trim() === '') {
    return false;
  }

  if (!voyage.start_time) {
    return false;
  }

  if (!voyage.end_time) {
    return false;
  }

  if (!voyage.distance || voyage.distance <= 0) {
    return false;
  }

  // Check for time sailed
  if (
    (voyage.time_sailed_days === undefined || voyage.time_sailed_days <= 0) &&
    (voyage.time_sailed_hours === undefined || voyage.time_sailed_hours <= 0) &&
    (voyage.time_sailed_minutes === undefined || voyage.time_sailed_minutes <= 0)
  ) {
    return false;
  }

  // Check for at least one fuel type with consumption
  const hasFuelConsumption =
    (voyage.fuel_consumption_HFO !== undefined && voyage.fuel_consumption_HFO > 0) ||
    (voyage.fuel_consumption_LFO !== undefined && voyage.fuel_consumption_LFO > 0) ||
    (voyage.fuel_consumption_MDO !== undefined && voyage.fuel_consumption_MDO > 0) ||
    (voyage.fuel_consumption_hfo !== undefined && voyage.fuel_consumption_hfo > 0) ||
    (voyage.fuel_consumption_lfo !== undefined && voyage.fuel_consumption_lfo > 0) ||
    (voyage.fuel_consumption_mdo !== undefined && voyage.fuel_consumption_mdo > 0) ||
    (voyage.fuel_consumption_mgo !== undefined && voyage.fuel_consumption_mgo > 0);

  if (!hasFuelConsumption) {
    return false;
  }

  // Check for total consumption
  if (!voyage.fuel_consumption || voyage.fuel_consumption <= 0) {
    return false;
  }

  // Check for total emissions
  if (!voyage.emissions && voyage.emissions !== 0) {
    return false;
  }

  // Check for cargo (required for MRV reporting)
  if (!voyage.cargo || voyage.cargo <= 0) {
    return false;
  }

  // Check for transport work (required for MRV reporting)
  if (!voyage.transport_work || voyage.transport_work <= 0) {
    return false;
  }

  // Check for voyage type (required for MRV reporting)
  if (!voyage.voyage_type || voyage.voyage_type.trim() === '') {
    return false;
  }

  return true;
}
