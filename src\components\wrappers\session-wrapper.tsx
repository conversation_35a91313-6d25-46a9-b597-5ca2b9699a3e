/* eslint-disable boundaries/no-unknown-files */
'use client';

import { SessionProvider } from 'next-auth/react';
import { ReactNode } from 'react';
import { Session } from 'next-auth';
interface SessionWrapperProps {
  children: ReactNode;
  session?: Session;
}

const SessionWrapper = ({ children, session }: SessionWrapperProps) => {
  return <SessionProvider session={session}>{children}</SessionProvider>;
};

export default SessionWrapper;
