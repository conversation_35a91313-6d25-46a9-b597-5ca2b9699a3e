import { defineConfig } from 'eslint/config';
import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';
import boundaries from 'eslint-plugin-boundaries';

export default defineConfig([
  {
    ignores: ['**/node_modules/**', '**/.next/**', '**/dist/**', '**/out/**', '**/build/**'],
  },
  tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'],
    plugins: {
      boundaries,
      js,
    },
    languageOptions: { globals: globals.browser },
    rules: {
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/no-unused-vars': ['error', { caughtErrorsIgnorePattern: '^_', argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      'boundaries/no-unknown': ['error'],
      'boundaries/no-unknown-files': ['error'],
      'boundaries/element-types': [
        'error',
        {
          default: 'disallow',
          rules: [
            { from: 'app', allow: ['features', 'layout', 'hooks', 'styles', 'lib', 'ui', 'ui-extensions'] },
            { from: 'layout', allow: ['ui', 'lib'] },
            { from: 'features', allow: ['ui', 'ui-extensions', 'hooks', 'lib', 'styles', 'data'] },
            { from: 'features/*', allow: ['ui', 'ui-extensions', 'hooks', 'lib', 'styles', 'data', 'features/*'] },
            { from: 'ui-extensions', allow: ['ui', 'lib'] },
            { from: 'ui', allow: ['ui', 'lib', 'hooks'] },
            { from: 'hooks', allow: [] },
          ],
        },
      ],
    },
    settings: {
      'boundaries/include': ['src/**'],
      'boundaries/elements': [
        { type: 'app', pattern: 'src/app/**' },
        { type: 'layout', pattern: 'src/components/layout/**' },
        { type: 'features', pattern: 'src/features/**' },
        { type: 'features/*', pattern: 'src/features/*' },
        { type: 'ui-extensions', pattern: 'src/components/ui-extensions/**' },
        { type: 'ui', pattern: 'src/components/ui/**' },
        { type: 'hooks', pattern: 'src/hooks/**' },
        { type: 'lib', pattern: 'src/lib/**' },
        { type: 'styles', pattern: 'src/styles/**' },
        { type: 'data', pattern: 'src/data/**' },
      ],
    },
  },
]);
