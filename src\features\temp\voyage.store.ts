import { create } from 'zustand';
import axios from 'axios';
import { VoyageGetDTO, VoyagePutDTO } from './voyage.types';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { toast } from 'sonner';

interface VoyageStore {
  voyages: VoyageGetDTO[];
  filteredVoyages: VoyageGetDTO[];
  searchQuery: string;
  selectedTab: string;
  pageSize: number;
  pageIndex: number;
  isLoading: boolean;
  year: string;
  availableYears: string[];
  mrvDataKeys: string[];
  mrvDataCopyKeys: string[];

  setSearchQuery: (query: string) => void;
  setSelectedTab: (tab: string) => void;
  setPageSize: (size: number) => void;
  setPageIndex: (index: number) => void;
  setVoyages: (voyages: VoyageGetDTO[]) => void;
  setYear: (year: string) => void;

  loadVoyages: () => Promise<void>;
  loadYears: () => Promise<void>;
  createVoyage: (createdVoyageData: Partial<VoyagePutDTO>) => Promise<void>;
  updateVoyage: (id: number, updatedData: Partial<VoyagePutDTO>) => Promise<void>;
  deleteVoyage: (id: number) => Promise<void>;
}

export const useVoyageStore = create<VoyageStore>((set, get) => ({
  voyages: [],
  filteredVoyages: [],
  searchQuery: '',
  selectedTab: 'all',
  pageSize: 10,
  pageIndex: 0,
  isLoading: false,
  year: '',
  availableYears: [] as string[],
  mrvDataKeys: [] as string[],
  mrvDataCopyKeys: [] as string[],

  loadYears: async () => {
    set({ isLoading: true });
    try {
      const currentVessel = useVesselStore.getState().currentVessel;
      if (!currentVessel?.imo) throw new Error('No vessel selected or vessel has no IMO');

      const { data: years } = await axios.get('/api/mrv/years', {
        params: {
          vessel_imo: currentVessel.imo,
          owner_vat: currentVessel.assigned_owner_vat,
        },
      });

      set({ availableYears: years, year: years[years.length - 1] });
    } catch (error) {
      console.error('Error loading years:', error);
      toast.error('Error loading years');
    } finally {
      set({ isLoading: false });
    }
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query, pageIndex: 0 });

    const { voyages, selectedTab } = get();
    const lowerCaseQuery = query.toLowerCase();

    let filtered = voyages.filter(
      (voyage) =>
        voyage.departure_port.toLowerCase().includes(lowerCaseQuery) || voyage.destination_port.toLowerCase().includes(lowerCaseQuery)
    );

    if (selectedTab === 'incomplete') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Incomplete');
    } else if (selectedTab === 'complete') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Complete');
    } else if (selectedTab === 'error') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Error');
    } else if (selectedTab === 'approved') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Approved');
    }

    set({ filteredVoyages: filtered });
  },

  setSelectedTab: (tab) => {
    set({ selectedTab: tab, pageIndex: 0 });

    const { voyages, searchQuery } = get();
    let filtered = [...voyages];

    if (tab === 'incomplete') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Incomplete');
    } else if (tab === 'complete') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Complete');
    } else if (tab === 'error') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Error');
    } else if (tab === 'approved') {
      filtered = filtered.filter((voyage) => voyage.voyage_status === 'Approved');
    }

    const lowerCaseQuery = searchQuery.toLowerCase();
    filtered = filtered.filter(
      (voyage) =>
        voyage.departure_port.toLowerCase().includes(lowerCaseQuery) || voyage.destination_port.toLowerCase().includes(lowerCaseQuery)
    );

    set({ filteredVoyages: filtered });
  },

  setPageSize: (size) => set({ pageSize: size, pageIndex: 0 }),

  setPageIndex: (index) => set({ pageIndex: index }),

  setVoyages: (voyages: VoyageGetDTO[]) => set({ voyages, filteredVoyages: voyages }),

  setYear: (year: string) => set({ year }),

  loadVoyages: async () => {
    set({ isLoading: true, filteredVoyages: [], voyages: [] });
    try {
      // Get the vessel store
      const vesselStore = useVesselStore.getState();

      // If vessels aren't loaded yet or there's no current vessel, load vessels first
      if (vesselStore.vessels.length === 0 || !vesselStore.currentVessel) {
        await vesselStore.loadVessels();
      }

      // Get the current vessel after ensuring it's loaded
      const currentVessel = useVesselStore.getState().currentVessel;

      if (!currentVessel?.imo) {
        throw new Error('No vessel selected or vessel has no IMO');
      }

      // Fetch MRV data
      const { data: response } = await axios.get('/api/mrv', {
        params: {
          vessel_imo: currentVessel.imo,
          year: get().year,
          owner_vat: currentVessel.assigned_owner_vat,
        },
      });

      const responseMrvDataKeys = Object.keys(response.mrv_data);
      const responseMrvDataCopyKeys = Object.keys(response.mrv_data_copy);
      set({
        mrvDataKeys: responseMrvDataKeys,
        mrvDataCopyKeys: responseMrvDataCopyKeys,
      });

      const mrvDataToUse = Object.keys(response.mrv_data_copy ?? {}).length > 0 ? response.mrv_data_copy! : (response.mrv_data ?? {});

      // Convert the mrv_data object to an array of voyages
      const voyages: VoyageGetDTO[] = Object.entries(mrvDataToUse).map(([uuid, data]) => {
        // Cast the data to any to work with it more easily
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const voyageData = data as any;

        // Calculate emissions based on fuel consumption
        const calculateEmissions = (fuelType: string, consumption: number): number => {
          if (!consumption) return 0;
          const coefficients: Record<string, number> = {
            hfo: 3.114,
            lfo: 3.151,
            mdo: 3.206,
            mgo: 3.206, // Using same coefficient as MDO if MGO is used
          };
          return Number((consumption * (coefficients[fuelType.toLowerCase()] || 0)).toFixed(2));
        };

        // Calculate emissions for each fuel type
        const hfoEmissions = calculateEmissions('hfo', voyageData.fuel_consumption_HFO || 0);
        const lfoEmissions = calculateEmissions('lfo', voyageData.fuel_consumption_LFO || 0);
        const mdoEmissions = calculateEmissions('mdo', voyageData.fuel_consumption_MDO || 0);

        // Calculate total emissions
        const totalEmissions = hfoEmissions + lfoEmissions + mdoEmissions;

        // Use backend-provided voyage_status directly
        return {
          uuid,
          id: parseInt(uuid.substring(0, 8), 16),
          aft_draft: voyageData.aft_draft,
          arrival_count: voyageData.arrival_count,
          arrival_location: voyageData.arrival_location,
          aux_consumption: voyageData.aux_consumption,
          boiler_consumption: voyageData.boiler_consumption,
          cargo_type: voyageData.cargo_type || null,
          departure_location: voyageData.departure_location || '',
          departure_port: voyageData.departure_port || '',
          description: voyageData.description || null,
          destination_port: voyageData.destination_port || '',
          distance: voyageData.distance || 0,
          emission_percentage_for_mrv: voyageData.emission_percentage_for_mrv,
          end_sailing_event: voyageData.end_sailing_event,
          end_time: voyageData.end_time || '',
          errors: voyageData.errors || [],
          eu_heading: voyageData.eu_heading,
          eu_mrv_count: voyageData.eu_mrv_count,
          forward_draft: voyageData.forward_draft,
          fuel_consumption: voyageData.fuel_consumption || 0,
          fuel_consumption_HFO: voyageData.fuel_consumption_HFO || 0,
          fuel_consumption_LFO: voyageData.fuel_consumption_LFO || 0,
          fuel_consumption_MDO: voyageData.fuel_consumption_MDO || 0,
          fuel_consumption_at_berth: voyageData.fuel_consumption_at_berth || 0,
          fuel_consumption_at_sea: voyageData.fuel_consumption_at_sea || 0,
          fuel_types: voyageData.fuel_types || [],
          invalid_keys: voyageData.invalid_keys || [],
          mrv_fuel_consumption: voyageData.mrv_fuel_consumption || 0,
          navigation_statuses: voyageData.navigation_statuses || {},
          ongoing: voyageData.ongoing || false,
          start_sailing_event: voyageData.start_sailing_event,
          start_time: voyageData.start_time || '',
          time_at_berth_days: voyageData.time_at_berth_days || 0,
          time_at_berth_hours: voyageData.time_at_berth_hours || 0,
          time_at_berth_minutes: voyageData.time_at_berth_minutes || 0,
          time_at_sea_days: voyageData.time_at_sea_days || 0,
          time_at_sea_hours: voyageData.time_at_sea_hours || 0,
          time_at_sea_minutes: voyageData.time_at_sea_minutes || 0,
          time_in_port_days: voyageData.time_in_port_days,
          time_in_port_hours: voyageData.time_in_port_hours,
          time_in_port_minutes: voyageData.time_in_port_minutes,
          time_sailed_days: voyageData.time_sailed_days || 0,
          time_sailed_hours: voyageData.time_sailed_hours || 0,
          time_sailed_minutes: voyageData.time_sailed_minutes || 0,
          transport_work: voyageData.transport_work,
          uk_heading: voyageData.uk_heading,
          uk_mrv_count: voyageData.uk_mrv_count,
          voyage_id: voyageData.voyage_id,
          voyage_status: voyageData.voyage_status, // Use backend status
          voyage_type: voyageData.voyage_type || null,
          approved_mrv_voyage: voyageData.approved_mrv_voyage || false,

          // Form compatibility fields
          cargo: voyageData.cargo || 0,
          emissions: totalEmissions,
          emissions_hfo: hfoEmissions,
          emissions_lfo: lfoEmissions,
          emissions_mdo: mdoEmissions,
          emissions_mgo: 0, // Not used in the API response
          fuel_consumption_hfo: voyageData.fuel_consumption_HFO || 0,
          fuel_consumption_lfo: voyageData.fuel_consumption_LFO || 0,
          fuel_consumption_mdo: voyageData.fuel_consumption_MDO || 0,
          fuel_consumption_mgo: 0, // Not used in the API response
          approved_mrv: voyageData.approved_mrv_voyage || false,
        } as VoyageGetDTO;
      });

      // No need to check for period overlaps or completeness here, as backend provides status
      const voyagesWithStatus = voyages;

      const { selectedTab, searchQuery } = get();

      let filtered = [...voyagesWithStatus];

      // Apply tab filter
      if (selectedTab === 'incomplete') {
        filtered = filtered.filter((voyage) => voyage.voyage_status === 'Incomplete');
      } else if (selectedTab === 'complete') {
        filtered = filtered.filter((voyage) => voyage.voyage_status === 'Complete');
      } else if (selectedTab === 'error') {
        filtered = filtered.filter((voyage) => voyage.voyage_status === 'Error');
      } else if (selectedTab === 'approved') {
        filtered = filtered.filter((voyage) => voyage.voyage_status === 'Approved');
      }

      // Apply search query
      const lowerCaseQuery = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (voyage) =>
          voyage.departure_port.toLowerCase().includes(lowerCaseQuery) || voyage.destination_port.toLowerCase().includes(lowerCaseQuery)
      );

      set({ voyages: voyagesWithStatus, filteredVoyages: filtered });
    } catch (error) {
      console.error('Error loading voyages:', error);
      toast.error('Error loading voyages');
    } finally {
      set({ isLoading: false });
    }
  },

  createVoyage: async (createdVoyageData) => {
    set({ isLoading: true });
    try {
      const currentVessel = useVesselStore.getState().currentVessel;
      if (!currentVessel?.imo) throw new Error('No vessel selected or vessel has no IMO');

      const year = get().year;

      const requestBody = {
        voyage_data: {
          ...createdVoyageData,
        },
      };

      // Build URL with the three params
      const url =
        `/api/mrv/voyage` +
        `?vessel_imo=${encodeURIComponent(currentVessel.imo)}` +
        `&year=${encodeURIComponent(year)}` +
        `&owner_vat=${encodeURIComponent(currentVessel.assigned_owner_vat)}`;

      await axios.post(url, requestBody);

      // After creating a voyage, reload all voyages to get the updated data
      await get().loadVoyages();
      toast.success('Voyage created successfully');
    } catch (error) {
      console.error('Error adding voyage:', error);
      toast.error('Failed to create voyage');
    } finally {
      set({ isLoading: false });
    }
  },

  updateVoyage: async (id, updatedData) => {
    set({ isLoading: true });
    try {
      const voyage = get().voyages.find((v) => v.id === id);
      if (!voyage) throw new Error('Voyage not found');

      const vesselStore = useVesselStore.getState();
      if (vesselStore.vessels.length === 0 || !vesselStore.currentVessel) {
        await vesselStore.loadVessels();
      }
      const currentVessel = useVesselStore.getState().currentVessel;
      if (!currentVessel?.imo) throw new Error('No vessel selected or vessel has no IMO');

      const year = get().year;

      // Only voyage_data goes in the body:
      const requestBody = {
        voyage_data: {
          ...updatedData,
        },
      };

      // Build URL with the three params
      const url =
        `/api/mrv/voyage` +
        `?vessel_imo=${encodeURIComponent(currentVessel.imo)}` +
        `&year=${encodeURIComponent(year)}` +
        `&voyage_id=${encodeURIComponent(voyage.uuid)}` +
        `&owner_vat=${encodeURIComponent(currentVessel.assigned_owner_vat)}`;

      await axios.put(url, requestBody);

      // reload voyages after update
      await get().loadVoyages();
      toast.success('Voyage updated successfully');
    } catch (error) {
      console.error('Error editing voyage:', error);
      toast.error('Failed to update voyage');
    } finally {
      set({ isLoading: false });
    }
  },

  deleteVoyage: async (id) => {
    set({ isLoading: true });
    try {
      // Find the voyage with the given ID
      const voyage = get().voyages.find((v) => v.id === id);

      if (!voyage) {
        throw new Error('Voyage not found');
      }

      const currentVessel = useVesselStore.getState().currentVessel;

      if (!currentVessel?.imo) {
        throw new Error('No vessel selected or vessel has no IMO');
      }

      // Get the current year
      const year = get().year;

      const mrvDataKeys = get().mrvDataKeys;
      const mrvDataCopyKeys = get().mrvDataCopyKeys;

      const is_new_voyage = mrvDataCopyKeys.includes(voyage.uuid) && !mrvDataKeys.includes(voyage.uuid);

      // Build URL with the three params
      const url =
        `/api/mrv/voyage` +
        `?vessel_imo=${encodeURIComponent(currentVessel.imo)}` +
        `&year=${encodeURIComponent(year)}` +
        `&voyage_id=${encodeURIComponent(voyage.uuid)}` +
        `&is_new_voyage=${encodeURIComponent(is_new_voyage)}` +
        `&owner_vat=${encodeURIComponent(currentVessel.assigned_owner_vat)}`;

      await axios.delete(url);
      await get().loadVoyages();
      toast.success('Voyage deleted successfully');
    } catch (error) {
      console.error('Error deleting voyage:', error);
      toast.error('Failed to delete voyage');
    } finally {
      set({ isLoading: false });
    }
  },
}));
