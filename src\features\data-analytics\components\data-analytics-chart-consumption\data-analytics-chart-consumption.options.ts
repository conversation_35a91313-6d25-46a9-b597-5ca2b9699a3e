import { colorMap, frugalColorMap, frugalModeLabels } from './data-analytics-chart-consumption.constants';
import Highcharts from 'highcharts';
import { FrugalStatusTime } from '../../data-analytics.types';
import { PlotBands, Series } from './data-analytics-chart-consumption.types';

export function getChartOptions({
  mefcmSeries,
  boifcmSeries,
  auxfcmSeries,
  mefcmRawSeries,
  boifcmRawSeries,
  auxfcmRawSeries,
  plotBands,
  frugalStatus,
  chartId,
  allChartRefs,
  isSyncing,
}: {
  mefcmSeries: Series;
  boifcmSeries: Series;
  auxfcmSeries: Series;
  mefcmRawSeries?: Series | null;
  boifcmRawSeries?: Series | null;
  auxfcmRawSeries?: Series | null;
  plotBands: PlotBands[];
  frugalStatus: FrugalStatusTime[];
  chartId: string;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
  isSyncing: boolean;
}): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      name: 'Main Engine Fuel Consumption (kg/h)',
      data: mefcmSeries,
      type: 'line',
      color: colorMap.mefcm,
      yAxis: 0,
      marker: { enabled: false },
    },
    {
      name: 'Boiler Fuel Consumption (kg/h)',
      data: boifcmSeries,
      type: 'line',
      color: colorMap.boifcm,
      yAxis: 1,
      marker: { enabled: false },
    },
    {
      name: 'Auxiliary Fuel Consumption (kg/h)',
      data: auxfcmSeries,
      type: 'line',
      color: colorMap.auxfcm,
      yAxis: 2,
      marker: { enabled: false },
    },
  ];

  if (mefcmRawSeries) {
    series.push({
      name: 'Main Engine Fuel Consumption (kg/h) (Raw)',
      data: mefcmRawSeries,
      type: 'line',
      color: colorMap.mefcm_raw,
      yAxis: 0,
      dashStyle: 'ShortDot',
      stickyTracking: false,
      marker: { enabled: false },
    });
  }

  if (boifcmRawSeries) {
    series.push({
      name: 'Boiler Fuel Consumption (kg/h) (Raw)',
      data: boifcmRawSeries,
      type: 'line',
      color: colorMap.boifcm_raw,
      yAxis: 1,
      dashStyle: 'ShortDot',
      stickyTracking: false,
      marker: { enabled: false },
    });
  }

  if (auxfcmRawSeries) {
    series.push({
      name: 'Auxiliary Fuel Consumption (kg/h) (Raw)',
      data: auxfcmRawSeries,
      type: 'line',
      color: colorMap.auxfcm_raw,
      yAxis: 2,
      dashStyle: 'ShortDot',
      stickyTracking: false,
      marker: { enabled: false },
    });
  }

  return {
    chart: {
      marginLeft: 78,
      marginRight: 108,
      zooming: {
        type: 'x',
      },
    },
    title: { text: '' },
    xAxis: {
      type: 'datetime',
      plotBands,
      max: new Date().getTime(),
      crosshair: true,
      labels: { style: { color: 'black' } },
      events: {
        setExtremes(e: Highcharts.AxisSetExtremesEventObject) {
          if (isSyncing) return;

          isSyncing = true;
          Object.entries(allChartRefs).forEach(([key, ref]) => {
            if (key !== chartId && ref.current) {
              ref.current.xAxis[0].setExtremes(e.min, e.max, true, false);
            }
          });
          isSyncing = false;
        },
      },
    },
    yAxis: [
      {
        title: { text: 'Main Engine Fuel Consumption (kg/h)', style: { color: colorMap.mefcm } },
        labels: { style: { color: colorMap.mefcm } },
      },
      {
        title: { text: 'Boiler Fuel Consumption (kg/h)', style: { color: colorMap.boifcm } },
        labels: { style: { color: colorMap.boifcm } },
        opposite: true,
      },
      {
        title: { text: 'Auxiliary Fuel Consumption (kg/h)', style: { color: colorMap.auxfcm } },
        labels: { style: { color: colorMap.auxfcm } },
        opposite: true,
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltip(this, frugalStatus);
      },
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          align: 'right',
          verticalAlign: 'top',
          menuItems: [
            'viewFullscreen',
            'printChart',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS',
          ],
        },
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
      x: -40,
      y: -4,
    },
    credits: { enabled: false },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
            },
          },
        },
      ],
    },
  };
}

export function formatTooltip(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number }[] },
  frugalStatusData: FrugalStatusTime[]
) {
  const x = ctx.x;
  const points = ctx.points;

  const frugal = frugalStatusData.find((r) => new Date(r.from).getTime() <= x && new Date(r.to).getTime() >= x);
  const mode = frugal?.frugal_mode ?? -1;
  const label = frugalModeLabels[mode] || 'Idle';
  const color = frugalColorMap[mode] || '#ccc';

  const lines = points.map((p) => `<span style="color:${p.color}">\u25CF</span> ${p.series.name}: <b>${p.y}</b>`);

  return `
    <div style="font-size: 13px">
      <div style="font-weight: bold; margin-bottom: 4px;">
        ${Highcharts.dateFormat('%A, %b %e, %H:%M', x)}
      </div>
      ${lines.join('<br/>')}
      <hr style="margin: 6px 0;" />
      <span style="color:${color}">\u25CF</span> <b>Frugal Mode:</b> ${label}
    </div>
  `;
}
