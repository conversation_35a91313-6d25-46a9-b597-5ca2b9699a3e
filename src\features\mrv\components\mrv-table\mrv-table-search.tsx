'use client';

import { Input } from '@/components/ui/input';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';

export function MrvTableSearch() {
  // Store & state
  const { searchQuery, setSearchQuery } = useMrvTableStore();

  // Handle input change
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchQuery(value);
  };

  return <Input type='text' placeholder='Search by port...' value={searchQuery} onChange={handleInputChange} className='w-80' />;
}
