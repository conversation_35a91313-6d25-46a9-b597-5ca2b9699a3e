import { FleetVesselsResponse } from '@/features/home-fleet/fleet.types';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Build URL
  const url = new URL('/home/<USER>', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.get<FleetVesselsResponse>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
