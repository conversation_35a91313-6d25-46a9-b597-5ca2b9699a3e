'use client';

import React, { useRef } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { handleNoSeriesEnabled, handleShowAllSeries } from './anomaly-detection-overview-chart-no-series';

interface ChartRendererProps {
  chartOptions: Highcharts.Options;
  chartKey: number;
  hasVisibleSeries: boolean;
  userDisabledSeries: boolean;
  hasData: boolean;
  onChartReady?: (chart: Highcharts.Chart) => void;
  setHasVisibleSeries: (hasVisibleSeries: boolean) => void;
  setUserDisabledSeries: (userDisabledSeries: boolean) => void;
}

export default function ChartRenderer({
  chartOptions,
  chartKey,
  hasVisibleSeries,
  userDisabledSeries,
  hasData,
  onChartReady,
  setHasVisibleSeries,
  setUserDisabledSeries,
}: ChartRendererProps) {
  const chartRef = useRef<Highcharts.Chart | null>(null);

  // Create enhanced chart options with event handlers
  const enhancedOptions: Highcharts.Options = {
    ...chartOptions,
    chart: {
      ...chartOptions.chart,
      events: {
        render: function (this: Highcharts.Chart) {
          // Get the chart container
          const container = this.container;
          if (!container) return;

          if (!hasData) {
            // If no data is available, the parent component handles this with NoDataPlaceholder
            return;
          } else if (!hasVisibleSeries && userDisabledSeries) {
            // If user has disabled all series, show the no series enabled placeholder
            handleNoSeriesEnabled({ container });
          } else {
            // Otherwise show all chart elements and hide placeholders
            handleShowAllSeries({ container });
          }
        },
        load: function (this: Highcharts.Chart) {
          chartRef.current = this;
          onChartReady?.(this);
        },
      },
    },
    plotOptions: {
      ...chartOptions.plotOptions,
      series: {
        ...chartOptions.plotOptions?.series,
        events: {
          ...chartOptions.plotOptions?.series?.events,
          // Track when user manually toggles series visibility
          hide: function () {
            setUserDisabledSeries(true);

            // Check if any series are still visible
            if (chartRef.current) {
              try {
                const anySeriesVisible = chartRef.current.series && Array.from(chartRef.current.series).some((s) => s.visible);
                setHasVisibleSeries(anySeriesVisible || false);
              } catch (_) {
                // If we can't check series visibility, assume there are visible series
                setHasVisibleSeries(true);
              }
            }
          },
          show: function () {
            // When a series is shown, we know at least one series is visible
            setHasVisibleSeries(true);
          },
        },
      },
    },
  };

  return <HighchartsReact key={chartKey} highcharts={Highcharts} options={enhancedOptions} />;
}
