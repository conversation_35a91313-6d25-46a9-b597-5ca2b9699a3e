import { create } from 'zustand';
import { MrvVoyage } from '../../mrv.types';

interface MrvVoyageDeleteStore {
  isOpen: boolean;
  voyage: MrvVoyage | null;
  openDialog: (voyage: MrvVoyage) => void;
  closeDialog: () => void;
}

export const useMrvVoyageDeleteStore = create<MrvVoyageDeleteStore>((set) => ({
  isOpen: false,
  voyage: null,

  openDialog: (voyage: MrvVoyage) => set({ isOpen: true, voyage: voyage }),
  closeDialog: () => set({ isOpen: false, voyage: null }),
}));
