'use client';

import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { useDataAnalyticsStore } from '../../data-analytics.store';
import { useChartData } from './data-analytics-chart-performance.data';
import { getChartOptions } from './data-analytics-chart-performance.options';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import { Switch } from '@/components/ui/switch';

interface Props {
  chartId: string;
  chartRef: React.RefObject<Highcharts.Chart | null>;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
}

// Used in getChartOptions(). Is actually a let (not const)
const isSyncing = false;

export default function DataAnalyticsChartPerformance({ chartId, chartRef, allChartRefs }: Props) {
  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const [showFrugal, setShowFrugal] = React.useState(true);
  const chartData = useChartData(dataAnalytics);

  // Dynamically load Highcharts modules
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const loadModules = async () => {
        try {
          const exportingModule = (await import('highcharts/modules/exporting')) as typeof import('highcharts/modules/exporting');
          if (typeof exportingModule === 'function') {
            (exportingModule as unknown as (highcharts: typeof Highcharts) => void)(Highcharts);
          } else if (typeof exportingModule.default === 'function') {
            exportingModule.default(Highcharts);
          }
        } catch (error) {
          console.error('Failed to load Highcharts modules:', error);
        }
      };

      loadModules();
    }
  }, []);

  const isEmpty = !isLoading && (!chartData || !chartData.merpmSeries?.length || !chartData.spowSeries?.length);

  const options = chartData
    ? getChartOptions({
        ...chartData,
        plotBands: showFrugal ? chartData.plotBands : [],
        chartId,
        allChartRefs,
        isSyncing,
      })
    : undefined;

  return (
    <div className='bg-card text-card-foreground relative min-h-[300px] rounded-md border p-4'>
      <div className='mb-4 flex flex-col gap-1 md:flex-row md:items-center md:justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Main Engine RPM And Shaft Power (kW)</div>
        <div className='relative right-3 flex items-center gap-2'>
          <Switch checked={showFrugal} onCheckedChange={() => setShowFrugal(!showFrugal)} />
          <label className='text-sm'>Display Frugal Mode</label>
        </div>
      </div>
      {isLoading ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        options && (
          <HighchartsReact
            key={JSON.stringify(options)}
            highcharts={Highcharts}
            options={options}
            callback={(chart: Highcharts.Chart) => {
              chartRef.current = chart;
            }}
          />
        )
      )}
    </div>
  );
}
