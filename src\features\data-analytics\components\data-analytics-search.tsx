'use client';

import { DateRangePicker } from '@/components/ui-extensions/date-range-picker';
import { Button } from '@/components/ui/button';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { Loader2, Search } from 'lucide-react';
import React, { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useDataAnalyticsStore } from '../data-analytics.store';

function formatDateTime(date: Date): string {
  const pad = (n: number) => String(n).padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
export default function DataAnalyticsSearch() {
  const { currentVessel } = useVesselStore();
  const { fetchDataAnalytics, isLoading } = useDataAnalyticsStore();

  const [dateRange, setDateRange] = useState(() => {
    const to = new Date();
    to.setHours(23, 59, 59, 999);

    const from = new Date();
    from.setDate(from.getDate() - 14);
    from.setHours(0, 0, 0, 0);

    return { from, to };
  });

  React.useEffect(() => {
    if (currentVessel !== null) {
      fetchDataAnalytics(formatDateTime(dateRange.from), formatDateTime(dateRange.to));
    }
  }, [currentVessel, fetchDataAnalytics]);

  if (currentVessel === null) {
    return (
      <div className='flex gap-4'>
        <div className='bg-muted h-10 w-[240px] animate-pulse rounded-md' />
        <div className='bg-muted h-10 w-10 animate-pulse rounded-md' />
      </div>
    );
  }

  const handleDateChange = (dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange?.to) {
      const normalized = {
        from: new Date(dateRange.from.setHours(0, 0, 0, 0)),
        to: new Date(dateRange.to.setHours(23, 59, 59, 999)),
      };
      setDateRange(normalized);
    }
  };

  const handleClick = () => {
    fetchDataAnalytics(formatDateTime(dateRange.from), formatDateTime(dateRange.to));
  };

  return (
    <div className='flex gap-4'>
      <DateRangePicker
        maxDate={new Date(currentVessel.available_dates[0].timestamp)}
        minDate={new Date(currentVessel.available_dates[1].timestamp)}
        value={dateRange}
        onChange={(range) => handleDateChange(range)}
      />
      <Button variant='default' onClick={handleClick} disabled={isLoading}>
        {isLoading ? <Loader2 className='animate-spin' /> : <Search />}
      </Button>
    </div>
  );
}
