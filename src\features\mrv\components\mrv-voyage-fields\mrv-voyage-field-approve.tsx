import { useEffect } from 'react';
import { useFormContext, Control, Path, FieldValues, PathValue } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';

export interface MrvVoyageFieldApproveProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
}

export function MrvVoyageFieldApprove<T extends FieldValues>({ control, name }: MrvVoyageFieldApproveProps<T>) {
  const { formState, setValue } = useFormContext<T>();
  const isValid = formState.isValid;

  useEffect(() => {
    if (!isValid) {
      setValue(name, false as PathValue<T, Path<T>>);
    }
  }, [isValid, setValue]);

  return (
    <TooltipProvider>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              {isValid ? (
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={(checked) => field.onChange(!!checked)} disabled={!isValid} />
                </FormControl>
              ) : (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={(checked) => field.onChange(!!checked)} disabled />
                    </FormControl>
                  </TooltipTrigger>
                  <TooltipContent side='top' sideOffset={4}>
                    <p>Fill out all required fields before approving the voyage for MRV.</p>
                  </TooltipContent>
                </Tooltip>
              )}
              <div className='leading-none'>
                <FormLabel className='cursor-help'>Approved MRV Voyage</FormLabel>
                <p className='text-muted-foreground text-sm'>Approve the voyage for MRV</p>
              </div>
            </div>
          </FormItem>
        )}
      />
    </TooltipProvider>
  );
}
