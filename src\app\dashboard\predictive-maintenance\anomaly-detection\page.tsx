import { PageHeader } from '@/components/layout/page-header';
import { AnomalyDetectionSensorPicker } from '@/features/anomaly-detection/components/anomaly-detection-sensor-picker';
import { AnomalyDetectionOverviewTable } from '@/features/anomaly-detection/components/anomaly-detection-overview-table/anomaly-detection-overview-table';
import AnomalyDetectionOverview<PERSON>hart from '@/features/anomaly-detection/components/anomaly-detection-overview-chart/anomaly-detection-overview-chart';
import { AnomalyDetectionDataProvider } from '@/features/anomaly-detection/components/anomaly-detection-data-provider';

export default function AnomalyDetectionPage() {
  return (
    <AnomalyDetectionDataProvider>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-col justify-between xl:flex-row xl:items-center'>
          <PageHeader title='Anomaly Detection' />
          <div className='flex flex-wrap gap-4'>
            <AnomalyDetectionSensorPicker />
          </div>
        </div>
        <AnomalyDetectionOverviewTable />
        <AnomalyDetectionOverviewChart />
      </div>
    </AnomalyDetectionDataProvider>
  );
}
