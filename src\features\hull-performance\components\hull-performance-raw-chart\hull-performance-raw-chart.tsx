'use client';

import React, { useEffect, useState } from 'react';
import Highcharts from 'highcharts';
import { useHullPerformanceStore } from '../../hull-performance.store';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createChartOptions } from './hull-performance-raw-chart-config';
import ChartRenderer from './hull-performance-raw-chart-renderer';

export default function HullPerformanceRawChart() {
  const { dataRaw, searchData, useSearchData, speedsList, selectedSpeed, setSelectedSpeed, isLoading, hasRawChartData } =
    useHullPerformanceStore();
  const [modulesLoaded, setModulesLoaded] = useState(false);
  const [chartRef, setChartRef] = useState<Highcharts.Chart | null>(null);
  const [hasVisibleSeries, setHasVisibleSeries] = useState(true);
  const [userDisabledSeries, setUserDisabledSeries] = useState(false);

  // Determine which data to use based on the useSearchData flag
  const hasSearchData = searchData && Object.keys(searchData).length > 0;
  const chartData = useSearchData && hasSearchData ? searchData : dataRaw;

  useEffect(() => {
    // Load Highcharts modules
    const loadModules = async () => {
      try {
        await import('highcharts/modules/exporting');
        await import('highcharts/modules/export-data');
        setModulesLoaded(true);
      } catch (error) {
        console.error('Failed to load Highcharts modules:', error);
      }
    };

    loadModules();
  }, []);

  // Add a key to force re-render when useSearchData changes
  const [chartKey, setChartKey] = useState(0);

  // Check if we have valid data to display
  const hasData = React.useMemo(() => {
    // If chartData is empty or undefined, we have no data
    if (!chartData || Object.keys(chartData).length === 0) return false;

    // For the selected speed, check if we have data
    if (selectedSpeed === 'All') {
      // Check if 'all' data exists and has values
      return (
        chartData.all &&
        chartData.all.all_timestamps &&
        chartData.all.all_timestamps.length > 0 &&
        chartData.all.all_shaft_power &&
        chartData.all.all_shaft_power.length > 0
      );
    } else {
      // For specific speeds, we need to check all entries that match the selected speed
      // Find all keys that match the selected speed
      const speedKeys = Object.keys(chartData).filter((key) => {
        const value = chartData[key];
        return 'speed' in value && value.speed === selectedSpeed;
      });

      // If we found any keys with matching speed, we have data
      return speedKeys.length > 0;
    }
  }, [chartData, selectedSpeed]);

  // Create chart options
  const chartOptions = React.useMemo(() => {
    if (!chartData || !hasData) {
      // If no data is available, return minimal options with disabled title, credits, and exporting
      return {
        title: { text: undefined },
        credits: { enabled: false },
        exporting: { enabled: false },
      };
    }

    return createChartOptions({
      chartData,
      selectedSpeed,
      hasVisibleSeries,
    });
  }, [chartData, selectedSpeed, hasData, hasVisibleSeries]);

  // Handle speed selection change
  const handleSpeedChange = (value: string) => {
    // Store the new speed value
    const newSpeed = value === 'All' ? 'All' : parseFloat(value);

    // First update the store with the new speed
    setSelectedSpeed(newSpeed);

    // Reset the userDisabledSeries flag when changing speeds
    // This ensures we don't show the NoSeriesEnabled component when changing speeds
    setUserDisabledSeries(false);

    // Always assume series are visible when changing speeds
    setHasVisibleSeries(true);

    // Add a small delay to ensure the store is updated before re-rendering
    setTimeout(() => {
      // Use a key to force a complete re-render of the chart component
      // This will create a new chart instance without the need to manually destroy it
      setChartKey((prev) => prev + 1);
    }, 0);
  };

  // Only show loading indicator when modules aren't loaded or we're actively loading
  // Otherwise, show the chart (which will display the no data placeholder if needed)
  const showChart = modulesLoaded && !isLoading;

  // Update chart visibility state when data changes
  useEffect(() => {
    if (chartRef && dataRaw) {
      // Check if any series are visible, but only update if user has manually disabled series
      try {
        const anySeriesVisible = chartRef.series && Array.from(chartRef.series).some((series) => series.visible);

        // Only update if user has manually disabled series
        if (userDisabledSeries) {
          setHasVisibleSeries(anySeriesVisible || false);
        } else {
          // During initial load, always assume series are visible
          setHasVisibleSeries(true);
        }
      } catch (_) {
        // If we can't check series visibility, assume there are visible series
        setHasVisibleSeries(true);
      }
    }
  }, [dataRaw, selectedSpeed, userDisabledSeries, chartRef]);

  // Handle chart ready callback
  const handleChartReady = (chart: Highcharts.Chart) => {
    setChartRef(chart);
  };

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Evolution Of The Most Common Shaft Power Values</div>
        <div className='flex items-center gap-2'>
          <span className='text-muted-foreground text-sm'>Speed:</span>
          <Select value={selectedSpeed.toString()} onValueChange={handleSpeedChange} disabled={isLoading || !hasRawChartData()}>
            <SelectTrigger className='w-[120px]'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {speedsList.map((speed) => (
                <SelectItem key={speed.toString()} value={speed.toString()}>
                  {speed === 'All' ? 'All' : `${speed} knots`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        {/* Chart container with full width */}
        <div className='w-full'>
          {!showChart ? (
            <div className='flex h-[220px] justify-center pt-8 md:h-[300px] md:pt-14 lg:h-[400px] lg:pt-25'>
              <ChartLoader />
            </div>
          ) : (
            <div className='w-full'>
              {/* Always render the chart with legend */}
              <ChartRenderer
                chartOptions={chartOptions}
                chartKey={chartKey}
                hasVisibleSeries={hasVisibleSeries}
                userDisabledSeries={userDisabledSeries}
                hasData={hasData}
                onChartReady={handleChartReady}
                setHasVisibleSeries={setHasVisibleSeries}
                setUserDisabledSeries={setUserDisabledSeries}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
