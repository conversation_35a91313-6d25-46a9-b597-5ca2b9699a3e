'use client';

import React, { useEffect, useState, useRef } from 'react';
import Highcharts from 'highcharts';
import { useHullPerformanceStore } from '../../hull-performance.store';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { processChartData } from './hull-performance-ai-chart-data-processor';
import { createChartOptions } from './hull-performance-ai-chart-config';
import ChartRenderer from './hull-performance-ai-chart-renderer';

export default function HullPerformanceAiChart() {
  const { dataAi, useSearchData, searchData, isLoading, searchPerformed, hasAiChartDataForCurrentState } = useHullPerformanceStore();
  const [modulesLoaded, setModulesLoaded] = useState(false);
  const chartRef = useRef<Highcharts.Chart | null>(null);
  const [hasVisibleSeries, setHasVisibleSeries] = useState(true);
  const [userDisabledSeries, setUserDisabledSeries] = useState(false);

  // Store the search date range separately from the date picker values
  const [searchFromValue, setSearchFromValue] = useState('');
  const [searchToValue, setSearchToValue] = useState('');

  // Add a key to force re-render when search button is pressed
  const [chartKey, setChartKey] = useState(0);

  // Update chart when searchData changes or loading state changes
  useEffect(() => {
    // When loading starts, increment the chart key to force a re-render
    if (isLoading) {
      setChartKey((prevKey) => prevKey + 1);
      return;
    }

    // Only update when search data changes and search is active
    if (useSearchData && searchData && Object.keys(searchData).length > 0) {
      // Get the current date range values from the store
      const { fromValue: currentFromValue, toValue: currentToValue } = useHullPerformanceStore.getState();

      // Update the search date values with the current date range
      setSearchFromValue(currentFromValue);
      setSearchToValue(currentToValue);

      // Increment the chart key to force a re-render
      setChartKey((prevKey) => prevKey + 1);

      // If we have a chart reference and valid date range, zoom to that range
      if (chartRef.current && currentFromValue && currentToValue) {
        try {
          const fromDate = new Date(currentFromValue).getTime();
          const toDate = new Date(currentToValue).getTime();

          // Set the extremes of the x-axis to zoom to the selected date range
          chartRef.current.xAxis[0].setExtremes(fromDate, toDate);
        } catch (error) {
          console.error('Error zooming chart to date range:', error);
        }
      }
    }
  }, [useSearchData, searchData, isLoading]); // Include isLoading in the dependency array

  useEffect(() => {
    // Initialize modules only on client side
    if (typeof window !== 'undefined') {
      // Dynamically import Highcharts modules
      const loadModules = async () => {
        try {
          // Import the modules dynamically
          const highchartsMore = (await import('highcharts/highcharts-more')) as typeof import('highcharts/highcharts-more');
          const highchartsAnnotations = (await import('highcharts/modules/annotations')) as typeof import('highcharts/modules/annotations');
          const exportingModule = (await import('highcharts/modules/exporting')) as typeof import('highcharts/modules/exporting');
          const exportDataModule = (await import('highcharts/modules/export-data')) as typeof import('highcharts/modules/export-data');

          // Apply the modules to Highcharts
          if (typeof highchartsMore === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (highchartsMore as any)(Highcharts);
          } else if (typeof highchartsMore.default === 'function') {
            highchartsMore.default(Highcharts);
          }

          if (typeof highchartsAnnotations === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (highchartsAnnotations as any)(Highcharts);
          } else if (typeof highchartsAnnotations.default === 'function') {
            highchartsAnnotations.default(Highcharts);
          }

          // Apply exporting modules
          if (typeof exportingModule === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (exportingModule as any)(Highcharts);
          } else if (typeof exportingModule.default === 'function') {
            exportingModule.default(Highcharts);
          }

          if (typeof exportDataModule === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (exportDataModule as any)(Highcharts);
          } else if (typeof exportDataModule.default === 'function') {
            exportDataModule.default(Highcharts);
          }

          setModulesLoaded(true);
        } catch (error) {
          console.error('Failed to load Highcharts modules:', error);
        }
      };

      loadModules();
    }
  }, []);

  // Process chart data and create chart options
  const { chartOptions, showChart, hasData } = React.useMemo(() => {
    // Don't show chart while loading or if modules aren't loaded
    if (!modulesLoaded || isLoading) {
      return { chartOptions: {}, showChart: false, hasData: false };
    }

    // Use the store helper to check if we have data for the current state (search vs normal)
    const hasDataForCurrentState = hasAiChartDataForCurrentState();

    if (!hasDataForCurrentState) {
      // If no data is available for current state, show chart but with no data
      // Return minimal chart options with disabled title, credits, and exporting
      return {
        chartOptions: {
          title: { text: undefined },
          credits: { enabled: false },
          exporting: { enabled: false },
        },
        showChart: true,
        hasData: false,
      };
    }

    // If we have data, ensure we have a valid dataAi object (this should be true if hasDataForCurrentState is true)
    if (!dataAi || !dataAi.all) {
      return {
        chartOptions: {
          title: { text: undefined },
          credits: { enabled: false },
          exporting: { enabled: false },
        },
        showChart: true,
        hasData: false,
      };
    }

    // Process the data for the chart
    const processedData = processChartData(dataAi.all, useSearchData, searchFromValue, searchToValue);

    // Create chart options
    const options = createChartOptions({
      dataAi: dataAi.all,
      processedData,
      hasVisibleSeries,
      userDisabledSeries,
    });

    return { chartOptions: options, showChart: true, hasData: true };
  }, [
    dataAi,
    useSearchData,
    searchFromValue,
    searchToValue,
    modulesLoaded,
    hasVisibleSeries,
    userDisabledSeries,
    isLoading,
    searchPerformed,
    hasAiChartDataForCurrentState,
  ]);

  // Handle chart ready callback
  const handleChartReady = (chart: Highcharts.Chart) => {
    chartRef.current = chart;
  };

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4'>
        <div className='text-lg font-semibold tracking-tight'>Performance Comparison With AI</div>
      </div>

      {isLoading ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[300px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : !showChart ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[300px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : (
        <div>
          <ChartRenderer
            chartOptions={chartOptions as Highcharts.Options}
            chartKey={chartKey}
            useSearchData={useSearchData}
            hasVisibleSeries={hasVisibleSeries}
            userDisabledSeries={userDisabledSeries}
            hasData={hasData}
            onChartReady={handleChartReady}
            setHasVisibleSeries={setHasVisibleSeries}
            setUserDisabledSeries={setUserDisabledSeries}
          />
        </div>
      )}
    </div>
  );
}
