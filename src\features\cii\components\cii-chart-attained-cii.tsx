'use client';

import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import React from 'react';
import { useCIIStore } from '../cii.store';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { CiiChartWeekEditRef } from './cii-chart-week-edit';

let isSyncing = false;

function getBarColor(value: number, boundaries: number[]): string {
  const BAR_COLORS = [
    'oklch(72.3% 0.219 149.579)',
    'oklch(76.8% 0.233 130.85)',
    'oklch(79.5% 0.184 86.047)',
    'oklch(70.5% 0.213 47.604)',
    'oklch(63.7% 0.237 25.331)',
  ];

  for (let i = 0; i < boundaries.length; i++) {
    if (value <= boundaries[i]) return BAR_COLORS[i - 1];
  }
  return BAR_COLORS[BAR_COLORS.length - 1];
}

function generatePlotLines(boundaries: number[] = []) {
  const BOUNDARY_COLORS: Record<string, string> = {
    A: 'oklch(72.3% 0.219 149.579)',
    B: 'oklch(76.8% 0.233 130.85)',
    C: 'oklch(79.5% 0.184 86.047)',
    D: 'oklch(70.5% 0.213 47.604)',
    E: 'oklch(63.7% 0.237 25.331)',
  };

  const zones = Object.keys(BOUNDARY_COLORS);
  return boundaries.map((value, index) => {
    const zone = zones[index];
    const color = BOUNDARY_COLORS[zone] || 'black';
    return {
      value,
      color,
      width: 1,
      dashStyle: 'Dash',
      zIndex: 1,
      label: {
        text: `${zone} (${value})`,
        align: 'right',
        x: 64,
        y: 3,
        style: { color, fontSize: '12px' },
      },
    };
  });
}

function createSharedPointEvents(linkedRef: React.RefObject<Highcharts.Chart>) {
  return {
    mouseOver(this: Highcharts.Point) {
      const category = this.category;
      if (!linkedRef?.current) return;

      const pointsToHighlight: Highcharts.Point[] = [];
      linkedRef.current.series.forEach((series) => {
        const match = series.points.find((p) => p.category === category);
        if (match) {
          match.setState('hover');
          pointsToHighlight.push(match);
        }
      });

      if (pointsToHighlight.length) linkedRef.current.tooltip.refresh(pointsToHighlight);
    },
    mouseOut() {
      if (!linkedRef?.current) return;

      linkedRef.current.series.forEach((series) => {
        series.points.forEach((point) => point.setState(''));
      });
      linkedRef.current.tooltip.hide();
    },
  };
}

export default function CiiChartAttainedCii({
  chartRef,
  linkedRef,
  weekEditRef,
}: {
  chartRef: React.RefObject<Highcharts.Chart>;
  linkedRef: React.RefObject<Highcharts.Chart>;
  weekEditRef: React.RefObject<CiiChartWeekEditRef>;
}) {
  const { weeklyDataMerged, weeklyDataManual, yearData } = useCIIStore();
  const modifiedWeeks = Object.keys(weeklyDataManual || {}).length;
  const missingWeeks = Object.values(weeklyDataMerged || {}).filter(
    (entry) => entry.missing_time !== '0 day(s), 0 hour(s), and 0 minute(s)'
  ).length;
  const totalWeeks = Object.keys(weeklyDataMerged || {}).length;

  const chartOptions = React.useMemo(() => {
    const boundaries = [0, ...(yearData?.cii?.adjusted_boundaries || [])];
    const weeks = weeklyDataMerged ? Object.keys(weeklyDataMerged).map(Number) : [];
    const attainedCIIValues = weeks.map((week) => weeklyDataMerged[week]?.attained_cii_snapshot ?? 0);
    const attainedCIIData = weeks.map((week, index) => {
      const value = attainedCIIValues[index];
      const isManual = weeklyDataManual?.hasOwnProperty(week);

      return {
        y: value,
        color: getBarColor(value, boundaries),
        dataLabels: isManual
          ? {
              enabled: true,
              inside: true,
              verticalAlign: 'top',
              align: 'center',
              format: 'M',
              style: {
                fontWeight: 'bold',
                color: '#000',
                textOutline: 'none',
                fontSize: '11px',
              },
            }
          : undefined,
      };
    });

    return {
      chart: { zooming: { type: 'x' }, marginRight: 64 },
      title: { text: null },
      legend: { enabled: false },
      credits: { enabled: false },
      exporting: { enabled: false },
      tooltip: {
        useHTML: true,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formatter: function (this: any) {
          const week = Number(this.point.category);
          const data = weeklyDataMerged?.[week];

          let tooltipHtml = `<b>Week: ${week}</b>`;
          tooltipHtml += `<br/>Attained CII: <b>${this.point.y.toFixed(2)}</b>`;
          tooltipHtml += `<br/>Distance: <b>${data?.distance.toFixed(2)} nm</b>`;
          tooltipHtml += `<br/>First received time: <b>${data?.start}</b>`;
          tooltipHtml += `<br/>Last received time: <b>${data?.end}</b>`;
          tooltipHtml += `<br/>Frugal Status: <b>On: ${data?.frugal_status_percentage.on?.toFixed(1)}% - Off: ${data?.frugal_status_percentage.off?.toFixed(1)}%</b>`;
          tooltipHtml += `<br/>Missing Time: <b>${data?.missing_time}</b>`;
          tooltipHtml += `<br/>Time Sailed: <b>${data?.time_sailed}</b>`;

          return tooltipHtml;
        },
      },
      xAxis: [
        {
          title: { text: 'Week Number' },
          categories: weeks.map(String),
          crosshair: true,
          labels: {
            style: { color: 'black' },
          },
          events: {
            setExtremes(e: Highcharts.AxisSetExtremesEventObject) {
              if (isSyncing) return;
              if (linkedRef?.current) {
                isSyncing = true;
                linkedRef.current.xAxis[0].setExtremes(e.min, e.max, true, false);
                isSyncing = false;
              }
            },
          },
        },
      ],
      yAxis: [
        {
          gridLineWidth: 1,
          title: {
            text: 'Attained CII',
            style: { color: 'black' },
          },
          labels: {
            format: '{value}',
            style: { color: 'black' },
          },
          opposite: false,
          plotLines: generatePlotLines(boundaries),
        },
      ],
      series: [
        {
          name: 'Attained CII',
          type: 'column',
          yAxis: 0,
          data: attainedCIIData,
          tooltip: { valueDecimals: 2 },
        },
      ],
      plotOptions: {
        series: {
          point: {
            events: {
              click: function (this: Highcharts.Point) {
                const week = Number(this.category);
                const data = weeklyDataMerged?.[week];

                if (!data) return;

                const payload = {
                  week_numb: week,
                  distance: data.distance,
                  fuel: Object.entries(data.fuel_consumption || {}).map(([type, value]) => ({
                    fuelType: type,
                    fuelConsumption: value,
                  })),
                };

                weekEditRef?.current?.open(payload);
              },
              ...createSharedPointEvents(linkedRef),
            },
          },
        },
      },
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 768,
            },
            chartOptions: {
              chart: {
                height: 300,
              },
            },
          },
          {
            condition: {
              maxWidth: 480,
            },
            chartOptions: {
              chart: {
                height: 220,
              },
            },
          },
        ],
      },
    };
  }, [weeklyDataMerged, yearData, linkedRef, weekEditRef]);

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex flex-col gap-1 md:flex-row md:items-center md:justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Attained CII Chart</div>
        <div className='text-muted-foreground text-sm'>
          {modifiedWeeks}/{totalWeeks} modified weeks &nbsp;|&nbsp; {missingWeeks}/{totalWeeks} missing weeks
        </div>
      </div>
      {weeklyDataMerged ? (
        <HighchartsReact
          highcharts={Highcharts}
          options={chartOptions}
          callback={(chart: Highcharts.Chart) => (chartRef.current = chart)}
        />
      ) : (
        <div className='flex h-[220px] justify-center pt-8 md:h-[300px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      )}
    </div>
  );
}
