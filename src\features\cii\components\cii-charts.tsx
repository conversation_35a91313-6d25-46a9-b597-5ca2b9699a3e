'use client';

import { useRef } from 'react';
import CIIChartAttainedCII from './cii-chart-attained-cii';
import CiiChartSpeedFuel from './cii-chart-speed-fuel';
import CiiChartWeekEdit, { CiiChartWeekEditRef } from './cii-chart-week-edit';

export default function CIICharts() {
  const attainedChartRef = useRef<Highcharts.Chart | null>(null);
  const speedFuelChartRef = useRef<Highcharts.Chart | null>(null);
  const weekEditRef = useRef<CiiChartWeekEditRef | null>(null);

  return (
    <div className='grid grid-cols-1 gap-4'>
      <CIIChartAttainedCII
        chartRef={attainedChartRef as React.RefObject<Highcharts.Chart>}
        linkedRef={speedFuelChartRef as React.RefObject<Highcharts.Chart>}
        weekEditRef={weekEditRef as React.RefObject<CiiChartWeekEditRef>}
      />
      <CiiChartSpeedFuel
        chartRef={speedFuelChartRef as React.RefObject<Highcharts.Chart>}
        linkedRef={attainedChartRef as React.RefObject<Highcharts.Chart>}
        weekEditRef={weekEditRef as React.RefObject<CiiChartWeekEditRef>}
      />
      <CiiChartWeekEdit ref={weekEditRef} />
    </div>
  );
}
