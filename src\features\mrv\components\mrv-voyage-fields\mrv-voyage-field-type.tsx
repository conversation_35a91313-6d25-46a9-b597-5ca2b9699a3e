'use client';

import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
  SelectGroup,
  SelectLabel,
  SelectSeparator,
} from '@/components/ui/select';
import { Control, FieldValues, Path } from 'react-hook-form';

interface MrvVoyageFieldTypeProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
}

export function MrvVoyageFieldType<T extends FieldValues>({ control, name, label }: MrvVoyageFieldTypeProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      rules={{ required: 'This field is required' }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Select onValueChange={field.onChange} value={field.value?.toString() || 'none'}>
              <SelectTrigger className='w-full min-w-[140px]'>
                <SelectValue placeholder='Select voyage type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='none'>None</SelectItem>
                <SelectSeparator />
                <SelectGroup>
                  <SelectLabel>EU Voyages</SelectLabel>
                  <SelectItem value='inbound'>Towards EU</SelectItem>
                  <SelectItem value='both'>Between EU</SelectItem>
                  <SelectItem value='outbound'>Outbound EU</SelectItem>
                  {/* <SelectItem value='at Berth EU'>at Berth EU</SelectItem> */}
                </SelectGroup>
                <SelectSeparator />
                {/* <SelectGroup>
                  <SelectLabel>UK Voyages</SelectLabel>
                  <SelectItem value='Towards UK'>Towards UK</SelectItem>
                  <SelectItem value='Between UK'>Between UK</SelectItem>
                  <SelectItem value='Outbound UK'>Outbound UK</SelectItem>
                  <SelectItem value='at Berth UK'>at Berth UK</SelectItem>
                </SelectGroup> */}
              </SelectContent>
            </Select>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
