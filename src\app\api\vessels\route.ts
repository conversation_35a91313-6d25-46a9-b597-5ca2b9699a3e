import { VesselDTO } from '@/features/vessels/vessel.types';
import { httpClient } from '@/lib/http-client';
import { HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';

// === GET ENDPOINT ===
export async function GET(request: NextRequest) {
  // Build URL
  const url = new URL('/vessels', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.get<{ vessels: VesselDTO[] }>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
