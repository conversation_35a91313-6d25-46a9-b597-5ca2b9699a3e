import { Reports } from '../../home-vessel.types';

export function useChartDataDaily(
  data: Reports | null,
  type: 'main' | 'aux' | 'boiler'
): {
  dayLabels: string[];
  yValues: number[];
  beaufortValues: number[];
  speedValues: number[];
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  detailValues: any[];
} | null {
  if (!data) return null;

  const months: string[] = data.months_to_use;
  if (months.length === 0) return null;

  const lastMonth = months[months.length - 1];
  const lastMonthData = data.monthly_fuel_report[lastMonth];
  const currentDays = Object.entries(lastMonthData.Days).slice(-14);

  let last14Days = [...currentDays];

  if (currentDays.length < 14 && months.length > 1) {
    const prevMonth = months[months.length - 2];
    const prevMonthData = data.monthly_fuel_report[prevMonth];
    const prevDays = Object.entries(prevMonthData.Days).slice(-(14 - currentDays.length));
    last14Days = [...prevDays, ...currentDays];
  }

  const dayLabels: string[] = last14Days.map(([dayKey, _], index) => {
    const month = index < 14 - currentDays.length && months.length > 1 ? months[months.length - 2] : months[months.length - 1];
    return `${month} ${dayKey}`;
  });

  const beaufortValues: number[] = last14Days.map(([_, value]) => value.bf ?? 0);

  const speedValues: number[] =
    data.efficiencies_daily.speed_type === 'log_speed'
      ? last14Days.map(([_, value]) => value.log_speed ?? 0)
      : data.efficiencies_daily.speed_type === 'gps_speed'
        ? last14Days.map(([_, value]) => value.gps_speed ?? 0)
        : [];

  const speedType =
    data.efficiencies_daily.speed_type === 'log_speed'
      ? 'Log Speed'
      : data.efficiencies_daily.speed_type === 'gps_speed'
        ? 'GPS Speed'
        : 'Unknown';

  let yValues: number[];
  switch (type) {
    case 'main':
      yValues = last14Days.map(([_, value]) => value.ME?.Tons ?? 0);
      break;
    case 'aux':
      yValues = last14Days.map(([_, value]) => value.AUX?.Tons ?? 0);
      break;
    case 'boiler':
      yValues = last14Days.map(([_, value]) => value.Boiler?.Tons ?? 0);
      break;
    default:
      yValues = [];
  }

  return {
    dayLabels,
    yValues,
    beaufortValues,
    speedValues,
    speedType,
    detailValues: last14Days,
  };
}

export function useChartDataMonthly(
  data: Reports | null,
  type: 'main' | 'aux' | 'boiler'
): {
  monthLabels: string[];
  yValues: number[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  detailValues: Record<string, any>[];
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown';
} | null {
  if (!data) return null;

  const monthLabels: string[] = data.months_to_use;

  if (monthLabels.length === 0) return null;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const monthlyData: Record<string, any> = data.monthly_fuel_report;
  const detailValues = monthLabels.map((month) => monthlyData[month]);

  const speedType =
    data.efficiencies_daily.speed_type === 'log_speed'
      ? 'Log Speed'
      : data.efficiencies_daily.speed_type === 'gps_speed'
        ? 'GPS Speed'
        : 'Unknown';

  let yValues: number[];
  switch (type) {
    case 'main':
      yValues = data.monthly_fuel_report_me_data[0].data;
      break;
    case 'aux':
      yValues = data.monthly_fuel_report_aux_data[0].data;
      break;
    case 'boiler':
      yValues = data.monthly_fuel_report_boiler_data[0].data;
      break;
    default:
      yValues = [];
  }

  return {
    monthLabels,
    yValues,
    detailValues,
    speedType,
  };
}
