import { create } from 'zustand';
import { AnomalyMetricsBySignal } from './anomaly-detection.types';
import axios from 'axios';
import { toast } from 'sonner';

export interface SensorOption {
  value: string;
  label: string;
  labelWithUnit: string;
}

export const SENSOR_OPTIONS: SensorOption[] = [
  { value: 'log_speed', label: 'Log Speed', labelWithUnit: 'Log Speed (kn)' },
  { value: 'gps_speed', label: 'GPS Speed', labelWithUnit: 'GPS Speed (kn)' },
  { value: 'shaft_1_rpm', label: 'Shaft RPM', labelWithUnit: 'Shaft RPM' },
  { value: 'shaft_1_power', label: 'Shaft Power', labelWithUnit: 'Shaft Power (kW)' },
  { value: 'me_1_fc_mass', label: 'Main Engine Fuel Consumption', labelWithUnit: 'Main Engine Fuel Consumption (kg/h)' },
  { value: 'aux_1_fc_mass', label: 'Auxiliary 1 Fuel Consumption', labelWithUnit: 'Auxiliary 1 Fuel Consumption (kg/h)' },
  { value: 'aux_2_fc_mass', label: 'Auxiliary 2 Fuel Consumption', labelWithUnit: 'Auxiliary 2 Fuel Consumption (kg/h)' },
  { value: 'me_1_rpm', label: 'Main Engine RPM', labelWithUnit: 'Main Engine RPM' },
];

// Helper function to get sensor label with units
export const getSensorLabelWithUnit = (sensorValue: string): string => {
  const sensorOption = SENSOR_OPTIONS.find((option) => option.value === sensorValue);
  return sensorOption?.labelWithUnit || sensorValue;
};

// Helper function to get sensor label without units (for backward compatibility)
export const getSensorLabel = (sensorValue: string): string => {
  const sensorOption = SENSOR_OPTIONS.find((option) => option.value === sensorValue);
  return sensorOption?.label || sensorValue;
};

interface AnomalyDetectionStore {
  anomalyDetectionData: AnomalyMetricsBySignal | null;
  isLoading: boolean;
  sensorOptions: SensorOption[];
  availableSensorOptions: SensorOption[];
  selectedSensors: string[];
  selectedSensor: string;

  setSelectedSensors: (sensors: string[]) => void;
  setSelectedSensor: (sensor: string) => void;
  fetchAnomalyDetectionData: (selected_owner_vat: string, vessel_imo: string) => Promise<void>;
}

export const useAnomalyDetectionStore = create<AnomalyDetectionStore>((set) => ({
  anomalyDetectionData: null,
  isLoading: false,
  sensorOptions: SENSOR_OPTIONS,
  availableSensorOptions: [],
  selectedSensors: [],
  selectedSensor: '',

  setSelectedSensors: (sensors: string[]) => {
    set({ selectedSensors: sensors });
  },

  setSelectedSensor: (sensor: string) => {
    set({ selectedSensor: sensor });
  },

  fetchAnomalyDetectionData: async (selectedOwnerVat, vesselImo) => {
    if (!selectedOwnerVat || !vesselImo) {
      toast.error('Missing parameters');
      return;
    }

    try {
      set({ isLoading: true, anomalyDetectionData: null, availableSensorOptions: [], selectedSensors: [], selectedSensor: '' });
      const params = {
        owner_vat: selectedOwnerVat,
        vessel_imo: vesselImo,
      };
      const res = await axios.get<AnomalyMetricsBySignal>(`/api/anomaly-detection?${new URLSearchParams(params).toString()}`);
      // Filter available sensors based on fetched data
      const availableKeys = Object.keys(res.data).filter((key) => key !== 'last_sanity_timestamp');
      const availableSensorOptions = SENSOR_OPTIONS.filter((option) => availableKeys.includes(option.value));
      const selectedSensors = availableSensorOptions.map((option) => option.value);

      set({
        anomalyDetectionData: res.data,
        availableSensorOptions,
        selectedSensors,
        selectedSensor: selectedSensors.length > 0 ? selectedSensors[0] : '',
        isLoading: false,
      });
    } catch (_error) {
      toast.error('Failed to fetch anomaly detection data');
    } finally {
      set({ isLoading: false });
    }
  },
}));
