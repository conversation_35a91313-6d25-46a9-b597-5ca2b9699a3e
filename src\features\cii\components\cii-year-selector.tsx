'use client';

import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useCIIStore } from '../cii.store';
import React from 'react';

export function CIIYearSelector() {
  const { years, year, setYear } = useCIIStore();

  if (!year || !years.length) {
    return <Skeleton className='h-10 w-22 rounded-md' />;
  }

  return (
    <Select value={year} onValueChange={setYear}>
      <SelectTrigger>
        <SelectValue placeholder='Select a year' />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Years</SelectLabel>
          {years.map((year) => (
            <SelectItem key={year} value={year}>
              {year}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
