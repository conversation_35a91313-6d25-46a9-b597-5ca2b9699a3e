import { DataAnalyticsResponse } from '../../data-analytics.types';
import { frugalColorMap } from './data-analytics-chart-performance.constants';

export function useChartData(data: DataAnalyticsResponse | null) {
  if (!data) return null;

  const xAxis = data.data_analytics.measurement_graphs.x_axis.calculated_categories;
  const xAxisRaw = data.data_analytics.measurement_graphs.x_axis.raw_categories;
  const merpm = data.data_analytics.measurement_graphs.y_axis.merpm.calculated_data;
  const spow = data.data_analytics.measurement_graphs.y_axis.spow.calculated_data;
  const merpm_raw = data.data_analytics.measurement_graphs.y_axis.merpm.raw_data;
  const spow_raw = data.data_analytics.measurement_graphs.y_axis.spow.raw_data;
  const frugalStatus = data.data_analytics.frugalStatusTime;

  const sorted = xAxis
    .map((date, i) => ({
      date,
      merpm: merpm[i],
      spow: spow[i],
      merpm_raw: merpm_raw?.[i] || null,
      spow_raw: spow_raw?.[i] || null,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const merpmSeries = sorted.map((d) => [new Date(d.date).getTime(), d.merpm]);
  const spowSeries = sorted.map((d) => [new Date(d.date).getTime(), d.spow]);
  const merpmRawSeries =
    merpm_raw && xAxisRaw
      ? merpm_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const spowRawSeries =
    spow_raw && xAxisRaw
      ? spow_raw
          .map((value, i) => {
            const time = new Date(xAxisRaw[i]).getTime();
            return isFinite(value) && !isNaN(time) ? { time, value } : null;
          })
          .filter((item): item is { time: number; value: number } => Boolean(item))
          .sort((a, b) => a.time - b.time)
          .map((d) => [d.time, d.value])
      : null;

  const plotBands = frugalStatus.map((r) => ({
    from: new Date(r.from).getTime(),
    to: new Date(r.to).getTime(),
    color: frugalColorMap[r.frugal_mode] || 'rgba(0, 0, 0, 0.0)',
  }));

  return {
    merpmSeries,
    spowSeries,
    merpmRawSeries,
    spowRawSeries,
    plotBands,
    frugalStatus,
  };
}
