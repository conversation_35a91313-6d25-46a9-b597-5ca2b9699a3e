'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>alog, DialogTrigger, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectSeparator,
  SelectGroup,
} from '@/components/ui/select';
import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { VoyageGetDTO, VoyagePutDTO } from '../voyage.types';
import { useVoyageStore } from '@/features/temp/voyage.store';
import { Pencil, Plus, Trash2 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { isVoyageComplete } from '../voyage.utils';

// Fuel emission coefficients
const fuelCoefficients = {
  mdo: 3.206,
  lfo: 3.151,
  hfo: 3.114,
};

export function VoyageEdit({ voyage }: { voyage: VoyageGetDTO }) {
  const [open, setOpen] = useState(false);
  const { updateVoyage, year } = useVoyageStore();

  const initialActiveFuelTypes = ['hfo', 'mdo', 'lfo'].filter((type) => {
    // look for either uppercase or lowercase field
    const consU = voyage[`fuel_consumption_${type.toUpperCase()}` as keyof typeof voyage] as number | undefined;
    const consL = voyage[`fuel_consumption_${type}` as keyof typeof voyage] as number | undefined;
    const consumption = (consU ?? consL) || 0;
    return consumption > 0;
  });

  const [activeFuelTypes, setActiveFuelTypes] = useState<string[]>(initialActiveFuelTypes);
  const [availableFuelTypes, setAvailableFuelTypes] = useState<string[]>(
    ['hfo', 'mdo', 'lfo'].filter((type) => !initialActiveFuelTypes.includes(type))
  );
  const [selectedFuelType, setSelectedFuelType] = useState<string>('');

  // Calculate time sailed values
  const calculateTimeSailed = (startTime: Date, endTime: Date) => {
    // Calculate the time difference in milliseconds
    const timeDiff = endTime.getTime() - startTime.getTime();

    // Skip calculation if end time is before start time
    if (timeDiff < 0) return { days: 0, hours: 0, minutes: 0 };

    // Convert to days, hours, minutes
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes };
  };

  // Get initial time values
  const startTime = voyage.start_time ? new Date(voyage.start_time) : new Date();
  const endTime = voyage.end_time ? new Date(voyage.end_time) : new Date();
  const { days, hours, minutes } = calculateTimeSailed(startTime, endTime);

  // If voyage has Error status, ensure approved_mrv is false
  const hasErrorStatus = voyage.voyage_status === 'Error';
  const approvedMrv = hasErrorStatus ? false : voyage.approved_mrv || voyage.approved_mrv_voyage || false;

  const form = useForm<VoyagePutDTO>({
    defaultValues: {
      departure_port: voyage.departure_port || '',
      destination_port: voyage.destination_port || '',
      start_time: voyage.start_time ? new Date(voyage.start_time).toISOString() : new Date().toISOString(),
      end_time: voyage.end_time ? new Date(voyage.end_time).toISOString() : new Date().toISOString(),
      voyage_status: voyage.voyage_status,
      cargo: voyage.cargo || 0,
      description: voyage.description || '',
      distance: voyage.distance || 0,
      emissions: voyage.emissions || 0,
      emissions_hfo: voyage.emissions_hfo || 0,
      emissions_lfo: voyage.emissions_lfo || 0,
      emissions_mdo: voyage.emissions_mdo || 0,
      emissions_mgo: voyage.emissions_mgo || 0,
      fuel_consumption: voyage.fuel_consumption || 0,
      fuel_consumption_hfo: voyage.fuel_consumption_hfo || voyage.fuel_consumption_HFO || 0,
      fuel_consumption_lfo: voyage.fuel_consumption_lfo || voyage.fuel_consumption_LFO || 0,
      fuel_consumption_mdo: voyage.fuel_consumption_mdo || voyage.fuel_consumption_MDO || 0,
      fuel_consumption_mgo: voyage.fuel_consumption_mgo || 0,
      fuel_types: voyage.fuel_types || [],
      time_sailed_days: days,
      time_sailed_hours: hours,
      time_sailed_minutes: minutes,
      transport_work: voyage.transport_work || 0,
      voyage_type: voyage.voyage_type || 'none',
      voyage_number: voyage.voyage_number || 0,
      approved_mrv: approvedMrv,
    },
  });

  // Calculate emissions based on fuel consumption
  const calculateEmissions = (fuelType: string, consumption: number): number => {
    if (!consumption) return 0;
    return Number((consumption * (fuelCoefficients[fuelType as keyof typeof fuelCoefficients] || 0)).toFixed(2));
  };

  // Add a new fuel type to the active list
  const addFuelType = (fuelType: string) => {
    if (fuelType && !activeFuelTypes.includes(fuelType)) {
      setActiveFuelTypes([...activeFuelTypes, fuelType]);
      setAvailableFuelTypes(availableFuelTypes.filter((type) => type !== fuelType));

      // Update the form's fuel_types array
      const currentFuelTypes = form.getValues('fuel_types') || [];
      form.setValue('fuel_types', [...currentFuelTypes, fuelType]);
    }
  };

  // Remove a fuel type from the active list
  const removeFuelType = (fuelType: string) => {
    setActiveFuelTypes(activeFuelTypes.filter((type) => type !== fuelType));
    setAvailableFuelTypes([...availableFuelTypes, fuelType]);

    // Update the form's fuel_types array
    const currentFuelTypes = form.getValues('fuel_types') || [];
    form.setValue(
      'fuel_types',
      currentFuelTypes.filter((type) => type !== fuelType)
    );

    // Reset the consumption and emissions for this fuel type
    form.setValue(`fuel_consumption_${fuelType}` as keyof VoyagePutDTO, 0);
    form.setValue(`emissions_${fuelType}` as keyof VoyagePutDTO, 0);

    // Update total values
    updateTotals();
  };

  // Function to update total consumption and emissions
  const updateTotals = () => {
    const formData = form.getValues() as Record<string, unknown>;
    let totalConsumption = 0;
    let totalEmissions = 0;
    activeFuelTypes.forEach((type) => {
      const consumption = formData[`fuel_consumption_${type}`] as number;
      if (typeof consumption === 'number') {
        totalConsumption += consumption;
      }
      const emissions = formData[`emissions_${type}`] as number;
      if (typeof emissions === 'number') {
        totalEmissions += emissions;
      }
    });
    form.setValue('fuel_consumption', Number(totalConsumption.toFixed(2)));
    form.setValue('emissions', Number(totalEmissions.toFixed(2)));
  };

  // Update emissions when fuel consumption changes and check completeness
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name && name.startsWith('fuel_consumption_')) {
        const fuelType = name.replace('fuel_consumption_', '');
        const consumption = value[name as keyof typeof value] as number;
        const emissions = calculateEmissions(fuelType, consumption);

        // Update the emissions value without resetting the form
        form.setValue(`emissions_${fuelType}` as keyof VoyagePutDTO, emissions);

        // Update total values
        updateTotals();
      }

      // If voyage status changes to Error, uncheck the approved checkbox
      if (name === 'voyage_status' && value.voyage_status === 'Error' && value.approved_mrv) {
        form.setValue('approved_mrv', false);
      }

      // Check if any required field changes affect voyage completeness
      // and automatically uncheck approved_mrv if voyage becomes incomplete
      if (value.approved_mrv) {
        const requiredFields = [
          'departure_port',
          'destination_port',
          'start_time',
          'end_time',
          'distance',
          'time_sailed_days',
          'time_sailed_hours',
          'time_sailed_minutes',
          'fuel_consumption',
          'fuel_consumption_hfo',
          'fuel_consumption_lfo',
          'fuel_consumption_mdo',
          'fuel_consumption_mgo',
          'emissions',
          'cargo',
          'transport_work',
          'voyage_type',
        ];

        // Check if the changed field is a required field
        if (name && requiredFields.includes(name)) {
          // Create temp voyage object with current form values to check completeness
          const currentValues = form.getValues();
          const tempVoyage = {
            ...voyage,
            ...currentValues,
            fuel_consumption_HFO: currentValues.fuel_consumption_HFO || currentValues.fuel_consumption_hfo || 0,
            fuel_consumption_LFO: currentValues.fuel_consumption_LFO || currentValues.fuel_consumption_lfo || 0,
            fuel_consumption_MDO: currentValues.fuel_consumption_MDO || currentValues.fuel_consumption_mdo || 0,
            emissions: currentValues.emissions || 0,
          };

          const isComplete = isVoyageComplete(tempVoyage);

          // If voyage is no longer complete, uncheck approved_mrv
          if (!isComplete) {
            form.setValue('approved_mrv', false);
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, activeFuelTypes, voyage]);

  // Update time sailed fields when departure or arrival time changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'start_time' || name === 'end_time') {
        const startTime = value.start_time ? new Date(value.start_time) : undefined;
        const endTime = value.end_time ? new Date(value.end_time) : undefined;
        if (startTime && endTime) {
          // Calculate the time difference in milliseconds
          const timeDiff = endTime.getTime() - startTime.getTime();

          // Skip calculation if end time is before start time
          if (timeDiff < 0) return;

          // Convert to days, hours, minutes
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

          // Update the form values
          form.setValue('time_sailed_days', days);
          form.setValue('time_sailed_hours', hours);
          form.setValue('time_sailed_minutes', minutes);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Update form when voyage data changes (e.g., after successful save)
  useEffect(() => {
    if (!open) {
      // Only update when dialog is closed to avoid interfering with user input
      const hasErrorStatus = voyage.voyage_status === 'Error';
      const approvedMrv = hasErrorStatus ? false : voyage.approved_mrv || voyage.approved_mrv_voyage || false;

      // Update the approved_mrv field to reflect the current voyage state
      form.setValue('approved_mrv', approvedMrv);
    }
  }, [voyage.approved_mrv, voyage.approved_mrv_voyage, voyage.voyage_status, open, form]);

  const onSubmit = async (data: VoyagePutDTO) => {
    function formatToYMDHMS(input: string | Date): string {
      // ensure we have a Date object
      const d = typeof input === 'string' ? new Date(input) : input;

      const pad2 = (n: number) => n.toString().padStart(2, '0');

      const YYYY = d.getFullYear();
      const MM = pad2(d.getMonth() + 1);
      const DD = pad2(d.getDate());
      const hh = pad2(d.getHours());
      const mm = pad2(d.getMinutes());
      const ss = pad2(d.getSeconds());

      return `${YYYY}-${MM}-${DD} ${hh}:${mm}:${ss}`;
    }

    data.start_time = formatToYMDHMS(data.start_time ?? new Date());
    data.end_time = formatToYMDHMS(data.end_time ?? new Date());

    // Handle voyage_type 'none' value
    if (data.voyage_type === 'none') {
      data.voyage_type = '';
    }

    // Always ensure approved_mrv_voyage matches approved_mrv
    data.approved_mrv_voyage = data.approved_mrv || false;

    if (data.voyage_status === 'Error') {
      data.approved_mrv = false;
      data.approved_mrv_voyage = false;
    }
    // If approved_mrv is checked, set voyage_status to 'Approved'
    else if (data.approved_mrv) {
      data.voyage_status = 'Approved';
    } else if (data.voyage_status === 'Approved' && !data.approved_mrv) {
      // If unapproving a previously approved voyage, set status back to Complete
      data.voyage_status = 'Complete';
    }

    // Map form fields to API fields - cargo and distance are now separate fields

    if (data.fuel_consumption_hfo !== undefined) {
      data.fuel_consumption_HFO = data.fuel_consumption_hfo;
    }

    if (data.fuel_consumption_lfo !== undefined) {
      data.fuel_consumption_LFO = data.fuel_consumption_lfo;
    }

    if (data.fuel_consumption_mdo !== undefined) {
      data.fuel_consumption_MDO = data.fuel_consumption_mdo;
    }

    // Make sure we have a valid ID
    const id = voyage.id || 0;
    data.voyage_id = voyage.uuid;

    // Close the popup and update the voyage
    setOpen(false);
    await updateVoyage(id, data);
  };

  // Function to reset form to initial values
  const resetForm = () => {
    // Recalculate time sailed values
    const resetStartTime = voyage.start_time ? new Date(voyage.start_time) : new Date();
    const resetEndTime = voyage.end_time ? new Date(voyage.end_time) : new Date();
    const { days, hours, minutes } = calculateTimeSailed(resetStartTime, resetEndTime);

    // If voyage has Error status, ensure approved_mrv is false
    const hasErrorStatus = voyage.voyage_status === 'Error';
    const approvedMrv = hasErrorStatus ? false : voyage.approved_mrv || voyage.approved_mrv_voyage || false;

    form.reset({
      departure_port: voyage.departure_port || '',
      destination_port: voyage.destination_port || '',
      start_time: voyage.start_time ? new Date(voyage.start_time).toISOString() : new Date().toISOString(),
      end_time: voyage.end_time ? new Date(voyage.end_time).toISOString() : new Date().toISOString(),
      voyage_status: voyage.voyage_status,
      cargo: voyage.cargo || 0,
      description: voyage.description || '',
      distance: voyage.distance || 0,
      emissions: voyage.emissions || 0,
      emissions_hfo: voyage.emissions_hfo || 0,
      emissions_lfo: voyage.emissions_lfo || 0,
      emissions_mdo: voyage.emissions_mdo || 0,
      emissions_mgo: voyage.emissions_mgo || 0,
      fuel_consumption: voyage.fuel_consumption || 0,
      fuel_consumption_hfo: voyage.fuel_consumption_hfo || voyage.fuel_consumption_HFO || 0,
      fuel_consumption_lfo: voyage.fuel_consumption_lfo || voyage.fuel_consumption_LFO || 0,
      fuel_consumption_mdo: voyage.fuel_consumption_mdo || voyage.fuel_consumption_MDO || 0,
      fuel_consumption_mgo: voyage.fuel_consumption_mgo || 0,
      fuel_types: voyage.fuel_types || [],
      time_sailed_days: days,
      time_sailed_hours: hours,
      time_sailed_minutes: minutes,
      transport_work: voyage.transport_work || 0,
      voyage_type: voyage.voyage_type || 'none',
      voyage_number: voyage.voyage_number || 0,
      approved_mrv: approvedMrv,
    });

    // Reset fuel types to initial state
    setActiveFuelTypes(initialActiveFuelTypes);
    setAvailableFuelTypes(['hfo', 'mdo', 'lfo'].filter((type) => !initialActiveFuelTypes.includes(type)));
    setSelectedFuelType('');
  };

  // Handle dialog close
  const handleDialogOpenChange = (open: boolean) => {
    setOpen(open);
    if (open) {
      // Reset form when opening the dialog to ensure fresh data
      resetForm();
    } else {
      // Reset form when closing the dialog to discard any unsaved changes
      resetForm();
    }
  };

  // Helper: get min/max date for selected year
  const getYearDateRange = (yearStr: string | undefined) => {
    if (!yearStr || isNaN(Number(yearStr))) return { minDate: undefined, maxDate: undefined };
    const y = Number(yearStr);
    return {
      minDate: new Date(y, 0, 1, 0, 0, 0, 0),
      maxDate: new Date(y, 11, 31, 23, 59, 59, 999),
    };
  };
  const { minDate, maxDate } = getYearDateRange(year);

  return (
    <>
      <Button variant='ghost' size='icon' onClick={() => setOpen(true)}>
        <Pencil className='h-4 w-4 text-blue-500' />
      </Button>

      <Dialog open={open} onOpenChange={handleDialogOpenChange}>
        <DialogTrigger />
        <DialogContent className='max-w-3xl'>
          <DialogHeader>
            <DialogTitle>Edit Voyage</DialogTitle>
            <DialogDescription>Update the voyage details below.</DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              {/* Ports, Times, Distance and Transport - 2 columns */}
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-4'>
                  {/* Departure Port */}
                  <FormField
                    control={form.control}
                    name='departure_port'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Departure Port</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Departure Time */}
                  <FormDateTimePicker control={form.control} name='start_time' label='Departure Time' fromDate={minDate} toDate={maxDate} />

                  {/* Distance (using cargo field) */}
                  <FormField
                    control={form.control}
                    name='distance'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Distance</FormLabel>
                        <FormControl>
                          <div className='relative'>
                            <Input
                              type='number'
                              {...field}
                              value={field.value === 0 ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : Number(e.target.value);
                                field.onChange(value);
                              }}
                              className='pr-12'
                            />
                            <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                              nm
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='space-y-4'>
                  {/* Destination Port */}
                  <FormField
                    control={form.control}
                    name='destination_port'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Destination Port</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Arrival Time */}
                  <FormDateTimePicker control={form.control} name='end_time' label='Arrival Time' fromDate={minDate} toDate={maxDate} />

                  {/* Time fields */}
                  <div>
                    <div className='grid grid-cols-3 gap-2'>
                      <FormField
                        control={form.control}
                        name='time_sailed_days'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Days</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name='time_sailed_hours'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Hours</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name='time_sailed_minutes'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minutes</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Voyage Information - 3 columns */}
              <div className='grid grid-cols-3 gap-4'>
                {/* Cargo */}
                <FormField
                  control={form.control}
                  name='cargo'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cargo</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            {...field}
                            value={field.value === 0 ? '' : field.value}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                              field.onChange(value);
                            }}
                            className='pr-8'
                          />
                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                            t
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Transport Work */}
                <FormField
                  control={form.control}
                  name='transport_work'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Transport Work</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            {...field}
                            value={field.value === 0 ? '' : field.value || ''}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                              field.onChange(value);
                            }}
                            className='pr-16'
                          />
                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                            t / nm
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Voyage Type */}
                <FormField
                  control={form.control}
                  name='voyage_type'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Voyage Type</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value || 'none'}>
                          <SelectTrigger className='w-full min-w-[140px]'>
                            <SelectValue placeholder='Select voyage type' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>None</SelectItem>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>EU Voyages</SelectLabel>
                              <SelectItem value='Towards EU'>Towards EU</SelectItem>
                              <SelectItem value='Between EU'>Between EU</SelectItem>
                              <SelectItem value='Outbound EU'>Outbound EU</SelectItem>
                              <SelectItem value='at Berth EU'>at Berth EU</SelectItem>
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>UK Voyages</SelectLabel>
                              <SelectItem value='Towards UK'>Towards UK</SelectItem>
                              <SelectItem value='Between UK'>Between UK</SelectItem>
                              <SelectItem value='Outbound UK'>Outbound UK</SelectItem>
                              <SelectItem value='at Berth UK'>at Berth UK</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Fuel Types and Consumption */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-sm font-medium'>Fuel Consumption and Emission</h3>
                  {availableFuelTypes.length > 0 && (
                    <div className='flex items-center gap-2'>
                      <Select
                        onValueChange={(value) => {
                          setSelectedFuelType(value);
                        }}
                        value={selectedFuelType}
                      >
                        <SelectTrigger className='w-[120px]'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableFuelTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          if (selectedFuelType) {
                            addFuelType(selectedFuelType);
                            setSelectedFuelType('');
                          }
                        }}
                        disabled={!selectedFuelType}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add
                      </Button>
                    </div>
                  )}
                </div>

                {activeFuelTypes.length === 0 ? (
                  <div className='text-muted-foreground rounded-md border border-dashed p-6 text-center text-sm'>
                    No fuel types added. Click the &quot;Add&quot; button to add a fuel type.
                  </div>
                ) : (
                  <div>
                    {/* Fuel cards in a grid layout */}
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                      {activeFuelTypes.map((fuelType) => (
                        <div key={fuelType} className={`rounded-md border p-4 ${activeFuelTypes.length === 1 ? 'md:col-span-2' : ''}`}>
                          <div className='mb-3 flex items-center justify-between'>
                            <h4 className='font-medium'>{fuelType.toUpperCase()}</h4>
                            <Button type='button' variant='ghost' size='sm' onClick={() => removeFuelType(fuelType)}>
                              <Trash2 className='h-4 w-4 text-red-500' />
                            </Button>
                          </div>

                          <div className='space-y-3'>
                            <FormField
                              control={form.control}
                              name={`fuel_consumption_${fuelType}` as keyof VoyagePutDTO}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Consumption</FormLabel>
                                  <FormControl>
                                    <div className='relative'>
                                      <Input
                                        type='number'
                                        {...field}
                                        value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                        onChange={(e) => {
                                          const value = e.target.value === '' ? 0 : Number(e.target.value);
                                          field.onChange(value);
                                        }}
                                        className='pr-8'
                                      />
                                      <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                        t
                                      </div>
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={`emissions_${fuelType}` as keyof VoyagePutDTO}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>CO2 Emission</FormLabel>
                                  <FormControl>
                                    <div className='relative'>
                                      <Input
                                        type='number'
                                        {...field}
                                        value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                        readOnly
                                        className='bg-gray-50 pr-8'
                                      />
                                      <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                        t
                                      </div>
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Total values in a separate section below */}
                    <div className='mt-4 grid grid-cols-2 gap-4 border-t pt-4'>
                      <FormField
                        control={form.control}
                        name='fuel_consumption'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total Consumption</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='number'
                                  {...field}
                                  value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                  readOnly
                                  className='bg-gray-50 pr-8 font-medium'
                                />
                                <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                  t
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='emissions'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total CO2 Emission</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='number'
                                  {...field}
                                  value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                  readOnly
                                  className='bg-gray-50 pr-8 font-medium'
                                />
                                <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                  t
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Approved MRV Voyage Checkbox */}
              <FormField
                control={form.control}
                name='approved_mrv'
                render={({ field }) => {
                  // Check if voyage has Error or Incomplete status
                  const status = form.getValues('voyage_status');
                  const hasErrorStatus = status === 'Error';
                  const isIncompleteStatus = status === 'Incomplete';

                  // Get current form values to check completeness in real-time
                  const currentValues = form.getValues();
                  const tempVoyage = {
                    ...voyage,
                    ...currentValues,
                    fuel_consumption_HFO: currentValues.fuel_consumption_HFO || currentValues.fuel_consumption_hfo || 0,
                    fuel_consumption_LFO: currentValues.fuel_consumption_LFO || currentValues.fuel_consumption_lfo || 0,
                    fuel_consumption_MDO: currentValues.fuel_consumption_MDO || currentValues.fuel_consumption_mdo || 0,
                    emissions: currentValues.emissions || 0,
                  };

                  const realTimeComplete = isVoyageComplete(tempVoyage);

                  // Disable checkbox if voyage is not complete, has Error, or is Incomplete status
                  const isDisabled = !realTimeComplete || hasErrorStatus || isIncompleteStatus;

                  // Force uncheck for Error status or when voyage becomes incomplete
                  if ((hasErrorStatus || !realTimeComplete) && field.value) {
                    setTimeout(() => {
                      form.setValue('approved_mrv', false);
                    }, 0);
                  }

                  return (
                    <FormItem className='flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4'>
                      <FormControl>
                        <Checkbox
                          checked={hasErrorStatus || !realTimeComplete ? false : field.value}
                          onCheckedChange={hasErrorStatus || !realTimeComplete ? () => {} : field.onChange}
                          disabled={isDisabled}
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel className={isDisabled ? 'text-muted-foreground' : ''}>Approved MRV Voyage</FormLabel>
                        <p className='text-muted-foreground text-sm'>
                          {!realTimeComplete || isIncompleteStatus
                            ? 'Complete all required fields to enable approval'
                            : hasErrorStatus
                              ? 'Voyages with Error status cannot be approved'
                              : 'Mark this voyage as approved for MRV reporting'}
                        </p>
                      </div>
                    </FormItem>
                  );
                }}
              />

              <DialogFooter>
                <Button type='button' variant='outline' onClick={() => handleDialogOpenChange(false)}>
                  Cancel
                </Button>
                <Button type='submit'>Save</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
