'use client';

import Highcharts from 'highcharts';

// Define consistent colors matching the CII charts
export const COLORS = {
  blue: 'oklch(62.3% 0.214 259.815)',
  yellow: 'oklch(79.5% 0.184 86.047)',
};

// Helper function to parse timestamp as UTC
export const parseUTCTimestamp = (timestamp: string) => {
  // Parse the timestamp as UTC
  const [datePart, timePart] = timestamp.split(' ');
  const [year, month, day] = datePart.split('-').map(Number);
  const [hours, minutes, seconds] = timePart ? timePart.split(':').map(Number) : [0, 0, 0];

  // Create a UTC date (months are 0-indexed in JavaScript Date)
  return Date.UTC(year, month - 1, day, hours, minutes, seconds);
};

// Helper function to get week number from date
export const getWeekNumber = (d: Date) => {
  // Copy date to avoid modifying the original
  const target = new Date(d.valueOf());
  // ISO week starts on Monday
  const dayNr = (d.getDay() + 6) % 7;
  // Set to nearest Thursday (current date + 4 - current day number)
  target.setDate(target.getDate() - dayNr + 3);
  // Get first day of year
  const firstThursday = new Date(target.getFullYear(), 0, 4);
  // First Thursday of the year (ISO 8601 standard)
  firstThursday.setDate(firstThursday.getDate() - ((firstThursday.getDay() + 6) % 7) + 3);
  // Get the interval between target and first Thursday
  const diff = (target.getTime() - firstThursday.getTime()) / 86400000;
  // Return 1 + number of weeks
  return 1 + Math.round(diff / 7);
};

// Helper function to format tooltip HTML
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const formatTooltipHTML = function (this: any) {
  if (!this.points) return '';

  const date = new Date(this.x);
  const year = date.getFullYear();
  const weekNumber = getWeekNumber(date);

  // Build tooltip HTML
  let tooltipHTML = `<div style="font-size: 12px;">
    <span style="font-weight: bold;">Year: ${year}, Week: ${weekNumber}</span><br/>`;

  // Add each point's data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  this.points.forEach((point: any) => {
    const series = point.series;
    const color = series.color;
    const name = series.name;

    if (series.type === 'arearange') {
      // For arearange series, the low and high values are directly on the point
      tooltipHTML += `<span style="color:${color}">●</span> ${name}: <b>${Highcharts.numberFormat(point.low, 2)} - ${Highcharts.numberFormat(point.high, 2)} kW</b><br/>`;
    } else {
      tooltipHTML += `<span style="color:${color}">●</span> ${name}: <b>${Highcharts.numberFormat(point.y, 2)} kW</b><br/>`;
    }
  });

  tooltipHTML += '</div>';
  return tooltipHTML;
};

// X-axis label formatter that shows week numbers in original format with year indicators
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const formatXAxisLabel = function (this: any) {
  const value = this.value;
  if (!value) return '';

  const date = new Date(value);
  const isFirstTick = this.isFirst;
  const weekNumber = getWeekNumber(date);
  const year = date.getFullYear();

  // Check if this is the first week of a new year (week 1)
  const isFirstWeekOfYear = weekNumber === 1;

  // Show year with week number for first tick or first week of a new year
  if (isFirstTick || isFirstWeekOfYear) {
    return `<span style="color: #2b5797;">${year}</span> ${weekNumber}`;
  }

  // For other ticks, show just the week number (original format)
  return `${weekNumber}`;
};
