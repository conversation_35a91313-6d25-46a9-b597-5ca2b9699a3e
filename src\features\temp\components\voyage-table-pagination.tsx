'use client';

import { useVoyageStore } from '../voyage.store';
import { PageSizeSelector } from '@/components/ui-extensions/page-size-selector';
import { PaginationControls } from '@/components/ui-extensions/pagination-controls';

export function VoyageTablePagination() {
  const { filteredVoyages, pageSize, pageIndex, setPageSize, setPageIndex, isLoading } = useVoyageStore();

  return (
    <div className='flex justify-end gap-4'>
      <PageSizeSelector
        pageSize={pageSize}
        onPageSizeChange={(size) => {
          setPageSize(size);
          setPageIndex(0);
        }}
        pageSizeOptions={[5, 10, 25, 50]}
        disabled={isLoading}
      />
      <PaginationControls
        pageIndex={pageIndex}
        pageCount={Math.ceil(filteredVoyages.length / pageSize)}
        onPreviousPage={() => setPageIndex(Math.max(pageIndex - 1, 0))}
        onNextPage={() => setPageIndex(Math.min(pageIndex + 1, Math.ceil(filteredVoyages.length / pageSize) - 1))}
      />
    </div>
  );
}
