'use client';

import { Input } from '@/components/ui/input';
import { useUserStore } from '../user.store';

export function UserSearch() {
  const { searchQuery, setSearchQuery, isLoading } = useUserStore();

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;

    setSearchQuery(value);
  };

  return (
    <Input
      type='text'
      placeholder='Search by name or email...'
      value={searchQuery}
      onChange={handleInputChange}
      className='w-full md:w-80'
      disabled={isLoading}
    />
  );
}
