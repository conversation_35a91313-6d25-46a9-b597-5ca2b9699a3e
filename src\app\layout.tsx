import type { Metada<PERSON> } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import '@/styles/globals.css';
import { ThemeProvider } from '@/components/layout/theme-provider';
import { Toaster } from '@/components/ui/sonner';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Fuel Optimization Dashboard | Shipping Efficiency',
  description:
    'Monitor and optimize fuel consumption for your fleet with real-time analytics, predictive maintenance, and anomaly detection. Designed for the shipping industry.',
  keywords: [
    'fuel optimization',
    'shipping efficiency',
    'maritime analytics',
    'predictive maintenance',
    'fleet management',
    'anomaly detection',
  ],
  robots: {
    index: false,
    follow: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <ThemeProvider attribute='class' defaultTheme='light' enableSystem={false} disableTransitionOnChange={true}>
          {children}
          <Toaster richColors closeButton />
        </ThemeProvider>
      </body>
    </html>
  );
}
