'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import {
  BrainCircuit,
  ChartColumnBig,
  ChartNoAxesCombined,
  ChevronRight,
  HardDriveDownload,
  Home,
  NotebookText,
  Users,
} from 'lucide-react';

interface NavItem {
  title: string;
  url?: string;
  icon: React.ComponentType;
  items?: { title: string; url: string }[];
}

const items: NavItem[] = [
  {
    title: 'Home',
    url: '/dashboard',
    icon: Home,
  },
  {
    title: 'Data Analytics',
    url: '/dashboard/data-analytics',
    icon: ChartNoAxesCombined,
  },
  {
    title: 'CII',
    url: '/dashboard/cii',
    icon: ChartColumnBig,
  },
  {
    title: 'Predictive Maintenance',
    icon: BrainCircuit,
    items: [
      {
        title: 'Anomaly Detection',
        url: '/dashboard/predictive-maintenance/anomaly-detection',
      },
      {
        title: 'Hull Performance',
        url: '/dashboard/predictive-maintenance/hull-performance',
      },
    ],
  },
  { title: 'MRV', url: '/dashboard/mrv', icon: NotebookText },
  { title: 'Users', url: '/dashboard/users', icon: Users },
  { title: 'Export', url: '/dashboard/export', icon: HardDriveDownload },
];

export function NavMain() {
  const pathname = usePathname(); // Get the current path

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Navigation</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = item.url ? pathname === item.url : false;
          const hasActiveChild = item.items?.some((subItem) => pathname === subItem.url) ?? false;

          return (
            <Collapsible key={item.title} asChild defaultOpen={isActive || hasActiveChild}>
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip={item.title} className={item.items?.length ? 'pointer-events-none' : ''}>
                  {item.url ? (
                    <Link
                      href={item.url}
                      className={isActive ? 'bg-primary text-sidebar-accent hover:!bg-primary hover:!text-sidebar-accent' : ''}
                    >
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  ) : (
                    <div>
                      <item.icon />
                      <span>{item.title}</span>
                    </div>
                  )}
                </SidebarMenuButton>

                {item.items && (
                  <>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuAction className='cursor-pointer data-[state=open]:rotate-90'>
                        <ChevronRight />
                        <span className='sr-only'>Toggle</span>
                      </SidebarMenuAction>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items.map((subItem) => {
                          const isSubActive = pathname === subItem.url;

                          return (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton asChild>
                                <Link
                                  href={subItem.url}
                                  className={
                                    isSubActive ? 'bg-primary !text-sidebar-accent hover:!bg-primary hover:!text-sidebar-accent' : ''
                                  }
                                >
                                  {' '}
                                  {subItem.title}{' '}
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          );
                        })}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </>
                )}
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
