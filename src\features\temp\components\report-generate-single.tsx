'use client';

import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { Button } from '@/components/ui/button';
import { FileDown } from 'lucide-react';
import React from 'react';
import { toast } from 'sonner';
import { useVoyageStore } from '../voyage.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { isVoyageComplete } from '../voyage.utils';

export function ReportGenerateSingle({ voyageId }: { voyageId: number }) {
  const [isLoading, setIsLoading] = React.useState(false);

  // Get voyage and status up front for button state
  const { voyages } = useVoyageStore.getState();
  const voyage = voyages.find((v) => v.id === voyageId);
  const isError = voyage?.voyage_status === 'Error';
  const isIncomplete = !voyage || !isVoyageComplete(voyage);
  const isDisabled = isLoading || isError || isIncomplete;

  const handleClick = async () => {
    setIsLoading(true);
    try {
      // Find the voyage with the given ID to get its UUID
      const { voyages, year } = useVoyageStore.getState();
      const voyage = voyages.find((v) => v.id === voyageId);

      if (!voyage) {
        throw new Error('Voyage not found');
      }

      // Get the current vessel after ensuring it's loaded
      const currentVessel = useVesselStore.getState().currentVessel;

      if (!currentVessel?.imo) {
        throw new Error('No vessel selected or vessel has no IMO');
      }

      // Create a blob URL for the PDF
      // Generate the report URL with query parameters
      const reportUrl = `/api/mrv/voyage-report?vessel_imo=${currentVessel.imo}&vessel_name=${encodeURIComponent(
        currentVessel.vessel_name || ''
      )}&voyage_id=${voyage.uuid}&year=${year}&owner_vat=${currentVessel.assigned_owner_vat}`;

      // Fetch the PDF file
      const response = await fetch(reportUrl);

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Format the start time for the filename
      const startTime = new Date(voyage.start_time).toISOString().split('T')[0];

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `${currentVessel.vessel_name || 'vessel'}-${startTime}.pdf`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Report download successful');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button variant='ghost' size='icon' onClick={handleClick} disabled={isDisabled}>
      {isLoading ? <CircleSpinner variant='primary' size='xs' /> : <FileDown className='h-4 w-4 text-blue-500' />}
    </Button>
  );
}
