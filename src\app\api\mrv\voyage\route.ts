// NOTES: These endpoints do not yet use zod for body validation.

import { MrvVoyage } from '@/features/mrv/mrv.types';
import { httpClient } from '@/lib/http-client';
import { HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === POST QUERY SCHEMA ===
const postQuerySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  year: z.string().nonempty('year must be provided'),
});

// === PUT QUERY SCHEMA ===
const putQuerySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  year: z.string().nonempty('year must be provided'),
  voyage_id: z.string().nonempty('voyage_id must be provided'),
});

// === DELETE QUERY SCHEMA ===
const deleteQuerySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  year: z.string().nonempty('year must be provided'),
  voyage_id: z.string().nonempty('voyage_id must be provided'),
});

// === POST ENDPOINT ===
export async function POST(request: NextRequest) {
  // Validate query params
  const validatedQuery = postQuerySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/mrv/voyage', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Body
  const requestBody = await request.json();

  // Make request
  try {
    const res = await httpClient.post<{ voyage: Partial<MrvVoyage> }>(request, url.toString(), requestBody);
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Failed to create voyage' }, { status: 500 });
  }
}

// === PUT ENDPOINT ===
export async function PUT(request: NextRequest) {
  // Validate query params
  const validatedQuery = putQuerySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/mrv/voyage', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Body
  const requestBody = await request.json();

  // Make request
  try {
    const res = await httpClient.put<{ voyage: Partial<MrvVoyage> }>(request, url.toString(), requestBody);
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Failed to update voyage' }, { status: 500 });
  }
}

// === DELETE ENDPOINT ===
export async function DELETE(request: NextRequest) {
  // Validate query params
  const validatedQuery = deleteQuerySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/mrv/voyage', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.delete(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Failed to delete voyage' }, { status: 500 });
  }
}
