'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { LatLngExpression, LatLngBounds, divIcon } from 'leaflet';
import { useEffect } from 'react';
import 'leaflet/dist/leaflet.css';

import { useVesselStore } from '@/features/vessels/vessel.store';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';

function FitBounds({ positions }: { positions: LatLngExpression[] }) {
  const map = useMap();

  useEffect(() => {
    if (positions.length > 0) {
      const bounds = new LatLngBounds(positions);
      map.fitBounds(bounds, { padding: [50, 50] });
    }
  }, [positions, map]);

  return null;
}

export default function HomeFleetMap() {
  const { vessels } = useVesselStore();

  if (!vessels || vessels.length === 0) {
    return (
      <div className='bg-card text-card-foreground flex h-[360px] w-full items-center justify-center rounded-xl border'>
        <CircleSpinner variant='primary' />
      </div>
    );
  }

  const vesselPositions: LatLngExpression[] = vessels
    .map((vessel) => {
      const ais = vessel.ais_data?.[0];
      const frugal = vessel.latest_row?.[0];
      const isAisNewer = ais?.timestamp && frugal?.timestamp ? new Date(ais.timestamp) > new Date(frugal.timestamp) : true;
      const latest = isAisNewer ? ais : frugal;
      if (!latest?.latitude || !latest?.longitude) return null;
      return [latest.latitude, latest.longitude] as LatLngExpression;
    })
    .filter((pos): pos is LatLngExpression => !!pos);

  const defaultCenter: LatLngExpression = vesselPositions[0] ?? [0, 0];

  return (
    <div className='bg-card text-card-foreground relative overflow-hidden rounded-xl border'>
      <MapContainer center={defaultCenter} zoom={5} scrollWheelZoom style={{ height: '504px', width: '100%' }}>
        <TileLayer
          url='https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png'
          attribution='&copy; <a href="https://carto.com/">CARTO</a>'
        />
        <FitBounds positions={vesselPositions} />

        {vessels.map((vessel) => {
          const ais = vessel.ais_data?.[0];
          const frugal = vessel.latest_row?.[0];
          const isAisNewer = ais?.timestamp && frugal?.timestamp ? new Date(ais.timestamp) > new Date(frugal.timestamp) : true;
          const latest = isAisNewer ? ais : frugal;
          if (!latest?.latitude || !latest?.longitude) return null;

          const position: LatLngExpression = [latest.latitude, latest.longitude];
          const heading = latest.heading ?? 0;
          const updatedFrom = isAisNewer ? 'AIS' : 'Frugal Systems';
          const navStatus = isAisNewer ? ais?.navigation_status : (frugal?.nav_status ?? 'unknown');
          const destination = isAisNewer ? ais?.dest_port_name : (frugal?.destination ?? 'unknown');
          const status = vessel.latest_row?.[0]?.frugal_status === 1 ? 'Engaged' : 'Disengaged';
          const etaRaw = isAisNewer ? ais?.eta_utc : (frugal?.eta ?? '');
          const eta = etaRaw?.replace('T', ' ').slice(0, 19) || 'unknown';
          const timestamp = latest.timestamp?.replace('T', ' ').slice(0, 19) ?? 'unknown';

          const shipIcon = divIcon({
            className: '',
            html: `
              <div style="position: relative; width: 20px; height: 20px;">
                <div style="transform: rotate(${heading}deg); width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                  <img src="/map/pointer.svg" style="width: 12px; height: 20px;" />
                </div>
                <div style="position: absolute; top: 80%; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 11px; background: white; padding: 1px 4px; border-radius: 3px; margin-top: 2px; box-shadow: 0 0 2px rgba(0, 0, 0, 0.2); pointer-events: none;">
                  ${vessel.vessel_name ?? ''}
                </div>
              </div>
            `,
            iconSize: [20, 20],
            iconAnchor: [10, 10],
          });

          return (
            <Marker key={vessel.id} position={position} icon={shipIcon}>
              <Popup>
                <div className='text-muted-foreground text-sm'>
                  <table className='w-full text-xs'>
                    <tbody>
                      <PopupRow label='Position' value={`[${position[0]}, ${position[1]}]`} />
                      <PopupRow label='IMO' value={vessel.imo} />
                      <PopupRow label='Frugal Status' value={status} />
                      <PopupRow label='Navigation Status' value={navStatus} />
                      <PopupRow label='Destination' value={destination} />
                      <PopupRow label='ETA' value={eta} />
                      <PopupRow label='Last Update' value={`${updatedFrom}: ${timestamp}`} />
                    </tbody>
                  </table>
                </div>
              </Popup>
            </Marker>
          );
        })}
      </MapContainer>
    </div>
  );
}

function PopupRow({ label, value }: { label: string; value?: string | number }) {
  return (
    <tr>
      <td className='px-2 py-1 text-left align-top font-medium whitespace-nowrap text-gray-700'>{label}:</td>
      <td className='px-2 py-1 text-left break-all text-gray-900'>{value ?? '—'}</td>
    </tr>
  );
}
