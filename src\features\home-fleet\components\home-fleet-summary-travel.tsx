'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useHomeFleetStore } from '../home-fleet.store';

export const HomeFleetSummaryTravel = () => {
  const { fleetData, isLoadingFleetData } = useHomeFleetStore();

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <h2 className='text-md mb-4 font-semibold tracking-tight'>Travel Summary</h2>
      <div className='space-y-3'>
        <StatValue
          label='Distance Traveled'
          value={fleetData?.fleet_fc_yearly_overview.distance ?? 'N/A'}
          units='nm'
          isLoading={isLoadingFleetData}
        />
      </div>
    </div>
  );
};
