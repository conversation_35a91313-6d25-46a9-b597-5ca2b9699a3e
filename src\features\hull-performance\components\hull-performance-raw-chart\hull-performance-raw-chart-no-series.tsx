'use client';

export interface NoSeriesEnabledProps {
  container: HTMLElement;
}

export const handleNoSeriesEnabled = ({ container }: NoSeriesEnabledProps) => {
  if (!container) return;

  // Find the chart area (excluding legend)
  const chartArea = container.querySelector('.highcharts-plot-background');
  if (!chartArea) return;

  // Hide the plot area and axes but keep the legend
  const elementsToHide = container.querySelectorAll(
    '.highcharts-plot-background, .highcharts-plot-border, ' +
      '.highcharts-series, .highcharts-grid, .highcharts-axis, ' +
      '.highcharts-axis-labels, .highcharts-markers, .highcharts-plot-lines-0'
  );

  elementsToHide.forEach((el: Element) => {
    (el as HTMLElement).style.display = 'none';
  });

  // If placeholder doesn't exist, create it
  let placeholder = container.querySelector('.chart-placeholder') as HTMLElement | null;
  if (!placeholder) {
    placeholder = document.createElement('div');
    placeholder.className = 'chart-placeholder';
    placeholder.style.position = 'absolute';
    placeholder.style.top = '50px'; // Position below the title
    placeholder.style.left = '0';
    placeholder.style.width = '100%';
    placeholder.style.height = 'calc(100% - 50px)'; // Adjust for title
    placeholder.style.display = 'flex';
    placeholder.style.alignItems = 'center';
    placeholder.style.justifyContent = 'center';

    // Create the placeholder content
    const placeholderContent = document.createElement('div');
    placeholderContent.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center;">
        <div style="position: relative; margin-bottom: 1rem; width: 6rem; height: 6rem;">
          <div style="position: absolute; bottom: 0; left: 0; height: 20%; width: 15%; background-color: var(--muted); opacity: 0.9; border-radius: 0.125rem;"></div>
          <div style="position: absolute; bottom: 0; left: 25%; height: 40%; width: 15%; background-color: var(--muted); opacity: 0.9; border-radius: 0.125rem;"></div>
          <div style="position: absolute; bottom: 0; left: 50%; height: 60%; width: 15%; background-color: var(--muted); opacity: 0.9; border-radius: 0.125rem;"></div>
          <div style="position: absolute; bottom: 0; left: 75%; height: 80%; width: 15%; background-color: var(--muted); opacity: 0.9; border-radius: 0.125rem;"></div>
        </div>
        <p style="color: var(--muted-foreground); text-align: center; font-size: 1.125rem;">No Data Series Enabled</p>
      </div>
    `;
    placeholder.appendChild(placeholderContent);
    container.appendChild(placeholder);
  } else {
    placeholder.style.display = 'flex';
  }
};

export const handleShowAllSeries = ({ container }: NoSeriesEnabledProps) => {
  if (!container) return;

  // Show all elements and hide placeholder if it exists
  const elementsToShow = container.querySelectorAll(
    '.highcharts-plot-background, .highcharts-plot-border, ' +
      '.highcharts-series, .highcharts-grid, .highcharts-axis, ' +
      '.highcharts-axis-labels, .highcharts-markers, .highcharts-plot-lines-0'
  );

  elementsToShow.forEach((el: Element) => {
    (el as HTMLElement).style.display = '';
  });

  // Hide placeholder if it exists
  const placeholder = container.querySelector('.chart-placeholder') as HTMLElement | null;
  if (placeholder) {
    placeholder.style.display = 'none';
  }
};
