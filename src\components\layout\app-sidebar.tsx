import { NavMain } from '@/components/layout/nav-main';
import { NavUser } from '@/components/layout/nav-user';
import { Sidebar, SidebarContent, SidebarHeader, SidebarFooter, SidebarGroup, SidebarGroupLabel } from '@/components/ui/sidebar';
import { OwnerSwitcher } from './owner-switcher';
import { VesselSwitcher } from './vessel-switcher';
import Image from 'next/image';
import { getServerSession } from 'next-auth';
import SessionWrapper from '../wrappers/session-wrapper';
export async function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const session = await getServerSession();
  const user = {
    name: session?.user?.name ?? '',
    email: session?.user?.email ?? '',
    image: session?.user?.image ?? '',
  };

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <Image src='/svg/logo-frugal-full.svg' alt='Logo' className='mt-4 w-full px-4' width={0} height={0} />
      </SidebarHeader>
      <SidebarContent className='p-2'>
        <NavMain />
        <SidebarGroup>
          <SidebarGroupLabel>Ship Owners / Vessels</SidebarGroupLabel>
          <SessionWrapper>
            <OwnerSwitcher />
          </SessionWrapper>
          <VesselSwitcher />
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className='p-4'>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  );
}
