import { AppSidebar } from '@/components/layout/app-sidebar';
import { Breadcrumbs } from '@/components/layout/breadcrumbs';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/layout/theme-toggle';

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className='flex h-screen flex-col'>
        <header className='flex h-16 shrink-0 items-center gap-2 border-b px-4'>
          <SidebarTrigger />
          <Separator orientation='vertical' className='mr-2 data-[orientation=vertical]:h-4' />
          <Breadcrumbs />
          <ThemeToggle className='ml-auto' />
        </header>
        <div className='grow-1 overflow-y-auto p-4'>{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
