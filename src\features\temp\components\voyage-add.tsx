'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>alog, DialogTrigger, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { useVoyageStore } from '@/features/temp/voyage.store';
import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectSeparator,
  SelectGroup,
} from '@/components/ui/select';
import { VoyagePostDTO, VoyageStatus } from '../voyage.types';
import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { Plus, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

// Fuel emission coefficients
const fuelCoefficients = {
  mdo: 3.206,
  lfo: 3.151,
  hfo: 3.114,
};

export function VoyageAdd() {
  const [open, setOpen] = useState(false);
  const { createVoyage, isLoading, year } = useVoyageStore();

  // State to track active fuel types
  const [activeFuelTypes, setActiveFuelTypes] = useState<string[]>([]);
  const [availableFuelTypes, setAvailableFuelTypes] = useState<string[]>(['mdo', 'hfo', 'lfo']);
  const [selectedFuelType, setSelectedFuelType] = useState<string>('');

  // Define form type to handle dynamic fuel fields
  type VoyageFormValues = {
    departure_port: string;
    destination_port: string;
    start_time: Date;
    end_time: Date;
    voyage_status: string;
    cargo: number;
    description: string;
    distance: number;
    emissions: number;
    fuel_consumption: number;
    fuel_types: string[];
    time_sailed_days: number;
    time_sailed_hours: number;
    time_sailed_minutes: number;
    transport_work: number;
    voyage_type: string;
    voyage_number: number;
    approved_mrv: boolean;
    emissions_hfo?: number;
    emissions_lfo?: number;
    emissions_mdo?: number;
    emissions_mgo?: number;
    fuel_consumption_hfo?: number;
    fuel_consumption_lfo?: number;
    fuel_consumption_mdo?: number;
    fuel_consumption_mgo?: number;
    [key: `fuel_consumption_${string}`]: number | undefined;
    [key: `emissions_${string}`]: number | undefined;
  };

  // Calculate initial time sailed values
  const calculateTimeSailed = (startTime: Date, endTime: Date) => {
    // Calculate the time difference in milliseconds
    const timeDiff = endTime.getTime() - startTime.getTime();

    // Skip calculation if end time is before start time
    if (timeDiff < 0) return { days: 0, hours: 0, minutes: 0 };

    // Convert to days, hours, minutes
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes };
  };

  // Get initial time values
  const initialStartTime = new Date();
  const initialEndTime = new Date();
  const { days, hours, minutes } = calculateTimeSailed(initialStartTime, initialEndTime);

  const form = useForm<VoyageFormValues>({
    defaultValues: {
      departure_port: '',
      destination_port: '',
      start_time: initialStartTime,
      end_time: initialEndTime,
      voyage_status: 'Incomplete',
      cargo: 0,
      description: '',
      distance: 0,
      emissions: 0,
      emissions_hfo: 0,
      emissions_lfo: 0,
      emissions_mdo: 0,
      emissions_mgo: 0,
      fuel_consumption: 0,
      fuel_consumption_hfo: 0,
      fuel_consumption_lfo: 0,
      fuel_consumption_mdo: 0,
      fuel_consumption_mgo: 0,
      fuel_types: [],
      time_sailed_days: days,
      time_sailed_hours: hours,
      time_sailed_minutes: minutes,
      transport_work: 0,
      voyage_type: 'none',
      voyage_number: 0,
      approved_mrv: false,
    },
  });

  // Calculate emissions based on fuel consumption
  const calculateEmissions = (fuelType: string, consumption: number): number => {
    if (!consumption) return 0;
    return Number((consumption * (fuelCoefficients[fuelType as keyof typeof fuelCoefficients] || 0)).toFixed(2));
  };

  // Add a new fuel type to the active list
  const addFuelType = (fuelType: string) => {
    if (fuelType && !activeFuelTypes.includes(fuelType)) {
      setActiveFuelTypes([...activeFuelTypes, fuelType]);
      setAvailableFuelTypes(availableFuelTypes.filter((type) => type !== fuelType));

      // Update the form's fuel_types array
      const currentValues = form.getValues();
      form.setValue('fuel_types', [...(currentValues.fuel_types || []), fuelType]);

      // Initialize the fuel consumption field to 0
      form.setValue(`fuel_consumption_${fuelType}`, 0);
      form.setValue(`emissions_${fuelType}`, 0);
    }
  };

  // Function to update total consumption and emissions
  const updateTotals = () => {
    const formData = form.getValues();

    // Calculate total consumption
    let totalConsumption = 0;
    let totalEmissions = 0;

    activeFuelTypes.forEach((type) => {
      const consumption = formData[`fuel_consumption_${type}`];
      if (typeof consumption === 'number') {
        totalConsumption += consumption;
      }

      const emissions = formData[`emissions_${type}`];
      if (typeof emissions === 'number') {
        totalEmissions += emissions;
      }
    });

    // Update the form with new totals without resetting
    form.setValue('fuel_consumption', Number(totalConsumption.toFixed(2)));
    form.setValue('emissions', Number(totalEmissions.toFixed(2)));
  };

  // Remove a fuel type from the active list
  const removeFuelType = (fuelType: string) => {
    setActiveFuelTypes(activeFuelTypes.filter((type) => type !== fuelType));
    setAvailableFuelTypes([...availableFuelTypes, fuelType]);

    // Update the form's fuel_types array
    const currentFuelTypes = form.getValues().fuel_types || [];
    form.setValue(
      'fuel_types',
      currentFuelTypes.filter((type) => type !== fuelType)
    );

    // Reset the consumption and emissions for this fuel type
    form.setValue(`fuel_consumption_${fuelType}`, 0);
    form.setValue(`emissions_${fuelType}`, 0);

    // Update total values
    updateTotals();
  };

  // Update emissions when fuel consumption changes and check completeness
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name && name.startsWith('fuel_consumption_')) {
        const fuelType = name.replace('fuel_consumption_', '');
        const consumption = value[name as keyof typeof value] as number;
        const emissions = calculateEmissions(fuelType, consumption);

        // Update the emissions value without resetting the form
        form.setValue(`emissions_${fuelType}`, emissions);

        // Update total values
        updateTotals();
      }
    });

    return () => subscription.unsubscribe();
  }, [form, activeFuelTypes]);

  // Update time sailed fields when departure or arrival time changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'start_time' || name === 'end_time') {
        const startTime = value.start_time as Date;
        const endTime = value.end_time as Date;

        if (startTime && endTime) {
          // Calculate the time difference in milliseconds
          const timeDiff = endTime.getTime() - startTime.getTime();

          // Skip calculation if end time is before start time
          if (timeDiff < 0) return;

          // Convert to days, hours, minutes
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

          // Update the form values
          form.setValue('time_sailed_days', days);
          form.setValue('time_sailed_hours', hours);
          form.setValue('time_sailed_minutes', minutes);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const onSubmit = async (data: VoyageFormValues) => {
    // Create a new object for the API request
    const apiData: VoyagePostDTO = {
      // Required fields from the form
      departure_port: data.departure_port || '',
      destination_port: data.destination_port || '',
      start_time: data.start_time ? format(new Date(data.start_time), 'yyyy-MM-dd HH:mm:ss') : format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      end_time: data.end_time ? format(new Date(data.end_time), 'yyyy-MM-dd HH:mm:ss') : format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      voyage_status: (data.voyage_status || 'Incomplete') as VoyageStatus,
      description: data.description || '',

      // Map form fields to API fields
      distance: data.distance || 0, // Use distance directly
      cargo: data.cargo || 0, // Map cargo field
      transport_work: data.transport_work || 0, // Map transport_work field
      approved_mrv_voyage: data.approved_mrv || false, // Map approved_mrv to approved_mrv_voyage
      voyage_type: data.voyage_type === 'none' ? '' : data.voyage_type || '', // Convert 'none' to empty string

      // Time fields
      time_sailed_days: data.time_sailed_days || 0,
      time_sailed_hours: data.time_sailed_hours || 0,
      time_sailed_minutes: data.time_sailed_minutes || 0,

      // Fuel consumption fields
      fuel_consumption: data.fuel_consumption || 0,
      fuel_types: data.fuel_types || [],

      // Map lowercase fuel consumption to uppercase for API
      fuel_consumption_HFO: data.fuel_consumption_hfo || 0,
      fuel_consumption_LFO: data.fuel_consumption_lfo || 0,
      fuel_consumption_MDO: data.fuel_consumption_mdo || 0,

      // Add any dynamic fuel consumption fields
      ...activeFuelTypes.reduce(
        (acc, type) => {
          const upperType = type.toUpperCase();
          acc[`fuel_consumption_${upperType}`] = data[`fuel_consumption_${type}`] || 0;
          return acc;
        },
        {} as Record<string, number>
      ),

      // Default values for required fields
      cargo_type: null,
      departure_location: data.departure_port || '',
      errors: [],
      fuel_consumption_at_berth: 0,
      fuel_consumption_at_sea: 0,
      mrv_fuel_consumption: data.fuel_consumption || 0,
      ongoing: false,
      time_at_berth_days: 0,
      time_at_berth_hours: 0,
      time_at_berth_minutes: 0,
      time_at_sea_days: 0,
      time_at_sea_hours: 0,
      time_at_sea_minutes: 0,
    };

    // Close the popup first
    setOpen(false);
    resetForm();

    // Then create the voyage
    await createVoyage(apiData);
  };

  // Function to reset form and state
  const resetForm = () => {
    // Recalculate time sailed values for new dates
    const resetStartTime = new Date();
    const resetEndTime = new Date();
    const { days, hours, minutes } = calculateTimeSailed(resetStartTime, resetEndTime);

    form.reset({
      departure_port: '',
      destination_port: '',
      start_time: resetStartTime,
      end_time: resetEndTime,
      voyage_status: 'Incomplete',
      cargo: 0,
      description: '',
      distance: 0,
      emissions: 0,
      emissions_hfo: 0,
      emissions_lfo: 0,
      emissions_mdo: 0,
      emissions_mgo: 0,
      fuel_consumption: 0,
      fuel_consumption_hfo: 0,
      fuel_consumption_lfo: 0,
      fuel_consumption_mdo: 0,
      fuel_consumption_mgo: 0,
      fuel_types: [],
      time_sailed_days: days,
      time_sailed_hours: hours,
      time_sailed_minutes: minutes,
      transport_work: 0,
      voyage_type: 'none',
      voyage_number: 0,
      approved_mrv: false,
    });
    setActiveFuelTypes([]);
    setAvailableFuelTypes(['mdo', 'hfo', 'lfo']);
    setSelectedFuelType('');
  };

  // Helper: get min/max date for selected year
  const getYearDateRange = (yearStr: string | undefined) => {
    if (!yearStr || isNaN(Number(yearStr))) return { minDate: undefined, maxDate: undefined };
    const y = Number(yearStr);
    return {
      minDate: new Date(y, 0, 1, 0, 0, 0, 0),
      maxDate: new Date(y, 11, 31, 23, 59, 59, 999),
    };
  };
  const { minDate, maxDate } = getYearDateRange(year);

  // Handle dialog close
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    setOpen(open);
  };

  return (
    <>
      <Button variant='outline' onClick={() => setOpen(true)} disabled={isLoading}>
        Add Voyage
      </Button>

      <Dialog open={open} onOpenChange={handleDialogOpenChange}>
        <DialogTrigger />
        <DialogContent className='max-w-3xl'>
          <DialogHeader>
            <DialogTitle>Create a New Voyage</DialogTitle>
            <DialogDescription>If you need to add a new voyage, please fill out the form below.</DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              {/* Ports, Times, Distance and Transport - 2 columns */}
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-4'>
                  {/* Departure Port */}
                  <FormField
                    control={form.control}
                    name='departure_port'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Departure Port</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Departure Time */}
                  <FormDateTimePicker control={form.control} name='start_time' label='Departure Time' fromDate={minDate} toDate={maxDate} />

                  {/* Distance (using cargo field) */}
                  <FormField
                    control={form.control}
                    name='distance'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Distance</FormLabel>
                        <FormControl>
                          <div className='relative'>
                            <Input
                              type='number'
                              {...field}
                              value={field.value === 0 ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : Number(e.target.value);
                                field.onChange(value);
                              }}
                              className='pr-12'
                            />
                            <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                              nm
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='space-y-4'>
                  {/* Destination Port */}
                  <FormField
                    control={form.control}
                    name='destination_port'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Destination Port</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Arrival Time */}
                  <FormDateTimePicker control={form.control} name='end_time' label='Arrival Time' fromDate={minDate} toDate={maxDate} />

                  {/* Time fields */}
                  <div>
                    <div className='grid grid-cols-3 gap-2'>
                      <FormField
                        control={form.control}
                        name='time_sailed_days'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Days</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name='time_sailed_hours'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Hours</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name='time_sailed_minutes'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minutes</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                {...field}
                                value={field.value === 0 ? '' : field.value}
                                disabled
                                className='bg-gray-50'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Voyage Information - 3 columns */}
              <div className='grid grid-cols-3 gap-4'>
                {/* Cargo */}
                <FormField
                  control={form.control}
                  name='cargo'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cargo</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            {...field}
                            value={field.value === 0 ? '' : field.value}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                              field.onChange(value);
                            }}
                            className='pr-8'
                          />
                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                            t
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Transport Work */}
                <FormField
                  control={form.control}
                  name='transport_work'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Transport Work</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            {...field}
                            value={field.value === 0 ? '' : field.value}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                              field.onChange(value);
                            }}
                            className='pr-16'
                          />
                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                            t /nm
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Voyage Type */}
                <FormField
                  control={form.control}
                  name='voyage_type'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Voyage Type</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value || 'none'}>
                          <SelectTrigger className='w-full min-w-[140px]'>
                            <SelectValue placeholder='Select voyage type' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>None</SelectItem>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>EU Voyages</SelectLabel>
                              <SelectItem value='Towards EU'>Towards EU</SelectItem>
                              <SelectItem value='Between EU'>Between EU</SelectItem>
                              <SelectItem value='Outbound EU'>Outbound EU</SelectItem>
                              <SelectItem value='at Berth EU'>at Berth EU</SelectItem>
                            </SelectGroup>
                            <SelectSeparator />
                            <SelectGroup>
                              <SelectLabel>UK Voyages</SelectLabel>
                              <SelectItem value='Towards UK'>Towards UK</SelectItem>
                              <SelectItem value='Between UK'>Between UK</SelectItem>
                              <SelectItem value='Outbound UK'>Outbound UK</SelectItem>
                              <SelectItem value='at Berth UK'>at Berth UK</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Fuel Types and Consumption */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-sm font-medium'>Fuel Consumption and Emission</h3>
                  {availableFuelTypes.length > 0 && (
                    <div className='flex items-center gap-2'>
                      <Select
                        onValueChange={(value) => {
                          setSelectedFuelType(value);
                        }}
                        value={selectedFuelType}
                      >
                        <SelectTrigger className='w-[120px]'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableFuelTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          if (selectedFuelType) {
                            addFuelType(selectedFuelType);
                            setSelectedFuelType('');
                          }
                        }}
                        disabled={!selectedFuelType}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add
                      </Button>
                    </div>
                  )}
                </div>

                {activeFuelTypes.length === 0 ? (
                  <div className='text-muted-foreground rounded-md border border-dashed p-6 text-center text-sm'>
                    No fuel types added. Click the &quot;Add&quot; button to add a fuel type.
                  </div>
                ) : (
                  <div>
                    {activeFuelTypes.length > 0 && (
                      <div>
                        {/* Fuel cards in a grid layout */}
                        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                          {activeFuelTypes.map((fuelType) => (
                            <div key={fuelType} className={`rounded-md border p-4 ${activeFuelTypes.length === 1 ? 'md:col-span-2' : ''}`}>
                              <div className='mb-3 flex items-center justify-between'>
                                <h4 className='font-medium'>{fuelType.toUpperCase()}</h4>
                                <Button type='button' variant='ghost' size='sm' onClick={() => removeFuelType(fuelType)}>
                                  <Trash2 className='h-4 w-4 text-red-500' />
                                </Button>
                              </div>

                              <div className='space-y-3'>
                                <FormField
                                  control={form.control}
                                  name={`fuel_consumption_${fuelType}` as keyof VoyageFormValues}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Consumption</FormLabel>
                                      <FormControl>
                                        <div className='relative'>
                                          <Input
                                            type='number'
                                            {...field}
                                            value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                            onChange={(e) => {
                                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                                              field.onChange(value);
                                            }}
                                            className='pr-8'
                                          />
                                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                            t
                                          </div>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={form.control}
                                  name={`emissions_${fuelType}` as keyof VoyageFormValues}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>CO2 Emission</FormLabel>
                                      <FormControl>
                                        <div className='relative'>
                                          <Input
                                            type='number'
                                            {...field}
                                            value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                            readOnly
                                            className='bg-gray-50 pr-8'
                                          />
                                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                            t
                                          </div>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Total values in a separate section below */}
                    <div className='mt-4 grid grid-cols-2 gap-4 border-t pt-4'>
                      <FormField
                        control={form.control}
                        name='fuel_consumption'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total Consumption</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='number'
                                  {...field}
                                  value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                  readOnly
                                  className='bg-gray-50 pr-8 font-medium'
                                />
                                <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                  t
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='emissions'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total CO2 Emission</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='number'
                                  {...field}
                                  value={typeof field.value === 'number' && field.value === 0 ? '' : String(field.value || '')}
                                  readOnly
                                  className='bg-gray-50 pr-8 font-medium'
                                />
                                <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                                  t
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant='outline' type='button' onClick={() => handleDialogOpenChange(false)}>
                  Cancel
                </Button>
                <Button type='submit' variant='default'>
                  Submit
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
