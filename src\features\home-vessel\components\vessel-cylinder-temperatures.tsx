'use client';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

type CylinderTemp = {
  name: string;
  temperature: number;
};

const MAX_TEMP = 600;

const cylinders: CylinderTemp[] = [
  { name: 'A1', temperature: 480 },
  { name: 'A2', temperature: 495 },
  { name: 'A3', temperature: 520 },
  { name: 'A4', temperature: 555 },
  { name: 'A5', temperature: 580 },
  { name: 'A6', temperature: 610 },
  { name: 'A7', temperature: 635 },
  { name: 'A8', temperature: 660 },
  { name: 'A9', temperature: 685 },
  { name: 'A10', temperature: 710 },
  { name: 'A11', temperature: 735 },
  { name: 'A12', temperature: 760 },
];

function getBarColor(temp: number) {
  if (temp <= 500) return 'bg-green-500';
  if (temp <= 540) return 'bg-amber-500';
  return 'bg-red-500';
}

export default function VesselCylinderTemperatures() {
  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Cylinder Temperatures</div>
      <div className='max-h-50 overflow-auto'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-[100px]'>Cylinder</TableHead>
              <TableHead>Temp (°C)</TableHead>
              <TableHead className='w-full'>Temperature Bar</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {cylinders.map((cyl) => {
              const percentage = Math.min((cyl.temperature / MAX_TEMP) * 100, 100);
              const barColor = getBarColor(cyl.temperature);
              return (
                <TableRow key={cyl.name}>
                  <TableCell>{cyl.name}</TableCell>
                  <TableCell>{cyl.temperature}°C</TableCell>
                  <TableCell>
                    <div className='bg-muted relative h-2 w-full rounded-full'>
                      <div className={`absolute top-0 left-0 h-full rounded-full ${barColor}`} style={{ width: `${percentage}%` }} />
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
