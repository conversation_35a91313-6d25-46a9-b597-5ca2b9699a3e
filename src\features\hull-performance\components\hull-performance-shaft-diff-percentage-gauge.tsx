'use client';

import React from 'react';
import GaugeChart from '@/components/ui-extensions/gauge-chart';
import { useHullPerformanceStore } from '../hull-performance.store';

export default function ShaftDiffPercentageGauge() {
  const { shaftDiffPercentage, isLoading } = useHullPerformanceStore();

  return (
    <GaugeChart
      title='Relative Shaft Power Change'
      value={shaftDiffPercentage}
      min={-100}
      max={100}
      unit='%/day'
      isPositiveGood={false}
      isLoading={isLoading}
    />
  );
}
