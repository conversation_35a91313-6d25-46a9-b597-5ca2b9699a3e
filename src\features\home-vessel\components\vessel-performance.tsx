'use client';

import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

const performanceData = [
  { logSpeed: 10, fuelTonsPerHour: 5.2, shaftPowerKw: 2800 },
  { logSpeed: 11, fuelTonsPerHour: 6.1, shaftPowerKw: 3200 },
  { logSpeed: 12, fuelTonsPerHour: 7.0, shaftPowerKw: 3700 },
  { logSpeed: 13, fuelTonsPerHour: 8.4, shaftPowerKw: 4300 },
  { logSpeed: 14, fuelTonsPerHour: 10.1, shaftPowerKw: 5000 },
  { logSpeed: 15, fuelTonsPerHour: 12.0, shaftPowerKw: 5800 },
];

const options: Highcharts.Options = {
  chart: {
    type: 'column',
  },
  title: {
    text: '',
  },
  xAxis: {
    categories: performanceData.map((d) => `${d.logSpeed} kn`),
    title: { text: 'Log Speed (knots)' },
  },
  yAxis: [
    {
      title: {
        text: 'Fuel Consumption (tons/h)',
      },
    },
    {
      title: {
        text: 'Shaft Power (kW)',
      },
      opposite: true,
    },
  ],
  tooltip: {
    shared: true,
  },
  legend: {
    align: 'center',
    verticalAlign: 'bottom',
  },
  plotOptions: {
    column: {
      grouping: true,
      shadow: false,
      borderWidth: 0,
    },
  },
  series: [
    {
      name: 'Fuel Consumption (tons/h)',
      type: 'column',
      data: performanceData.map((d) => d.fuelTonsPerHour),
      yAxis: 0,
      color: '#3b82f6', // Tailwind blue-500
    },
    {
      name: 'Shaft Power (kW)',
      type: 'column',
      data: performanceData.map((d) => d.shaftPowerKw),
      yAxis: 1,
      color: '#10b981', // Tailwind green-500
    },
  ],
  credits: {
    enabled: false,
  },
};

export default function VesselPerformance() {
  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Main Engine Fuel Consumption vs Shaft Power</div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}
