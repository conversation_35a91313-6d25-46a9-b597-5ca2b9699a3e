'use client';

import { Input } from '@/components/ui/input';
import { FormLabel } from '@/components/ui/form';
import React from 'react';

interface MrvVoyageFieldTimeProps {
  fromDate: Date;
  toDate: Date;
}

export function MrvVoyageFieldTime({ fromDate, toDate }: MrvVoyageFieldTimeProps) {
  const { days, hours, minutes } = React.useMemo(() => {
    const diffMs = toDate.getTime() - fromDate.getTime();
    if (diffMs < 0) return { days: 0, hours: 0, minutes: 0 };

    const totalMinutes = Math.floor(diffMs / 1000 / 60);
    const days = Math.floor(totalMinutes / (60 * 24));
    const hours = Math.floor((totalMinutes % (60 * 24)) / 60);
    const minutes = totalMinutes % 60;

    return { days, hours, minutes };
  }, [fromDate, toDate]);

  return (
    <div className='grid grid-cols-3 gap-2'>
      <div className='flex flex-col gap-1'>
        <FormLabel>Days</FormLabel>
        <Input type='number' value={days} disabled className='bg-gray-50' />
      </div>
      <div className='flex flex-col gap-1'>
        <FormLabel>Hours</FormLabel>
        <Input type='number' value={hours} disabled className='bg-gray-50' />
      </div>
      <div className='flex flex-col gap-1'>
        <FormLabel>Minutes</FormLabel>
        <Input type='number' value={minutes} disabled className='bg-gray-50' />
      </div>
    </div>
  );
}
