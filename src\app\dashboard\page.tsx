'use client';

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import HomeVesselContent from '@/features/home-vessel/components/home-vessel-content';
import FleetContent from '@/features/home-fleet/components/home-fleet-content';
import React from 'react';

export default function HomePage() {
  const [defaultValue, setDefaultValue] = React.useState('vessel');

  const handleTabChange = (value: string) => {
    setDefaultValue(value);
  };

  const header = defaultValue === 'vessel' ? 'Vessel Overview' : 'Fleet Overview';

  return (
    <Tabs defaultValue='vessel' onValueChange={handleTabChange}>
      <div className='mb-2 flex items-center justify-between'>
        <h1 className='pb-0 text-xl font-bold tracking-tight lg:text-2xl'>{header}</h1>
        <div className='flex items-center gap-4'>
          <TabsList>
            <TabsTrigger value='vessel'>Vessel</TabsTrigger>
            <TabsTrigger value='fleet'>Fleet</TabsTrigger>
          </TabsList>
        </div>
      </div>

      <TabsContent value='vessel'>
        <HomeVesselContent />
      </TabsContent>

      <TabsContent value='fleet'>
        <FleetContent />
      </TabsContent>
    </Tabs>
  );
}
