'use client';

import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { Button } from '@/components/ui/button';
import { FileJson } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { useState } from 'react';
import { toast } from 'sonner';
import { formatDateTime } from '../utils/date-formatter';
import { useVesselStore } from '@/features/vessels/vessel.store';

interface ExportJsonFormData {
  fromDate: Date;
  toDate: Date;
}

export default function ExportJson() {
  const [isExporting, setIsExporting] = useState(false);
  const { currentVessel } = useVesselStore();

  const form = useForm<ExportJsonFormData>({
    defaultValues: {
      fromDate: new Date(new Date().setDate(new Date().getDate() - 7)), // 7 days ago
      toDate: new Date(),
    },
  });

  const handleExport = async (data: ExportJsonFormData) => {
    if (!currentVessel) {
      toast.error('No vessel selected');
      return;
    }

    setIsExporting(true);
    try {
      // Format dates for API call
      const fromDate = formatDateTime(data.fromDate);
      const toDate = formatDateTime(data.toDate);

      // Create download URL
      const downloadUrl = `/api/download-json?vessel_imo=${currentVessel.imo}&owner_vat=${encodeURIComponent(
        currentVessel.assigned_owner_vat
      )}&from_date=${encodeURIComponent(fromDate)}&to_date=${encodeURIComponent(toDate)}&vessel_name=${encodeURIComponent(
        currentVessel.vessel_name
      )}`;

      // Fetch the JSON file
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error('Failed to download JSON data');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Format dates for filename
      const fromDateFormatted = data.fromDate.toISOString().split('T')[0];
      const toDateFormatted = data.toDate.toISOString().split('T')[0];

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `Json-Data-${currentVessel.vessel_name || 'vessel'}-${fromDateFormatted}-to-${toDateFormatted}.json`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('JSON export completed successfully');
    } catch (error) {
      console.error('Error exporting JSON:', error);
      toast.error('Failed to export JSON data');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-2 flex items-center gap-2'>
        <div className='text-lg font-semibold tracking-tight'>JSON</div>
        <FileJson className='h-4 w-4' />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleExport)} className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormDateTimePicker control={form.control} name='fromDate' label='From' />
            <FormDateTimePicker control={form.control} name='toDate' label='To' />
          </div>
          <div className='flex justify-end'>
            <Button type='submit' disabled={isExporting}>
              {isExporting ? <>Exporting...</> : <>Export</>}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
