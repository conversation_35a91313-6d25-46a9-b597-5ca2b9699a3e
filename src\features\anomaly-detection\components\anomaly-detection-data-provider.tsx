'use client';

import React from 'react';
import { useAnomalyDetectionStore } from '../anomaly-detection.store';
import { useVesselStore } from '../../vessels/vessel.store';

interface AnomalyDetectionDataProviderProps {
  children: React.ReactNode;
}

export function AnomalyDetectionDataProvider({ children }: AnomalyDetectionDataProviderProps) {
  const { fetchAnomalyDetectionData } = useAnomalyDetectionStore();
  const { currentVessel } = useVesselStore();

  React.useEffect(() => {
    if (currentVessel?.imo) {
      fetchAnomalyDetectionData(currentVessel.assigned_owner_vat, currentVessel.imo.toString());
    }
  }, [currentVessel?.imo, fetchAnomalyDetectionData]);

  return <>{children}</>;
}
