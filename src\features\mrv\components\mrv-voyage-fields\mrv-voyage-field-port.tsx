'use client';

import { Input } from '@/components/ui/input';
import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import { Control, FieldValues, Path } from 'react-hook-form';

interface MrvVoyageFieldPortProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
}

export function MrvVoyageFieldPort<T extends FieldValues>({ control, name, label }: MrvVoyageFieldPortProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      rules={{ required: 'This field is required' }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              placeholder={label}
              {...field}
              value={field.value?.toString().toUpperCase() ?? ''}
              onChange={(e) => field.onChange(e.target.value.toUpperCase())}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
