'use client';

import React from 'react';
import GaugeChart from '@/components/ui-extensions/gauge-chart';
import { useHullPerformanceStore } from '../hull-performance.store';

export default function FuelConsumptionGauge() {
  const { overConsumptionFuel, isLoading } = useHullPerformanceStore();

  return (
    <GaugeChart
      title='Fuel Over Consumption'
      value={overConsumptionFuel}
      min={-100}
      max={100}
      unit='%'
      isPositiveGood={false}
      isLoading={isLoading}
    />
  );
}
