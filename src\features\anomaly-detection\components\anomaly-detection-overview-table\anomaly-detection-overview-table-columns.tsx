'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { getSensorLabel } from '../../anomaly-detection.store';

export type Sensor = {
  key: string;
  name: string;
  time_range: string;
  overall_min: number;
  overall_max: number;
  overall_mean: number;
  overall_outlier_count: number;
};

// Helper function to format sensor names using helper from store
const formatSensorName = (sensorName: string): string => {
  return getSensorLabel(sensorName);
};

// Helper function to format numbers in compact notation for very large values
const formatCompactNumber = (value: number, useDecimals: boolean = true): string => {
  if (!Number.isFinite(value)) return 'N/A';

  const absValue = Math.abs(value);

  // Use scientific notation for numbers >= 1 million
  if (absValue >= 1_000_000) {
    return value.toExponential(2);
  }

  // Use regular formatting for smaller numbers
  if (useDecimals) {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } else {
    return value.toLocaleString('en-US');
  }
};

// Helper function to format numbers to 2 decimal places with comma separators
const formatNumber = (value: number): string => {
  return formatCompactNumber(value, true);
};

// Helper function to format integer numbers with comma separators (no decimals)
const formatInteger = (value: number): string => {
  return formatCompactNumber(value, false);
};

// Helper function to format time range
const formatTimeRange = (timeRange: string): string => {
  try {
    // Split the time range string (format: "fromDate - toDate")
    const [fromStr, toStr] = timeRange.split(' - ');
    if (!fromStr || !toStr) return timeRange;

    const fromDate = new Date(fromStr);
    const toDate = new Date(toStr);

    // Format dates as "2024 Jan 1 12:21:05"
    const formattedFrom = format(fromDate, 'yyyy MMM d HH:mm:ss');
    const formattedTo = format(toDate, 'yyyy MMM d HH:mm:ss');

    return `${formattedFrom} - ${formattedTo}`;
  } catch (_) {
    // If parsing fails, return the original string
    return timeRange;
  }
};

export const columns: ColumnDef<Sensor>[] = [
  {
    accessorKey: 'name',
    header: 'Sensor Name',
    cell: ({ row }) => formatSensorName(row.getValue('name')),
  },
  {
    accessorKey: 'time_range',
    header: 'Time Range',
    cell: ({ row }) => formatTimeRange(row.getValue('time_range')),
  },
  {
    accessorKey: 'overall_min',
    header: 'Lowest Value',
    cell: ({ row }) => formatNumber(row.getValue('overall_min')),
  },
  {
    accessorKey: 'overall_max',
    header: 'Highest Value',
    cell: ({ row }) => formatNumber(row.getValue('overall_max')),
  },
  {
    accessorKey: 'overall_mean',
    header: 'Average Value',
    cell: ({ row }) => formatNumber(row.getValue('overall_mean')),
  },
  {
    accessorKey: 'overall_outlier_count',
    header: 'Outlier Count',
    cell: ({ row }) => formatInteger(row.getValue('overall_outlier_count')),
  },
];
