'use client';

import React, { useEffect, useState } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui-extensions/date-range-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select';
import { useVesselStore } from '@/features/vessels/vessel.store';

import { DateRange } from 'react-day-picker';
import { endOfDay, formatToServerDateTime, startOfDay } from '@/lib/date-utils';
import { FleetEfficiencyPostDTO } from '../../home-fleet-efficiency.types';
import { useHomeFleetStore } from '../../home-fleet.store';
import { MultiSelectPopover } from '@/components/ui-extensions/multi-select-popover';

const getDefaultDateRange = (): DateRange => {
  const to = new Date();
  const from = new Date(to);
  from.setDate(to.getDate() - 7);
  return { from, to };
};

export default function HomeFleetChartEfficiencyFilter() {
  const [fpOn, setFpOn] = useState(true);
  const [fpOff, setFpOff] = useState(true);
  const [loadCondition, setLoadCondition] = useState('All');
  const [beaufort, setBeaufort] = useState('4');
  const [dateRange, setDateRange] = useState<DateRange>(getDefaultDateRange());
  const [vessels, setVessels] = useState<{ id: number; imo: number; name: string; assigned_owner_vat: string }[]>([]);

  const { fetchFleetEfficiency } = useHomeFleetStore();
  const { currentVessel, vessels: allVessels } = useVesselStore();

  const handleSearch = () => {
    if (!currentVessel) return;

    const requestBody: FleetEfficiencyPostDTO = {
      request: {
        vessel_id: currentVessel.id,
        vessel_imo: currentVessel.imo,
        vessel_name: currentVessel.vessel_name,
        selected_owner_vat: currentVessel.assigned_owner_vat,
        from_date: formatToServerDateTime(startOfDay(dateRange.from)),
        to_date: formatToServerDateTime(endOfDay(dateRange.to)),
      },
      filters: {
        type: 'fleet',
        vessels,
        fpOn,
        fpOff,
        loadCondition,
        beaufort: Number(beaufort),
      },
      owner_vat: currentVessel.assigned_owner_vat,
    };

    fetchFleetEfficiency(requestBody);
  };

  // Select all vessels as default once
  useEffect(() => {
    if (allVessels.length > 0 && vessels.length === 0) {
      setVessels(
        allVessels.map((v) => ({
          id: v.id,
          imo: v.imo,
          name: v.vessel_name,
          assigned_owner_vat: v.assigned_owner_vat,
        }))
      );
    }
  }, [allVessels, vessels.length]);

  // Run search only once vessels are initialized
  useEffect(() => {
    if (allVessels.length > 0 && vessels.length > 0) {
      handleSearch();
    }
  }, [allVessels, vessels]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline'>Filters</Button>
      </PopoverTrigger>
      <PopoverContent className='mr-2 w-69'>
        <div className='mb-2 text-sm font-medium'>Filter Options</div>
        <div className='space-y-4'>
          {/* Date Range */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Date Range</Label>
            <DateRangePicker value={dateRange} onChange={(value) => setDateRange(value ?? getDefaultDateRange())} />
          </div>

          {/* Load Condition */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Load Condition</Label>
            <Select value={loadCondition} onValueChange={setLoadCondition}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select...' />
              </SelectTrigger>
              <SelectContent>
                {['All', 'Laden', 'Partially Laden', 'Ballast'].map((val) => (
                  <SelectItem key={val} value={val}>
                    {val}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Frugal Propulsion */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Frugal Propulsion On / Off</Label>
            <div className='flex gap-4'>
              {[
                { id: 'fp-on', label: 'FP On', checked: fpOn, onChange: setFpOn },
                { id: 'fp-off', label: 'FP Off', checked: fpOff, onChange: setFpOff },
              ].map(({ id, label, checked, onChange }) => (
                <div key={id} className='flex items-center space-x-2'>
                  <Checkbox id={id} checked={checked} onCheckedChange={(v) => onChange(!!v)} />
                  <Label htmlFor={id} className='text-xs'>
                    {label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Beaufort */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Beaufort</Label>
            <Select value={beaufort} onValueChange={setBeaufort}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select...' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All</SelectItem>
                {Array.from({ length: 13 }, (_, i) => (
                  <SelectItem key={i} value={i.toString()}>
                    {i}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Vessels */}
          <div>
            <Label className='mb-1 block text-xs font-medium'>Vessels</Label>
            <MultiSelectPopover
              title='Vessels'
              options={allVessels.map((v) => ({
                label: v.vessel_name,
                value: v.id.toString(),
              }))}
              selectedValues={vessels.map((v) => v.id.toString())}
              setSelectedValues={(ids) => {
                setVessels(
                  allVessels
                    .filter((v) => ids.includes(v.id.toString()))
                    .map((v) => ({
                      id: v.id,
                      imo: v.imo,
                      name: v.vessel_name,
                      assigned_owner_vat: v.assigned_owner_vat,
                    }))
                );
              }}
              placeholder='Select vessels'
              className='w-full'
            />
          </div>
        </div>

        <div className='mt-4 flex justify-end'>
          <Button size='sm' onClick={handleSearch}>
            Search
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
