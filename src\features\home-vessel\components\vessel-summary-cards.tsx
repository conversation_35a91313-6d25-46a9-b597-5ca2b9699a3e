import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { FuelIcon, GaugeIcon, PowerIcon, WifiIcon } from 'lucide-react';

export default function VesselSummaryCards() {
  return (
    <div className='mb-4 grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
      {/* Frugal Propulsion Usage */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex flex-row items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Frugal Propulsion Usage</div>
          <Tooltip>
            <TooltipTrigger>
              <PowerIcon className='h-4 w-4 text-green-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Frugal Propulsion is currently in use</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>97%</div>
          <p className='text-xs text-green-500'>+20.1% from last month</p>
        </div>
      </div>

      {/* GPS Speed */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex flex-row items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>GPS Speed</div>
          <Tooltip>
            <TooltipTrigger>
              <GaugeIcon className='h-4 w-4 text-green-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Vessel is currently moving.</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>12.5 knots</div>
          <p className='text-muted-foreground text-xs'>ETA 12:53:04</p>
        </div>
      </div>

      {/* Connection Status */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex flex-row items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Connection Status</div>
          <Tooltip>
            <TooltipTrigger>
              <WifiIcon className='h-4 w-4 text-red-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Currently not connected to the vessel.</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>No Signal</div>
          <p className='text-muted-foreground text-xs'>Last updated 12:00:00</p>
        </div>
      </div>

      {/* Fuel Consumption */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex flex-row items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Fuel Consumption</div>
          <Tooltip>
            <TooltipTrigger>
              <FuelIcon className='text-muted-foreground h-4 w-4' />
            </TooltipTrigger>
            <TooltipContent>
              <p></p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>320 L/hour</div>
          <p className='text-xs text-red-500'>+5% from last trip</p>
        </div>
      </div>
    </div>
  );
}
