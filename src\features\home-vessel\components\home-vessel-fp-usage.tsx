'use client';

import { Progress } from '@/components/ui/progress';
import { useHomeVesselStore } from '../home-vessel.store';

export default function HomeVesselFpUsage() {
  const { vesselData, isLoadingVesselData } = useHomeVesselStore();

  const chartData = vesselData?.chartData;

  const onPercentage = chartData?.on_percentage ?? 0;
  const onHours = chartData?.on_hours as [number, number] | undefined;
  const offHours = chartData?.off_hours as [number, number] | undefined;
  const totalHours = chartData?.total_hours as [number, number] | undefined;

  const formatHours = (hours?: number[]) => (hours && hours.length === 2 ? `${hours[0]} hours, ${hours[1]} minutes - ` : 'N/A');

  return (
    <div className='bg-card text-card-foreground h-[156px] space-y-4 rounded-xl border p-4'>
      <div className='flex items-center justify-between'>
        <h2 className='text-md font-semibold tracking-tight'>Frugal Propulsion Usage</h2>
        <span className='text-muted-foreground text-sm'>{onPercentage.toFixed(0)}%</span>
      </div>

      <Progress value={onPercentage} className='h-3' />

      {isLoadingVesselData ? (
        <p className='text-muted-foreground text-xs'>Loading data...</p>
      ) : (
        <div className='text-muted-foreground space-y-1 text-xs'>
          <p>
            <span className='font-medium'>{formatHours(onHours)}</span> ON
          </p>
          <p>
            <span className='font-medium'>{formatHours(offHours)}</span> OFF
          </p>
          <p>
            <span className='font-medium'>{formatHours(totalHours)}</span> TOTAL
          </p>
        </div>
      )}
    </div>
  );
}
