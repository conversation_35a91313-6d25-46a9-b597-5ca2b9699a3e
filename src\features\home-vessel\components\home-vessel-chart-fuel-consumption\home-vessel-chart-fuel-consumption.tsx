import React from 'react';
import { useHomeVesselStore } from '../../home-vessel.store';
import { useChartDataDaily, useChartDataMonthly } from './home-vessel-chart-fuel-consumption.data';
import { getChartOptionsDaily, getChartOptionsMonthly } from './home-vessel-chart-fuel-consumption.option';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import { SelectContent, SelectItem, SelectLabel } from '@/components/ui/select';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import { SelectGroup } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectTrigger, SelectValue } from '@/components/ui/select';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { SeriesDaily, SeriesMonthly } from './home-vessel-chart-fuel-consumption.types';
import HomeVesselChartFuelConsumptionMonth from '../home-vessel-chart-fuel-consumption-month/home-vessel-chart-fuel-consumption-month';
import { MonthlyFuelReport } from '../../home-vessel.types';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';

export default function HomeVesselChartFuelConsumption() {
  const [type, setType] = React.useState<'main' | 'aux' | 'boiler'>('main');
  const [mode, setMode] = React.useState<'daily' | 'monthly'>('daily');
  const [selectedMonth, setSelectedMonth] = React.useState<string>('');

  const { vesselData, isLoadingVesselData } = useHomeVesselStore();
  const chartData =
    mode === 'daily' ? useChartDataDaily(vesselData?.reports ?? null, type) : useChartDataMonthly(vesselData?.reports ?? null, type);
  const isEmpty = !isLoadingVesselData && !chartData;

  const options = chartData
    ? mode === 'daily'
      ? getChartOptionsDaily(chartData as SeriesDaily)
      : getChartOptionsMonthly(chartData as SeriesMonthly, setSelectedMonth)
    : undefined;

  const speedType =
    vesselData?.reports?.efficiencies_daily?.speed_type === 'log_speed'
      ? 'Log Speed'
      : vesselData?.reports?.efficiencies_daily?.speed_type === 'gps_speed'
        ? 'GPS Speed'
        : 'Unknown';

  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='relative text-lg font-semibold tracking-tight'>
          {selectedMonth ? `Fuel Consumption - ${selectedMonth}` : `Fuel Consumption - ${mode === 'daily' ? 'Daily' : 'Monthly'}`}
          {selectedMonth && (
            <Button
              variant='link'
              onClick={() => setSelectedMonth('')}
              className='text-muted-foreground absolute top-8 left-[-12px] z-10 flex items-center gap-1 text-sm'
            >
              <ChevronLeft className='h-4 w-4' />
              Go Back
            </Button>
          )}
        </div>
        <div className='flex items-center gap-2'>
          <Tabs value={mode} onValueChange={(value) => setMode(value as 'daily' | 'monthly')}>
            <TabsList className='flex'>
              <TabsTrigger value='daily' disabled={isLoadingVesselData || selectedMonth !== ''}>
                Daily
              </TabsTrigger>
              <TabsTrigger value='monthly' disabled={isLoadingVesselData || selectedMonth !== ''}>
                Monthly
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Select
            value={type}
            onValueChange={(value) => setType(value as 'main' | 'aux' | 'boiler')}
            disabled={isLoadingVesselData || selectedMonth !== ''}
          >
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Select Engine Type' />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Fuel Consumption</SelectLabel>
                <SelectItem value='main'>Main Engine</SelectItem>
                <SelectItem value='aux'>AUX</SelectItem>
                <SelectItem value='boiler'>Boiler</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoadingVesselData ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        <>
          {selectedMonth ? (
            <HomeVesselChartFuelConsumptionMonth
              data={vesselData?.reports?.monthly_fuel_report[selectedMonth] ?? ({} as MonthlyFuelReport)}
              month={selectedMonth}
              type={type}
              speedType={speedType}
            />
          ) : (
            <HighchartsReact key={JSON.stringify(options)} highcharts={Highcharts} options={options} />
          )}
        </>
      )}
    </div>
  );
}
