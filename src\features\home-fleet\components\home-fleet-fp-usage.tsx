'use client';

import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { useState, useMemo } from 'react';
import { useHomeFleetStore } from '../home-fleet.store';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';

type VesselPropulsionData = {
  name: string;
  propulsionPercent: number;
  hoursOn: [number, number]; // [hours, minutes]
  hoursOff: [number, number]; // [hours, minutes]
  hoursTotal: [number, number]; // [hours, minutes]
};

// Helper function to format [hours, minutes] array into "X hours, Y minutes"
const formatHoursAndMinutes = (hoursArray: [number, number]): string => {
  const [hours, minutes] = hoursArray;
  return `${hours} hours, ${minutes} minutes`;
};

export default function HomeFleetFpUsage() {
  const [search, setSearch] = useState('');
  const { fleetData, isLoadingFleetData } = useHomeFleetStore();

  const vesselData = useMemo(() => {
    if (!fleetData?.vessels_fp_usage) return [];

    return fleetData.vessels_fp_usage.map((vessel): VesselPropulsionData => {
      // Get the latest values from the arrays - first element is hours, second is minutes
      // Ensure we always have exactly 2 elements [hours, minutes]
      const getHoursMinutes = (arr: number[]): [number, number] => {
        if (arr.length >= 2) {
          const last2 = arr.slice(-2);
          return [last2[0] || 0, last2[1] || 0];
        }
        return [0, 0];
      };

      const hoursOnArray = getHoursMinutes(vessel.hours_on);
      const hoursOffArray = getHoursMinutes(vessel.hours_off);
      const hoursTotalArray = getHoursMinutes(vessel.hours_total);

      // Calculate propulsion percentage as 100 - percent_off
      const propulsionPercent = Math.max(0, Math.min(100, 100 - vessel.percent_off));

      return {
        name: vessel.vessel,
        propulsionPercent,
        hoursOn: hoursOnArray,
        hoursOff: hoursOffArray,
        hoursTotal: hoursTotalArray,
      };
    });
  }, [fleetData?.vessels_fp_usage]);

  const filtered = vesselData
    .filter((v) => {
      return v.name.toLowerCase().includes(search.toLowerCase());
    })
    .sort((a, b) => a.propulsionPercent - b.propulsionPercent);

  if (isLoadingFleetData) {
    return (
      <div className='bg-card h-full rounded-xl border p-4'>
        <div className='flex h-full w-full items-center justify-center'>
          <CircleSpinner variant='primary' />
        </div>
      </div>
    );
  }

  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <div className='text-lg font-semibold tracking-tight'>Frugal Propulsion Usage</div>
          <div className='text-muted-foreground text-sm'>Last 30 days (Engine Underway)</div>
        </div>
        <Input placeholder='Search vessel...' value={search} onChange={(e) => setSearch(e.target.value)} className='w-full sm:w-64' />
      </div>
      <div className='relative max-h-97 overflow-auto'>
        <div className='relative w-full'>
          <table className='relative w-full caption-bottom text-sm'>
            <thead className='[&_tr]:border-b'>
              <tr className='hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors'>
                <th className='bg-background text-foreground sticky top-0 z-10 h-10 border-b px-2 text-left align-middle font-medium whitespace-nowrap'>
                  Vessel
                </th>
                <th className='bg-background text-foreground sticky top-0 z-10 h-10 w-64 border-b px-2 text-left align-middle font-medium whitespace-nowrap'>
                  Usage
                </th>
                <th className='bg-background text-foreground sticky top-0 z-10 h-10 border-b px-2 text-left align-middle font-medium whitespace-nowrap'>
                  Hours On
                </th>
                <th className='bg-background text-foreground sticky top-0 z-10 h-10 border-b px-2 text-left align-middle font-medium whitespace-nowrap'>
                  Hours Off
                </th>
                <th className='bg-background text-foreground sticky top-0 z-10 h-10 border-b px-2 text-left align-middle font-medium whitespace-nowrap'>
                  Total Hours
                </th>
              </tr>
            </thead>
            <tbody className='[&_tr:last-child]:border-0'>
              {filtered.length === 0 ? (
                <tr className='hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors'>
                  <td colSpan={5} className='p-2 py-8 text-center align-middle whitespace-nowrap'>
                    <p className='text-muted-foreground text-sm'>
                      {vesselData.length === 0 ? 'No vessel data available' : 'No vessels match your search'}
                    </p>
                  </td>
                </tr>
              ) : (
                filtered.map((vessel) => {
                  return (
                    <tr key={vessel.name} className='hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors'>
                      <td className='p-2 align-middle whitespace-nowrap'>{vessel.name}</td>
                      <td className='w-32 p-2 align-middle whitespace-nowrap'>
                        <div className='flex flex-col gap-1'>
                          <Progress value={vessel.propulsionPercent} className='h-2' />
                          <span className='text-muted-foreground text-xs'>
                            {vessel.propulsionPercent % 1 === 0 ? vessel.propulsionPercent.toFixed(0) : vessel.propulsionPercent.toFixed(1)}
                            %
                          </span>
                        </div>
                      </td>
                      <td className='p-2 align-middle whitespace-nowrap'>{formatHoursAndMinutes(vessel.hoursOn)}</td>
                      <td className='p-2 align-middle whitespace-nowrap'>{formatHoursAndMinutes(vessel.hoursOff)}</td>
                      <td className='p-2 align-middle whitespace-nowrap'>{formatHoursAndMinutes(vessel.hoursTotal)}</td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
