import { create } from 'zustand';
import { toast } from 'sonner';
import axios from 'axios';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { useMrvTableStore } from './components/mrv-table/mrv-table.store';
import { MrvVoyage, MrvVoyagePutDto, MrvVoyagePostDto, MrvGetDto } from './mrv.types';

interface MrvStore {
  voyages: Record<string, MrvVoyage>;
  isLoading: boolean;

  clearVoyages: () => void;
  loadVoyages: () => Promise<void>;
  createVoyage: (voyageData: MrvVoyagePostDto) => Promise<void>;
  updateVoyage: (id: string, updatedData: MrvVoyagePutDto) => Promise<void>;
  deleteVoyage: (id: string) => Promise<void>;
}

export const useMrvStore = create<MrvStore>((set, get) => ({
  voyages: {},
  isLoading: false,

  clearVoyages: () => {
    set({ voyages: {} });
  },

  loadVoyages: async () => {
    // Reset state
    set({ isLoading: true });

    // Vessel and year check
    const currentVessel = useVesselStore.getState().currentVessel;
    const year = useMrvTableStore.getState().year;
    if (!currentVessel || !year) return;

    try {
      // Build url with query params
      const url = new URL('/api/mrv', window.location.origin);
      url.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        year: year.toString(),
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      // Fetch voyages
      const { data } = await axios.get<MrvGetDto>(url.toString());

      // Set state
      set({ voyages: data.mrv_data_copy });
    } catch {
      // Error toast
      toast.error('Error loading voyages');
    } finally {
      // Reset loading state
      set({ isLoading: false });
    }
  },

  createVoyage: async (voyageData) => {
    // Set loading state
    set({ isLoading: true });

    // Vessel and year check
    const currentVessel = useVesselStore.getState().currentVessel;
    const year = useMrvTableStore.getState().year;
    if (!currentVessel || !year) return;

    try {
      // Build url with query params and body
      const url = new URL('/api/mrv/voyage', window.location.origin);

      url.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        year: year.toString(),
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      const body = { voyage_data: voyageData };

      // Create voyage
      const { data } = await axios.post<{ voyage: MrvVoyage }>(url.toString(), body);

      // Set state
      set((state) => ({ voyages: { ...state.voyages, [data.voyage.voyage_id]: data.voyage } }));

      // Success toast
      toast.success('Voyage created successfully');

      // Reload voyages
      get().loadVoyages();
    } catch {
      // Error toast
      toast.error('Error creating voyage');
      set({ isLoading: false });
    }
  },

  updateVoyage: async (id: string, updatedData: MrvVoyagePutDto) => {
    // Set loading state
    set({ isLoading: true });

    // Vessel and year check
    const currentVessel = useVesselStore.getState().currentVessel;
    const year = useMrvTableStore.getState().year;
    const voyage = get().voyages[id];
    if (!currentVessel || !year || !voyage) return;

    try {
      // Build url with query params and body
      const url = new URL('/api/mrv/voyage', window.location.origin);

      url.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        year: year.toString(),
        voyage_id: id,
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      const body = { voyage_data: updatedData };

      // Update voyage
      const { data } = await axios.put<{ voyage: MrvVoyage }>(url.toString(), body);

      // Set state
      set((state) => ({ voyages: { ...state.voyages, [data.voyage.voyage_id]: data.voyage } }));

      // Success toast
      toast.success('Voyage updated');

      // Reload voyages
      get().loadVoyages();
    } catch {
      // Error toast
      toast.error('Error updating voyage');
      set({ isLoading: false });
    }
  },

  deleteVoyage: async (key: string) => {
    // Set loading state
    set({ isLoading: true });

    // Vessel and year check
    const currentVessel = useVesselStore.getState().currentVessel;
    const year = useMrvTableStore.getState().year;
    const voyage = get().voyages[key];
    if (!currentVessel || !year || !voyage) return;

    try {
      // Build url with query params
      const url = new URL('/api/mrv/voyage', window.location.origin);

      url.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        year: year.toString(),
        voyage_id: voyage.voyage_id,
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      // Delete voyage
      await axios.delete(url.toString());

      // Set state
      set((state) => {
        const { [voyage.voyage_id]: _, ...rest } = state.voyages;
        return { voyages: rest };
      });

      // Success toast
      toast.success('Voyage deleted successfully');

      // Reload voyages
      get().loadVoyages();
    } catch {
      // Error toast
      toast.error('Failed to delete voyage');
      set({ isLoading: false });
    }
  },
}));
