'use client';

import { useState } from 'react';
import { PageHeader } from '@/components/layout/page-header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MainEngineHealthResponse } from '@/features/main-engine-health/main-engine-health.types';

export default function TestMainEngineHealthPage() {
  const [vesselImo, setVesselImo] = useState('');
  const [ownerVat, setOwnerVat] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<MainEngineHealthResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTest = async () => {
    if (!vesselImo.trim() || !ownerVat.trim()) {
      setError('Both vessel_imo and owner_vat are required');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const params = new URLSearchParams({
        vessel_imo: vesselImo.trim(),
        owner_vat: ownerVat.trim(),
      });

      const res = await fetch(`/api/main-engine-health?${params.toString()}`);
      
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();
      setResponse(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex flex-col gap-4'>
      <PageHeader title='Test Main Engine Health Route' />
      
      <Card>
        <CardHeader>
          <CardTitle>API Route Tester</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='vessel_imo'>Vessel IMO</Label>
              <Input
                id='vessel_imo'
                value={vesselImo}
                onChange={(e) => setVesselImo(e.target.value)}
                placeholder='Enter vessel IMO'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='owner_vat'>Owner VAT</Label>
              <Input
                id='owner_vat'
                value={ownerVat}
                onChange={(e) => setOwnerVat(e.target.value)}
                placeholder='Enter owner VAT'
              />
            </div>
          </div>
          
          <Button 
            onClick={handleTest} 
            disabled={loading}
            className='w-full md:w-auto'
          >
            {loading ? 'Testing...' : 'Test Route'}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Card>
          <CardHeader>
            <CardTitle className='text-red-600'>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className='text-sm text-red-600 whitespace-pre-wrap'>{error}</pre>
          </CardContent>
        </Card>
      )}

      {response && (
        <Card>
          <CardHeader>
            <CardTitle className='text-green-600'>Success Response</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm'>
                <div>
                  <strong>Has Alarm Data:</strong> {response.hasAlarmData ? 'Yes' : 'No'}
                </div>
                <div>
                  <strong>Not Enough Data:</strong> {response.notEnoughData ? 'Yes' : 'No'}
                </div>
                <div>
                  <strong>Days Diff:</strong> {response.daysDiff}
                </div>
                <div>
                  <strong>Health Score:</strong> {response.mainEngineHealthScore}
                </div>
                <div>
                  <strong>Health Score Min:</strong> {response.mainEngineHealthScoreMin}
                </div>
                <div>
                  <strong>Health Score Max:</strong> {response.mainEngineHealthScoreMax}
                </div>
                <div>
                  <strong>Consistency:</strong> {response.mainEngineConsistency}
                </div>
                <div>
                  <strong>Volatility:</strong> {response.mainEngineHealthVolatility}
                </div>
                <div>
                  <strong>Start Time:</strong> {response.startTime}
                </div>
                <div>
                  <strong>End Time:</strong> {response.endTime}
                </div>
              </div>
              
              <details className='mt-4'>
                <summary className='cursor-pointer font-semibold mb-2'>Full Response (JSON)</summary>
                <pre className='text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96'>
                  {JSON.stringify(response, null, 2)}
                </pre>
              </details>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
