'use client';

import { useState, useMemo } from 'react';
import { Control, useWatch, useFormContext, FieldValues, Path, PathValue } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Trash2, Plus } from 'lucide-react';

const fuelCoefficients = {
  MDO: 3.206,
  LFO: 3.151,
  HFO: 3.114,
};

type FuelType = keyof typeof fuelCoefficients;

interface MrvVoyageFieldFuelProps<T extends FieldValues> {
  control: Control<T>;
}

export function MrvVoyageFieldFuel<T extends FieldValues>({ control }: MrvVoyageFieldFuelProps<T>) {
  const { setValue, getValues } = useFormContext<T>();

  // Initialize activeFuelTypes from form values on mount
  const [activeFuelTypes, setActiveFuelTypes] = useState<FuelType[]>(() => {
    const values = getValues();
    return (Object.keys(fuelCoefficients) as FuelType[]).filter((fuel) => {
      const val = values[`fuel_consumption_${fuel}` as Path<T>];
      return val !== undefined && val !== null && Number(val) > 0;
    });
  });

  // State for the selected fuel type
  const [selectedFuel, setSelectedFuel] = useState<FuelType | ''>('');

  // Get the values from the form
  const values = useWatch({ control });

  // Calculate the available fuel types
  const availableFuelTypes = useMemo(
    () => (Object.keys(fuelCoefficients) as FuelType[]).filter((ft) => !activeFuelTypes.includes(ft)),
    [activeFuelTypes]
  );

  // Calculate the emissions per type
  const emissionsPerType = useMemo(() => {
    const emissions: Record<FuelType, number> = {} as Record<FuelType, number>;
    activeFuelTypes.forEach((type) => {
      const key = `fuel_consumption_${type}` as Path<T>;
      const consumption = Number(values?.[key] ?? 0);
      emissions[type] = consumption * fuelCoefficients[type];
    });
    return emissions;
  }, [activeFuelTypes, values]);

  // Calculate the total consumption
  const totalConsumption = useMemo(() => {
    return activeFuelTypes.reduce((sum, fuel) => {
      return sum + Number(values?.[`fuel_consumption_${fuel}` as Path<T>] ?? 0);
    }, 0);
  }, [activeFuelTypes, values]);

  // Calculate the total emissions
  const totalEmissions = useMemo(() => {
    return activeFuelTypes.reduce((sum, fuel) => sum + emissionsPerType[fuel], 0);
  }, [activeFuelTypes, emissionsPerType]);

  // Add a fuel type action
  const addFuel = () => {
    if (!selectedFuel) return;
    setActiveFuelTypes((prev) => [...prev, selectedFuel]);
    setSelectedFuel('');
    setValue(`fuel_consumption_${selectedFuel}` as Path<T>, 0 as PathValue<T, Path<T>>);
  };

  // Remove a fuel type action
  const removeFuel = (fuel: FuelType) => {
    setActiveFuelTypes((prev) => prev.filter((f) => f !== fuel));
    setValue(`fuel_consumption_${fuel}` as Path<T>, undefined as PathValue<T, Path<T>>);
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <h3 className='text-sm font-medium'>Fuel Consumption</h3>
        {availableFuelTypes.length > 0 && (
          <div className='flex items-center gap-2'>
            <Select value={selectedFuel} onValueChange={(val) => setSelectedFuel(val as FuelType)}>
              <SelectTrigger className='w-[120px]'>
                <SelectValue placeholder='Select fuel' />
              </SelectTrigger>
              <SelectContent>
                {availableFuelTypes.map((fuel) => (
                  <SelectItem key={fuel} value={fuel}>
                    {fuel.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button type='button' variant='outline' size='sm' onClick={addFuel} disabled={!selectedFuel}>
              <Plus className='mr-1 h-4 w-4' />
              Add
            </Button>
          </div>
        )}
      </div>

      {activeFuelTypes.length === 0 ? (
        <div className='text-muted-foreground rounded-md border border-dashed p-4 text-center text-sm'>
          No fuel types added. Select a fuel and click &quot;Add&quot;.
        </div>
      ) : (
        <div className='grid grid-cols-1 gap-4'>
          {activeFuelTypes.map((fuel) => (
            <div key={fuel} className='rounded-md border p-4'>
              <div className='mb-2 flex items-center justify-between'>
                <h4 className='font-medium'>{fuel.toUpperCase()}</h4>
                <Button variant='ghost' size='sm' onClick={() => removeFuel(fuel)}>
                  <Trash2 className='h-4 w-4 text-red-500' />
                </Button>
              </div>
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={control}
                  name={`fuel_consumption_${fuel}` as Path<T>}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Consumption</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            step='0.01'
                            {...field}
                            value={field.value === 0 ? '' : (field.value ?? '')}
                            onChange={(e) => {
                              const value = e.target.value === '' ? 0 : Number(e.target.value);
                              field.onChange(value);
                            }}
                          />
                          <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>
                            t
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className='grid grid-cols-1 gap-2'>
                  <FormLabel>Emissions</FormLabel>
                  <div className='relative'>
                    <Input readOnly value={emissionsPerType[fuel]?.toFixed(3) ?? '0.000'} className='bg-gray-50' />
                    <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>t</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeFuelTypes.length > 0 && (
        <div className='grid grid-cols-2 gap-4 border-t pt-4'>
          <div className='flex flex-col gap-1'>
            <FormLabel>Total Consumption</FormLabel>
            <div className='relative'>
              <Input readOnly value={totalConsumption.toFixed(3)} className='bg-gray-50 font-medium' />
              <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>t</div>
            </div>
          </div>
          <div className='flex flex-col gap-1'>
            <FormLabel>Total Emissions</FormLabel>
            <div className='relative'>
              <Input readOnly value={totalEmissions.toFixed(3)} className='bg-gray-50 font-medium' />
              <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>t</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
