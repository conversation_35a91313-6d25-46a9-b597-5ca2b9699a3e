import { UserGetDTO, UserRole } from '@/features/users/user.types';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === POST BODY SCHEMA ===
const postBodySchema = z.object({
  email: z.string().nonempty('Email is required').email('Invalid email format'),
  role: z.nativeEnum(UserRole, { errorMap: () => ({ message: 'Invalid role' }) }),
});

// === GET ENDPOINT ===
export async function GET(request: NextRequest) {
  // Build URL
  const url = new URL('/users', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.get<UserGetDTO[]>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}

// === POST ENDPOINT ===
export async function POST(request: NextRequest) {
  // Validate body
  const validatedBody = postBodySchema.safeParse(await request.json());
  if (!validatedBody.success) {
    return NextResponse.json({ errors: validatedBody.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/users', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.post<UserGetDTO>(request, url.toString(), validatedBody.data);
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
