'use client';

import { DataAiObj } from '../../hull-performance.types';
import { parseUTCTimestamp } from './hull-performance-ai-chart-utils';

export interface ProcessedChartData {
  realData: [number, number | null][];
  predictedRangeData: [number, number | null, number | null][];
  plotLines: Highcharts.AxisPlotLinesOptions[];
  plotBands: Highcharts.AxisPlotBandsOptions[];
  trainFromDate: number | null;
  trainToDate: number | null;
}

export const processChartData = (
  dataAi: DataAiObj,
  useSearchData: boolean,
  searchFromValue: string,
  searchToValue: string
): ProcessedChartData => {
  const { timestamps, real_shaft_powers, upper_bounds, lower_bounds, model_train_from, model_train_to } = dataAi;

  // First, ensure we only use timestamps that have corresponding real shaft power values
  // This prevents showing predictions without real data
  const validTimestamps: string[] = [];
  const validRealShaftPowers: number[] = [];
  const validUpperBounds: number[] = [];
  const validLowerBounds: number[] = [];

  // Only include data points where we have real shaft power values
  for (let i = 0; i < timestamps.length && i < real_shaft_powers.length; i++) {
    validTimestamps.push(timestamps[i]);
    validRealShaftPowers.push(real_shaft_powers[i]);

    // Make sure we don't go out of bounds for prediction arrays
    if (i < upper_bounds.length) {
      validUpperBounds.push(upper_bounds[i]);
    } else {
      validUpperBounds.push(0); // Fallback value
    }

    if (i < lower_bounds.length) {
      validLowerBounds.push(lower_bounds[i]);
    } else {
      validLowerBounds.push(0); // Fallback value
    }
  }

  // Filter data based on search date range if search is active
  let filteredTimestamps = [...validTimestamps];
  let filteredRealShaftPowers = [...validRealShaftPowers];
  let filteredUpperBounds = [...validUpperBounds];
  let filteredLowerBounds = [...validLowerBounds];

  if (useSearchData && searchFromValue && searchToValue) {
    const fromDate = new Date(searchFromValue).getTime();
    const toDate = new Date(searchToValue).getTime();

    // Create filtered arrays based on date range
    const filteredData = timestamps
      .map((timestamp, index) => {
        const timestampDate = new Date(timestamp).getTime();
        return {
          timestamp,
          inRange: timestampDate >= fromDate && timestampDate <= toDate,
          index,
        };
      })
      .filter((item) => item.inRange);

    if (filteredData.length > 0) {
      filteredTimestamps = filteredData.map((item) => item.timestamp);
      filteredRealShaftPowers = filteredData.map((item) => real_shaft_powers[item.index]);
      filteredUpperBounds = filteredData.map((item) => upper_bounds[item.index]);
      filteredLowerBounds = filteredData.map((item) => lower_bounds[item.index]);
    }
  }

  // Use filtered data if search is active, otherwise use original data
  const dataTimestamps = useSearchData && filteredTimestamps.length > 0 ? filteredTimestamps : timestamps;
  const dataRealShaftPowers = useSearchData && filteredRealShaftPowers.length > 0 ? filteredRealShaftPowers : real_shaft_powers;
  const dataUpperBounds = useSearchData && filteredUpperBounds.length > 0 ? filteredUpperBounds : upper_bounds;
  const dataLowerBounds = useSearchData && filteredLowerBounds.length > 0 ? filteredLowerBounds : lower_bounds;

  // Calculate the typical time difference between consecutive points (in milliseconds)
  let typicalTimeDiff = 0;
  let diffCount = 0;

  for (let i = 1; i < dataTimestamps.length; i++) {
    const currentTime = parseUTCTimestamp(dataTimestamps[i]);
    const prevTime = parseUTCTimestamp(dataTimestamps[i - 1]);
    const diff = currentTime - prevTime;

    // Only count reasonable differences (avoid outliers)
    if (diff > 0 && diff < 30 * 24 * 60 * 60 * 1000) {
      // Less than 30 days
      typicalTimeDiff += diff;
      diffCount++;
    }
  }

  // Calculate average time difference if we have enough data points
  typicalTimeDiff = diffCount > 0 ? typicalTimeDiff / diffCount : 7 * 24 * 60 * 60 * 1000; // Default to 1 week if not enough data

  // Threshold for gap detection (e.g., 2x the typical difference)
  const gapThreshold = typicalTimeDiff * 2;

  // Process the data with gap detection for real shaft power
  const processedRealData: [number, number | null][] = [];
  for (let i = 0; i < dataTimestamps.length; i++) {
    const currentTime = parseUTCTimestamp(dataTimestamps[i]);
    const value = dataRealShaftPowers[i];

    // Check if there's a gap between this point and the previous one
    if (i > 0) {
      const prevTime = parseUTCTimestamp(dataTimestamps[i - 1]);
      const timeDiff = currentTime - prevTime;

      // If the time difference is larger than our threshold, insert a null point to create a gap
      if (timeDiff > gapThreshold) {
        // Add a null point right after the previous point
        processedRealData.push([prevTime + 1, null]);
        // Add another null point right before the current point
        processedRealData.push([currentTime - 1, null]);
      }
    }

    // Add the actual data point
    processedRealData.push([currentTime, value]);
  }

  // Process the data with gap detection for the predicted range
  const processedPredictedRangeData: [number, number | null, number | null][] = [];
  for (let i = 0; i < dataTimestamps.length; i++) {
    const currentTime = parseUTCTimestamp(dataTimestamps[i]);
    const lowerValue = dataLowerBounds[i];
    const upperValue = dataUpperBounds[i];

    // Check if there's a gap between this point and the previous one
    if (i > 0) {
      const prevTime = parseUTCTimestamp(dataTimestamps[i - 1]);
      const timeDiff = currentTime - prevTime;

      // If the time difference is larger than our threshold, insert a null point to create a gap
      if (timeDiff > gapThreshold) {
        // Add a null point right after the previous point
        processedPredictedRangeData.push([prevTime + 1, null, null]);
        // Add another null point right before the current point
        processedPredictedRangeData.push([currentTime - 1, null, null]);
      }
    }

    // Add the actual data point
    processedPredictedRangeData.push([currentTime, lowerValue, upperValue]);
  }

  // Find min and max dates for reference period (using UTC)
  const trainFromDate = model_train_from ? parseUTCTimestamp(model_train_from) : null;
  const trainToDate = model_train_to ? parseUTCTimestamp(model_train_to) : null;

  // Create plotLines for reference period boundaries (without labels)
  const referenceStartLine = trainFromDate
    ? {
        value: trainFromDate,
        width: 1,
        color: 'rgba(100, 150, 200, 0.8)',
        dashStyle: 'dash' as Highcharts.DashStyleValue,
        zIndex: 5,
      }
    : null;

  const referenceEndLine = trainToDate
    ? {
        value: trainToDate,
        width: 1,
        color: 'rgba(100, 150, 200, 0.8)',
        dashStyle: 'dash' as Highcharts.DashStyleValue,
        zIndex: 5,
      }
    : null;

  // Create plotBand for reference period (colored area with label)
  const referencePeriodBand =
    trainFromDate && trainToDate
      ? {
          from: trainFromDate,
          to: trainToDate,
          color: 'rgba(220, 240, 255, 0.3)',
          zIndex: 0,
          label: {
            text: 'Reference Period',
            align: 'center' as Highcharts.AlignValue,
            verticalAlign: 'top' as Highcharts.VerticalAlignValue,
            y: 15,
            style: {
              color: '#666',
              fontWeight: 'bold',
              fontSize: '12px',
            },
          },
        }
      : null;

  // Create arrays for plotLines and plotBands
  const plotLines = [];
  if (referenceStartLine) plotLines.push(referenceStartLine);
  if (referenceEndLine) plotLines.push(referenceEndLine);

  const plotBands = [];
  if (referencePeriodBand) plotBands.push(referencePeriodBand);

  return {
    realData: processedRealData,
    predictedRangeData: processedPredictedRangeData,
    plotLines,
    plotBands,
    trainFromDate,
    trainToDate,
  };
};
