'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import React from 'react';
import { useDataAnalyticsStore } from '../../data-analytics.store';
import { useChartData, getEmptyBeauforts } from './data-analytics-chart-con-speed.data';
import { getChartOptions } from './data-analytics-chart-con-speed.options';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import DataAnalyticsChartTable from './data-analytics-chart-table';

export default function DataAnalyticsChartConSpeed() {
  const [chartType, setChartType] = React.useState<'chart' | 'table'>('chart');
  const [dataSet, setDataSet] = React.useState<'best' | 'raw'>('best');
  const [speedType, setSpeedType] = React.useState<'lspd' | 'gspd'>('lspd');
  const [beaufort, setBeaufort] = React.useState<string>('4');

  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const chartData = useChartData(dataAnalytics, beaufort, speedType, dataSet);

  const { keys, emptyKeys } = getEmptyBeauforts(dataAnalytics, speedType, dataSet);

  const isEmpty = !isLoading && !chartData;

  const options = chartData ? getChartOptions(chartData) : undefined;

  return (
    <div className='bg-card text-card-foreground relative min-h-[300px] rounded-md border p-4'>
      <div className='mb-4 flex flex-col gap-1 md:flex-row md:items-center md:justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Relationship between ME Consumption and Speed</div>
        <div className='relative right-3 flex items-center gap-2'>
          <Tabs value={chartType} onValueChange={(val) => setChartType(val as 'chart' | 'table')}>
            <TabsList className='flex'>
              <TabsTrigger value='chart' disabled={isLoading}>
                Chart
              </TabsTrigger>
              <TabsTrigger value='table' disabled={isLoading}>
                Table
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Tabs value={dataSet} onValueChange={(val) => setDataSet(val as 'best' | 'raw')}>
            <TabsList className='flex'>
              <TabsTrigger value='best' disabled={isLoading}>
                Best Fit
              </TabsTrigger>
              <TabsTrigger value='raw' disabled={isLoading}>
                Raw
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Tabs value={speedType} onValueChange={(val) => setSpeedType(val as 'lspd' | 'gspd')}>
            <TabsList className='flex'>
              <TabsTrigger value='lspd' disabled={isLoading}>
                Log Speed
              </TabsTrigger>
              <TabsTrigger value='gspd' disabled={isLoading}>
                GPS Speed
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Select value={beaufort} onValueChange={setBeaufort} disabled={isLoading}>
            <SelectTrigger>
              <SelectValue>Beaufort {beaufort}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {keys.map((key) => (
                <SelectItem key={key} value={key} disabled={emptyKeys.includes(key)}>
                  Beaufort {key}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      {isLoading ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        <>
          {chartType === 'chart' && options && <HighchartsReact key={JSON.stringify(options)} highcharts={Highcharts} options={options} />}
          {chartType === 'table' && chartData && (
            <DataAnalyticsChartTable xValues={chartData.categories} yValues={chartData.labels} data={chartData.yValues} />
          )}
        </>
      )}
    </div>
  );
}
