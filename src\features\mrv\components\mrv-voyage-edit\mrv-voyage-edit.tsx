'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useMrvVoyageEditStore } from './mrv-voyage-edit.store';
import { MrvVoyageEditForm } from './mrv-voyage-edit-form';

export function MrvVoyageEdit() {
  const { isOpen, closeDialog } = useMrvVoyageEditStore();

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent>
        <MrvVoyageEditForm onSuccess={closeDialog} onCancel={closeDialog} />
      </DialogContent>
    </Dialog>
  );
}
