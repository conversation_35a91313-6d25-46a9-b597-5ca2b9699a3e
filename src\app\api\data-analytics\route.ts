import { DataAnalyticsResponse } from '@/features/data-analytics/data-analytics.types';
import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z
  .object({
    vessel_imo: z.string().nonempty('vessel_imo must be provided'),
    vessel_id: z.string().nonempty('vessel_id must be provided'),
    vessel_name: z.string().nonempty('vessel_name must be provided'),
    owner_vat: z.string().nonempty('owner_vat must be provided'),
    from_date: z.string().nonempty('from_date must be provided'),
    to_date: z.string().nonempty('to_date must be provided'),
  })
  .refine((data) => data.from_date <= data.to_date, {
    message: 'from_date must be before to_date',
    path: ['from_date'],
  });

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/data-analytics', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const response = await httpClient.get<DataAnalyticsResponse>(request, url.toString());
    return NextResponse.json(response);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
