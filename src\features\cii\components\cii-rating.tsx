'use client';

import { DotLoader } from '@/components/ui-extensions/dot-loader';
import { useCIIStore } from '../cii.store';
import { Badge } from '@/components/ui/badge';

const formatNumber = (val: unknown, decimals = 2): string => {
  const num = Number(val);
  return Number.isFinite(num) ? num.toFixed(decimals) : 'N/A';
};

const VesselStats = ({ label, value, loading }: { label: string; value: string | number; loading: boolean }) => (
  <div className='flex justify-between'>
    <p className='text-muted-foreground text-sm font-medium'>{label}:</p>
    {loading ? <DotLoader size='0.5rem' /> : <p className='text-sm font-medium'>{value}</p>}
  </div>
);

export default function CIIRating() {
  const { isLoading, yearData } = useCIIStore();

  const computeDeviation = (): string => {
    const attained = Number(yearData?.cii?.attained_cii);
    const required = Number(yearData?.cii?.required_cii);

    if (Number.isFinite(attained) && Number.isFinite(required) && attained + required !== 0) {
      const deviation = (Math.abs(attained - required) / ((attained + required) / 2)) * 100;
      return `${deviation.toFixed(2)}%`;
    }

    return 'N/A';
  };

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='text-lg font-semibold tracking-tight'>CII Rating</div>
        <Badge variant='outline' className='rounded-md border border-green-400 bg-green-50 px-3 py-1 text-green-800'>
          Estimated
        </Badge>
      </div>

      <div className='space-y-3'>
        <VesselStats label='Attained' value={formatNumber(yearData?.cii?.attained_cii)} loading={isLoading} />
        <VesselStats label='Required' value={formatNumber(yearData?.cii?.required_cii)} loading={isLoading} />
        <VesselStats label='Deviation' value={computeDeviation()} loading={isLoading} />
      </div>
    </div>
  );
}
