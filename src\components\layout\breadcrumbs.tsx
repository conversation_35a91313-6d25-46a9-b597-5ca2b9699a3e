'use client';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const routes = [
  {
    title: 'Home',
    url: '/dashboard',
    active: true,
  },
  {
    title: 'Data Analytics',
    url: '/dashboard/data-analytics',
    active: true,
  },
  {
    title: 'CII',
    url: '/dashboard/cii',
    active: true,
  },
  {
    title: 'Predictive Maintenance',
    url: '/dashboard/predictive-maintenance',
    active: false,
  },
  {
    title: 'Anomaly Detection',
    url: '/dashboard/predictive-maintenance/anomaly-detection',
    active: true,
  },
  {
    title: 'Hull Performance',
    url: '/dashboard/predictive-maintenance/hull-performance',
    active: true,
  },
  {
    title: 'MRV',
    url: '/dashboard/mrv',
    active: true,
  },
  {
    title: 'Users',
    url: '/dashboard/users',
    active: true,
  },
  {
    title: 'Export',
    url: '/dashboard/export',
    active: true,
  },
];

export function Breadcrumbs({}: React.ComponentProps<'div'>) {
  const pathname = usePathname();

  const breadcrumbItems = routes.filter((route) => pathname.startsWith(route.url));

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((route, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          return (
            <BreadcrumbItem key={route.url}>
              {route.active && !isLast ? (
                <BreadcrumbLink asChild>
                  <Link href={route.url}>{route.title}</Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage className='text-muted-foreground'>{route.title}</BreadcrumbPage>
              )}
              {!isLast && <BreadcrumbSeparator />}
            </BreadcrumbItem>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
