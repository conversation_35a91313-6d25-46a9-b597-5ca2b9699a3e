'use client';

import { useCIIStore } from '../cii.store';
import { DotLoader } from '@/components/ui-extensions/dot-loader';
import { Badge } from '@/components/ui/badge';

const getGradeBadgeColor = (grade?: string): string => {
  switch (grade?.toUpperCase()) {
    case 'A':
      return 'bg-green-500';
    case 'B':
      return 'bg-lime-500';
    case 'C':
      return 'bg-yellow-500';
    case 'D':
      return 'bg-orange-500';
    case 'E':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const GradeValue = ({ label, grade, isLoading }: { label: string; grade?: string; isLoading: boolean }) => (
  <div className='flex justify-between'>
    <p className='text-muted-foreground text-sm font-medium'>{label}</p>
    {isLoading ? (
      <DotLoader />
    ) : grade ? (
      <Badge className={`rounded-md px-3 py-0 text-white ${getGradeBadgeColor(grade)}`}>{grade}</Badge>
    ) : (
      <span className='text-muted-foreground text-sm font-medium'>N/A</span>
    )}
  </div>
);

const TextValue = ({ label, value, isLoading }: { label: string; value?: string | number | null; isLoading: boolean }) => (
  <div className='flex justify-between'>
    <p className='text-muted-foreground text-sm font-medium'>{label}</p>
    {isLoading ? (
      <DotLoader />
    ) : value != null ? (
      <p className='text-sm font-medium'>{value}</p>
    ) : (
      <span className='text-muted-foreground text-sm font-medium'>N/A</span>
    )}
  </div>
);

export default function CIIGrade() {
  const { isLoading, yearData } = useCIIStore();

  const attainedGrade = yearData?.cii?.cii_letter_rating;
  const projectedGrade = yearData?.cii?.projected_cii?.next_year;
  const deteriorationYear = yearData?.cii?.projected_cii?.deterioration_year;

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Grade</div>
      </div>

      <div className='space-y-3'>
        <GradeValue label='Attained Grade YTD:' grade={attainedGrade} isLoading={isLoading} />
        <GradeValue label='Projected Grade Next Year:' grade={projectedGrade} isLoading={isLoading} />
        <TextValue label='Projected Year of Deterioration:' value={deteriorationYear} isLoading={isLoading} />
      </div>
    </div>
  );
}
