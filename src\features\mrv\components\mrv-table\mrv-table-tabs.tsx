'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';

export function MrvTableTabs() {
  // Store & state
  const { selectedTab, setSelectedTab } = useMrvTableStore();

  // Handle tab change
  const handleTabChange = (value: string) => {
    setSelectedTab(value);
  };

  return (
    <Tabs value={selectedTab} onValueChange={handleTabChange}>
      <TabsList className='flex'>
        <TabsTrigger value='all'>All</TabsTrigger>
        <TabsTrigger value='incomplete'>Incomplete</TabsTrigger>
        <TabsTrigger value='complete'>Complete</TabsTrigger>
        <TabsTrigger value='error'>Error</TabsTrigger>
        <TabsTrigger value='approved'>Approved</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
