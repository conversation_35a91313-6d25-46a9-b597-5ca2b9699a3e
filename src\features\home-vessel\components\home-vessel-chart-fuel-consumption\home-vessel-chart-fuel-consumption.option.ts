import Highcharts from 'highcharts';
import { SeriesDaily, SeriesMonthly } from './home-vessel-chart-fuel-consumption.types';
import { colorMap } from './home-vessel-chart-fuel-consumption.constants';

export function getChartOptionsDaily(chartData: SeriesDaily): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      type: 'column',
      name: 'Fuel (Tons)',
      data: chartData.yValues,
      yAxis: 0,
      color: colorMap.fuel,
    },
    {
      type: 'line',
      name: chartData.speedType + ' (kn)',
      data: chartData.speedValues,
      yAxis: 1,
      color: colorMap.speed,
      marker: { enabled: true },
    },
    {
      type: 'scatter',
      name: '<PERSON>',
      data: chartData.beaufortValues,
      yAxis: 2,
      color: colorMap.beaufort,
      enableMouseTracking: false,
    },
  ];

  return {
    chart: { zooming: { type: 'x' } },
    exporting: { enabled: false },
    credits: { enabled: false },
    title: { text: '' },
    xAxis: {
      title: { text: '' },
      crosshair: true,
      categories: chartData.dayLabels,
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        min: 0,
        title: { text: 'Fuel Consumption (Tons)', style: { color: colorMap.fuel } },
        labels: { style: { color: colorMap.fuel } },
      },
      {
        title: { text: chartData.speedType + ' (kn)', style: { color: colorMap.speed } },
        opposite: true,
        labels: { style: { color: colorMap.speed } },
      },
      {
        title: { text: 'Beaufort', style: { color: colorMap.beaufort } },
        opposite: true,
        labels: { style: { color: colorMap.beaufort } },
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltipDaily(this, chartData);
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
  };
}

export function formatTooltipDaily(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number; index: number }[] },
  chartData: SeriesDaily
) {
  const index = ctx?.points?.[0]?.index ?? 0;
  const details = chartData.detailValues[index];

  let tooltip = `<div style="font-family: Arial, sans-serif; font-size: 13px; color: #222;">`;
  tooltip += `<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">${chartData.dayLabels[index]}</div>`;

  if (details && ctx.points?.[0]) {
    const consumption = ctx.points[0].y;

    tooltip += `<hr style="margin:8px 0; border-color: #ddd;" />`;
    tooltip += `<table style="font-size: 12px; color: #555; width: 100%;">`;
    tooltip += `<tbody>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Consumption:</td><td style="padding: 2px 4px;">${consumption} Tons</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Average ${chartData.speedType}:</td><td style="padding: 2px 4px;">${chartData.speedType === 'Log Speed' ? details[1].log_speed : chartData.speedType === 'GPS Speed' ? details[1].gps_speed : 'N/A'} knots</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Beaufort:</td><td style="padding: 2px 4px;">${details[1].bf}</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Data Received:</td><td style="padding: 2px 4px;">${details[1].PctData ?? 'N/A'}%</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Time Sailed:</td><td style="padding: 2px 4px;">${details[1].HoursSailed ?? 'N/A'}</td></tr>`;
    tooltip += `</tbody>`;
    tooltip += `</table>`;
  }

  tooltip += `</div>`;

  return tooltip;
}

export function getChartOptionsMonthly(chartData: SeriesMonthly, setSelectedMonth?: (month: string) => void): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      type: 'column',
      name: 'Fuel (Tons)',
      data: chartData.yValues,
      yAxis: 0,
      color: colorMap.fuel,
    },
  ];

  return {
    chart: { zooming: { type: 'x' } },
    exporting: { enabled: false },
    credits: { enabled: false },
    title: { text: '' },
    xAxis: {
      title: { text: '' },
      crosshair: true,
      categories: chartData.monthLabels,
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        min: 0,
        title: { text: 'Fuel Consumption (Tons)', style: { color: colorMap.fuel } },
        labels: { style: { color: colorMap.fuel } },
      },
    ],
    series,
    plotOptions: {
      series: {
        cursor: 'pointer',
        point: {
          events: {
            click: function () {
              const index = this.index;
              const clickedMonth = chartData.monthLabels[index];
              setSelectedMonth?.(clickedMonth);
            },
          },
        },
      },
    },
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltipMonthly(this, chartData);
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
  };
}

export function formatTooltipMonthly(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number; index: number }[] },
  chartData: SeriesMonthly
) {
  const index = ctx?.points?.[0]?.index ?? 0;
  const details = chartData.detailValues[index];

  let tooltip = `<div style="font-family: Arial, sans-serif; font-size: 13px; color: #222;">`;
  tooltip += `<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">${chartData.monthLabels[index]}</div>`;

  if (details && ctx.points?.[0]) {
    const consumption = ctx.points[0].y;

    tooltip += `<hr style="margin:8px 0; border-color: #ddd;" />`;
    tooltip += `<table style="font-size: 12px; color: #555; width: 100%;">`;
    tooltip += `<tbody>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Consumption:</td><td style="padding: 2px 4px;">${consumption} Tons</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Data Received:</td><td style="padding: 2px 4px;">${details.PctData ?? 'N/A'}%</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Time Sailed:</td><td style="padding: 2px 4px;">${details.HoursSailed ?? 'N/A'}</td></tr>`;
    tooltip += `</tbody>`;
    tooltip += `</table>`;
  }

  tooltip += `</div>`;

  return tooltip;
}
