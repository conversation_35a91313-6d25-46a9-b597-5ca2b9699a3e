'use client';

import React from 'react';
import { flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { cn } from '@/lib/utils';
import { columns, Sensor } from './anomaly-detection-overview-table-columns';
import { useAnomalyDetectionStore } from '../../anomaly-detection.store';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { SignalAnomalySummary } from '../../anomaly-detection.types';

// Helper function to calculate the actual time range from sensor data
const calculateSensorTimeRange = (sensorData: SignalAnomalySummary): string => {
  if (!sensorData?.data || typeof sensorData.data !== 'object') {
    return 'No data available';
  }

  const timestamps = Object.keys(sensorData.data);
  if (timestamps.length === 0) {
    return 'No data available';
  }

  // Sort timestamps to get the earliest and latest
  const sortedTimestamps = timestamps.sort();
  const earliestTimestamp = sortedTimestamps[0];
  const latestTimestamp = sortedTimestamps[sortedTimestamps.length - 1];

  return `${earliestTimestamp} - ${latestTimestamp}`;
};

export function AnomalyDetectionOverviewTable() {
  const { anomalyDetectionData, setSelectedSensor, selectedSensor, selectedSensors, isLoading } = useAnomalyDetectionStore();

  const data: Sensor[] = React.useMemo(() => {
    return Object.entries(anomalyDetectionData || {})
      .map(([key, value]) => {
        if (typeof value !== 'object' || !value.name) return null;

        if (selectedSensors.includes(key)) {
          return {
            key: key,
            name: value.name,
            time_range: calculateSensorTimeRange(value),
            overall_min: value.overall_min,
            overall_max: value.overall_max,
            overall_mean: value.overall_mean,
            overall_outlier_count: value.overall_outlier_count,
          };
        }
      })
      .filter(Boolean) as Sensor[];
  }, [anomalyDetectionData, selectedSensors]);

  React.useEffect(() => {
    if (data.length > 0 && !selectedSensor) {
      setSelectedSensor(data[0].key);
    }
  }, [data, selectedSensor, setSelectedSensor]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleRowClick = (key: string) => {
    setSelectedSensor(key);
  };

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader className='bg-muted'>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id} className='text-muted-foreground'>
                  {flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length > 0 ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                onClick={() => handleRowClick(row.original.key)}
                className={cn('hover:bg-muted/50 cursor-pointer transition-colors', selectedSensor === row.original.key && 'bg-muted/50')}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className='h-24 text-center'>
                {isLoading ? 'Loading...' : 'No data found'}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
