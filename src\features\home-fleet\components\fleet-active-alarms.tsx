'use client';

import * as React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

type Alarm = {
  vessel: string;
  alarmId: string;
  type: string;
  timestamp: string;
};

// Sample data
const activeAlarms: Alarm[] = [
  { vessel: 'Aurora', alarmId: 'A101', type: 'Engine', timestamp: '2025-05-07 14:22:10' },
  { vessel: 'Nebula', alarmId: 'B205', type: 'Fire', timestamp: '2025-05-07 13:15:02' },
  { vessel: 'Orion', alarmId: 'C307', type: 'Overheat', timestamp: '2025-05-07 12:01:45' },
  { vessel: 'Aurora', alarmId: 'A108', type: 'Navigation', timestamp: '2025-05-07 11:53:30' },
  { vessel: 'Vega', alarmId: 'V004', type: 'Power', timestamp: '2025-05-07 10:42:17' },
  { vessel: 'Nebula', alarmId: 'B205', type: 'Fire', timestamp: '2025-05-07 13:15:02' },
  { vessel: 'Orion', alarmId: 'C307', type: 'Overheat', timestamp: '2025-05-07 12:01:45' },
  { vessel: 'Aurora', alarmId: 'A108', type: 'Navigation', timestamp: '2025-05-07 11:53:30' },
  { vessel: 'Vega', alarmId: 'V004', type: 'Power', timestamp: '2025-05-07 10:42:17' },
  { vessel: 'Nebula', alarmId: 'B205', type: 'Fire', timestamp: '2025-05-07 13:15:02' },
];

export default function FleetActiveAlarms() {
  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Active Alarms</div>
      <div className='h-80 overflow-auto'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vessel</TableHead>
              <TableHead>Alarm ID</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Timestamp</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {activeAlarms.length > 0 ? (
              activeAlarms.map((alarm, index) => (
                <TableRow key={index}>
                  <TableCell>{alarm.vessel}</TableCell>
                  <TableCell>{alarm.alarmId}</TableCell>
                  <TableCell>{alarm.type}</TableCell>
                  <TableCell className='text-muted-foreground'>{alarm.timestamp}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className='text-muted-foreground text-center text-sm'>
                  No alarms match the filters.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
