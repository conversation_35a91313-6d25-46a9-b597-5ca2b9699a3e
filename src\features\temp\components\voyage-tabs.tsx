'use client';

import * as React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useVoyageStore } from '@/features/temp/voyage.store';

export function VoyageTabs() {
  const { selectedTab, setSelectedTab, isLoading } = useVoyageStore();

  return (
    <Tabs value={selectedTab} onValueChange={setSelectedTab}>
      <TabsList className='flex'>
        <TabsTrigger value='all' disabled={isLoading}>
          All
        </TabsTrigger>
        <TabsTrigger value='incomplete' disabled={isLoading}>
          Incomplete
        </TabsTrigger>
        <TabsTrigger value='complete' disabled={isLoading}>
          Complete
        </TabsTrigger>
        <TabsTrigger value='error' disabled={isLoading}>
          Error
        </TabsTrigger>
        <TabsTrigger value='approved' disabled={isLoading}>
          Approved
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
