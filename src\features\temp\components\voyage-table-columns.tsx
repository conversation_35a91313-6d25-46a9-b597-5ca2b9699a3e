'use client';

import { Column, ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { SeverityBadge } from '@/components/ui-extensions/severity-badge';
import { Severity } from '@/components/ui-extensions/severity-badge';
import { VoyageGetDTO } from '../voyage.types';
import { VoyageDelete } from './voyage-delete';
import { VoyageEdit } from './voyage-edit';
import { ReportGenerateSingle } from './report-generate-single';
import { format } from 'date-fns';

export const statusToSeverity: Record<string, Severity> = {
  Error: 'error',
  Incomplete: 'warning',
  Complete: 'success',
  Approved: 'info',
};

// Sortable Header Component
function SortableHeader({ column, title }: { column: Column<VoyageGetDTO, unknown>; title: string }) {
  return (
    <Button variant='ghost' className='!p-0' onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
      {title} <ArrowUpDown />
    </Button>
  );
}

// Define Columns
export const columns: ColumnDef<VoyageGetDTO>[] = [
  {
    accessorKey: 'departure_port',
    header: 'Departure Port',
  },
  {
    accessorKey: 'destination_port',
    header: 'Destination Port',
  },
  {
    accessorKey: 'start_time',
    header: ({ column }) => <SortableHeader column={column} title='Departure Time' />,
    cell: ({ row }) => format(new Date(row.getValue('start_time')), 'LLL dd, yyyy p'),
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'end_time',
    header: ({ column }) => <SortableHeader column={column} title='Arrival Time' />,
    cell: ({ row }) => format(new Date(row.getValue('end_time')), 'LLL dd, yyyy p'),
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'voyage_status',
    header: ({ column }) => <SortableHeader column={column} title='Status' />,
    cell: ({ row }) => {
      const status = row.getValue('voyage_status') as keyof typeof statusToSeverity;
      const severity = statusToSeverity[status];
      const errors: string[] = (row.original as VoyageGetDTO).errors || [];

      if (status === 'Error') {
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='inline-block'>
                <SeverityBadge severity={severity}>{status}</SeverityBadge>
              </span>
            </TooltipTrigger>

            <TooltipContent side='top' align='center' className='bg-primary max-w-sm p-4 break-words'>
              <h4 className='mb-2 text-center text-base font-semibold text-white'>Error Details</h4>

              {errors.length > 0 ? (
                <ul className='space-y-2'>
                  {errors.map((err, idx) => {
                    const lines = err.split(/\n|(?<!:)\s{2,}/).filter(Boolean);
                    return (
                      <li key={idx} className='rounded border border-white bg-white p-2 text-black'>
                        {lines.map((line, i) => {
                          const kv = line.match(/^([\w\s]+):\s*(.+)$/);
                          return kv ? (
                            <div key={i} className='flex justify-between text-sm'>
                              <span className='font-semibold'>{kv[1]}</span>
                              <span>{kv[2]}</span>
                            </div>
                          ) : (
                            <div key={i} className='text-sm'>
                              {line}
                            </div>
                          );
                        })}
                      </li>
                    );
                  })}
                </ul>
              ) : (
                <ul className='space-y-2'>
                  <li className='rounded border border-white bg-white p-2 text-center text-black'>
                    Voyage timeframe is overlapping with another voyage
                  </li>
                </ul>
              )}
            </TooltipContent>
          </Tooltip>
        );
      }

      return <SeverityBadge severity={severity}>{status}</SeverityBadge>;
    },
    sortingFn: 'text',
  },
  {
    id: 'actions',
    header: '',
    cell: ({ row }) => (
      <div className='flex justify-center space-x-2'>
        <ReportGenerateSingle voyageId={row.original.id} />
        <VoyageEdit voyage={row.original} />
        <VoyageDelete voyageId={row.original.id} />
      </div>
    ),
  },
];
