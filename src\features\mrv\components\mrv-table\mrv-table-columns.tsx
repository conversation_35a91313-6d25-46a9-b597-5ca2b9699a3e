'use client';

import { Column, ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Pencil, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SeverityBadge } from '@/components/ui-extensions/severity-badge';
import { Severity } from '@/components/ui-extensions/severity-badge';
import { MrvVoyage } from '../../mrv.types';
import { MrvReportGenerateSingle } from '../mrv-report/mrv-report-generate-single';
import { format } from 'date-fns';
import { useMrvVoyageDeleteStore } from '../mrv-voyage-delete.ts/mrv.voyage.delete.store';
import { useMrvVoyageEditStore } from '../mrv-voyage-edit/mrv-voyage-edit.store';

// State for the dialog open/close
export const statusToSeverity: Record<string, Severity> = {
  Error: 'error',
  Incomplete: 'warning',
  Complete: 'success',
  Approved: 'info',
};

// Sortable Header Component
function SortableHeader({ column, title }: { column: Column<MrvVoyage, unknown>; title: string }) {
  return (
    <Button variant='ghost' className='!p-0' onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
      {title} <ArrowUpDown />
    </Button>
  );
}

// Extracted Status cell component
function StatusCell({ status }: { status: string }) {
  const severity = statusToSeverity[status] ?? 'info';
  return <SeverityBadge severity={severity}>{status}</SeverityBadge>;
}

// Extracted Actions cell component
function ActionsCell({ voyage }: { voyage: MrvVoyage }) {
  const { openDialog: openDeleteDialog } = useMrvVoyageDeleteStore();
  const { openDialog: openEditDialog } = useMrvVoyageEditStore();

  return (
    <div className='flex justify-center space-x-2'>
      <MrvReportGenerateSingle voyageId={voyage.voyage_id} />
      <Button variant='ghost' size='icon' onClick={() => openEditDialog(voyage)}>
        <Pencil className='h-4 w-4 text-blue-500' />
      </Button>
      <Button variant='ghost' size='icon' onClick={() => openDeleteDialog(voyage)}>
        <Trash className='h-4 w-4 text-red-500' />
      </Button>
    </div>
  );
}

// Define Columns
export const columns: ColumnDef<MrvVoyage>[] = [
  {
    accessorKey: 'departure_port',
    header: ({ column }) => <SortableHeader column={column} title='Departure Port' />,
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'destination_port',
    header: ({ column }) => <SortableHeader column={column} title='Destination Port' />,
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'start_time',
    header: ({ column }) => <SortableHeader column={column} title='Departure Time' />,
    cell: ({ row }) => format(new Date(row.getValue('start_time')), 'LLL dd, yyyy p'),
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'end_time',
    header: ({ column }) => <SortableHeader column={column} title='Arrival Time' />,
    cell: ({ row }) => format(new Date(row.getValue('end_time')), 'LLL dd, yyyy p'),
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'voyage_status',
    header: ({ column }) => <SortableHeader column={column} title='Status' />,
    cell: ({ row }) => <StatusCell status={row.getValue('voyage_status')} />,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actions',
    header: '',
    cell: ({ row }) => <ActionsCell voyage={row.original} />,
  },
];
