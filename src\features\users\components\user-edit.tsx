'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { UserGetDTO, UserRole } from '../user.types';
import { useUserStore } from '../user.store';
import { Pencil } from 'lucide-react';
import { useForm } from 'react-hook-form';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { Label } from '@/components/ui/label';
import { useSession } from 'next-auth/react';
import { ExtendedSession } from '@/lib/auth';

const roleLabelMap: Record<UserRole, string> = {
  [UserRole['Super Admin']]: 'Super Admin',
  [UserRole.Admin]: 'Admin',
  [UserRole.Basic]: 'Basic',
};

export function UserEdit({ user }: { user: UserGetDTO }) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { updateUser } = useUserStore();
  const { data: session } = useSession();

  const form = useForm<{ role: UserRole }>({
    defaultValues: {
      role: user.role,
    },
  });

  if (!session) {
    return (
      <Button variant='ghost' size='icon'>
        <CircleSpinner variant='primary' size='xs' />
      </Button>
    );
  }

  const extendedSession = session as ExtendedSession;
  const role = extendedSession.current_owner.role;

  const onSubmit = async (data: { role: UserRole }) => {
    setIsLoading(true);
    try {
      await updateUser({ ...user }, data.role);
      setOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant='ghost' size='icon' disabled={role !== UserRole['Super Admin']}>
          {isLoading ? <CircleSpinner variant='primary' size='xs' /> : <Pencil className='h-4 w-4 text-blue-500' />}
        </Button>
      </PopoverTrigger>

      <PopoverContent className='w-60'>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Label htmlFor='role' className='mb-1'>
            Role
          </Label>
          <Select
            value={form.watch('role').toString()}
            onValueChange={(value: string) => form.setValue('role', parseInt(value) as UserRole)}
            disabled={isLoading}
          >
            <SelectTrigger className='mb-4 w-full'>
              <SelectValue placeholder='Select role'>{roleLabelMap[form.watch('role')]}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {role === UserRole['Super Admin'] && <SelectItem value={UserRole['Super Admin'].toString()}>Super Admin</SelectItem>}
              <SelectItem value={UserRole.Admin.toString()}>Admin</SelectItem>
              <SelectItem value={UserRole.Basic.toString()}>Basic</SelectItem>
            </SelectContent>
          </Select>

          <div className='flex justify-end gap-2'>
            <Button type='button' size='sm' variant='outline' onClick={() => setOpen(false)}>
              Close
            </Button>
            <Button type='submit' size='sm' disabled={isLoading}>
              {isLoading && <CircleSpinner size='xs' />}
              Save
            </Button>
          </div>
        </form>
      </PopoverContent>
    </Popover>
  );
}
