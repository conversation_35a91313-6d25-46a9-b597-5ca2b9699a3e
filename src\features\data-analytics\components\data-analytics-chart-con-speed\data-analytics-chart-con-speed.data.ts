import { DataAnalyticsResponse } from '../../data-analytics.types';
import { Series } from './data-analytics-chart-con-speed.types';

export function useChartData(
  data: DataAnalyticsResponse | null,
  beaufort: string,
  speedType: 'lspd' | 'gspd',
  dataSet: 'best' | 'raw'
): Series | null {
  if (!data) return null;

  const source = data.data_analytics.lspdMefcmBestFit;

  if (!source) return null;

  const speedData = speedType === 'lspd' ? source.lspd : source.gspd;
  if (!speedData) return null;

  const bfIndex = Number(beaufort);
  if (!(bfIndex in speedData)) return null;

  const beaufortData = speedData[bfIndex];
  if (!beaufortData) return null;

  const allX = new Set<string>();
  for (const bf of Object.values(beaufortData)) {
    if (bf && typeof bf === 'object') {
      for (const x of Object.keys(bf)) {
        allX.add(x);
      }
    }
  }

  const categories = Array.from(allX).sort((a, b) => parseFloat(a) - parseFloat(b));
  const labels = Object.keys(beaufortData).sort((a, b) => parseFloat(a) - parseFloat(b));
  let yValues = labels.map((label) => {
    const points = beaufortData[label] ?? {};
    return categories.map((x) => points[x] ?? null);
  });

  if (dataSet === 'raw') {
    const rawData = data.data_analytics.lspdMefcm;
    const speedData = speedType === 'lspd' ? rawData.lspd : rawData.gspd;
    if (!speedData) return null;
    const bfIndex = Number(beaufort);
    if (!(bfIndex in speedData)) return null;
    const beaufortData = speedData[bfIndex];
    if (!beaufortData) return null;

    const rawYValues: (number | null)[][] = [];
    Object.keys(beaufortData).forEach((label) => {
      const points = beaufortData[label] ?? {};
      const values = categories.map((x) => points[x] ?? null);
      rawYValues.push(values.map((value) => value?.fuel_consumption ?? null));
    });

    yValues = rawYValues;
  }

  return {
    categories,
    labels,
    yValues,
  };
}

export function getEmptyBeauforts(
  data: DataAnalyticsResponse | null,
  speedType: 'lspd' | 'gspd',
  dataSet: 'best' | 'raw'
): { keys: string[]; emptyKeys: string[] } {
  if (!data) return { keys: [], emptyKeys: [] };

  const source = dataSet === 'best' ? data.data_analytics.lspdMefcmBestFit : data.data_analytics.lspdMefcm;
  if (!source) return { keys: [], emptyKeys: [] };

  const speedData = speedType === 'lspd' ? source.lspd : source.gspd;
  if (!speedData) return { keys: [], emptyKeys: [] };

  const keys = Object.keys(speedData);

  const emptyKeys = keys.filter((key) => {
    const value = speedData[Number(key)];
    return typeof value === 'object' && value !== null && !Array.isArray(value) && Object.keys(value).length === 0;
  });

  return { keys, emptyKeys };
}
