'use client';

import { PageSizeSelector } from '@/components/ui-extensions/page-size-selector';
import { PaginationControls } from '@/components/ui-extensions/pagination-controls';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';
import { useFilteredVoyages } from '@/features/mrv/components/mrv-table/hooks/use-filtered-voyages';
import { useMrvStore } from '@/features/mrv/mrv.store';
import React from 'react';

export function MrvTablePaginator() {
  const { pageSize, pageIndex, setPageSize, setPageIndex } = useMrvTableStore();
  const { isLoading } = useMrvStore();
  const pageSizeOptions = [10, 25, 50, 100];

  const filteredVoyageCount = useFilteredVoyages().length;
  const pageCount = React.useMemo(() => Math.ceil(filteredVoyageCount / pageSize), [filteredVoyageCount, pageSize]);

  // On Next Page
  const onNextPage = React.useCallback(() => {
    setPageIndex(Math.min(pageIndex + 1, pageCount - 1));
  }, [pageIndex, pageCount, setPageIndex]);

  // On Previous Page
  const onPreviousPage = React.useCallback(() => {
    setPageIndex(Math.max(pageIndex - 1, 0));
  }, [pageIndex, setPageIndex]);

  // On Page Size Change
  const onPageSizeChange = React.useCallback(
    (size: number) => {
      setPageSize(size);
      setPageIndex(0);
    },
    [setPageSize, setPageIndex]
  );

  return (
    <div className='flex justify-end gap-4'>
      <PageSizeSelector pageSize={pageSize} onPageSizeChange={onPageSizeChange} pageSizeOptions={pageSizeOptions} disabled={isLoading} />
      <PaginationControls pageIndex={pageIndex} pageCount={pageCount} onPreviousPage={onPreviousPage} onNextPage={onNextPage} />
    </div>
  );
}
