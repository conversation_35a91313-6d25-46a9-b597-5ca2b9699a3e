'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useDataAnalyticsStore } from '../data-analytics.store';

export const DataAnalyticsCardHours = () => {
  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const data = dataAnalytics?.reports['fpfc'];

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Operational Hours</div>
      <div className='space-y-3'>
        <StatValue label='On Hours' value={data?.on_hours ? Number(data.on_hours.join('.')) : null} isLoading={isLoading} />
        <StatValue label='Off Hours' value={data?.off_hours ? Number(data.off_hours.join('.')) : null} isLoading={isLoading} />
        <StatValue
          label='Total Hours (On/Off)'
          value={data?.total_hours ? Number(data.total_hours.join('.')) : null}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};
