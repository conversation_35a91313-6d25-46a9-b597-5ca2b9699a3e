import Highcharts from 'highcharts';
import { SeriesDaily } from './home-vessel-chart-fuel-consumption-month.types';
import { colorMap } from './home-vessel-chart-fuel-consumption-month.constants';

export function getChartOptionsDaily(
  chartData: SeriesDaily,
  month: string,
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown'
): Highcharts.Options {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      type: 'column',
      name: 'Fuel (Tons)',
      data: chartData.yValues,
      yAxis: 0,
      color: colorMap.fuel,
    },
    {
      type: 'line',
      name: speedType + ' (kn)',
      data: chartData.speedValues,
      yAxis: 1,
      color: colorMap.speed,
      marker: { enabled: true },
    },
    {
      type: 'scatter',
      name: 'Beaufort',
      data: chartData.beaufortValues,
      yAxis: 2,
      color: colorMap.beaufort,
      enableMouseTracking: false,
    },
  ];

  return {
    chart: { zooming: { type: 'x' } },
    exporting: { enabled: false },
    credits: { enabled: false },
    title: { text: '' },
    xAxis: {
      title: { text: '' },
      crosshair: true,
      categories: chartData.dayLabels,
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        min: 0,
        title: { text: 'Fuel Consumption (Tons)', style: { color: colorMap.fuel } },
        labels: { style: { color: colorMap.fuel } },
      },
      {
        title: { text: speedType + ' (kn)', style: { color: colorMap.speed } },
        opposite: true,
        labels: { style: { color: colorMap.speed } },
      },
      {
        title: { text: 'Beaufort', style: { color: colorMap.beaufort } },
        opposite: true,
        labels: { style: { color: colorMap.beaufort } },
      },
    ],
    series,
    tooltip: {
      shared: true,
      useHTML: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        return formatTooltipDaily(this, chartData, month, speedType);
      },
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
  };
}

export function formatTooltipDaily(
  ctx: { x: number; points: { color: string; series: { name: string }; y: number; index: number }[] },
  chartData: SeriesDaily,
  month: string,
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown'
) {
  const index = ctx?.points?.[0]?.index ?? 0;
  const details = chartData.detailValues[index];

  let tooltip = `<div style="font-family: Arial, sans-serif; font-size: 13px; color: #222;">`;
  tooltip += `<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px;">${month} ${chartData.dayLabels[index]}</div>`;

  if (details && ctx.points?.[0]) {
    const consumption = ctx.points[0].y;

    tooltip += `<hr style="margin:8px 0; border-color: #ddd;" />`;
    tooltip += `<table style="font-size: 12px; color: #555; width: 100%;">`;
    tooltip += `<tbody>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Consumption:</td><td style="padding: 2px 4px;">${consumption} Tons</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Average ${speedType}:</td><td style="padding: 2px 4px;">${speedType === 'Log Speed' ? details.log_speed : speedType === 'GPS Speed' ? details.gps_speed : 'N/A'} kn</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Beaufort:</td><td style="padding: 2px 4px;">${details.bf ?? 'N/A'}</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Data Received:</td><td style="padding: 2px 4px;">${details.PctData ?? 'N/A'}%</td></tr>`;
    tooltip += `<tr><td style="padding: 2px 4px; font-weight: 600;">Time Sailed:</td><td style="padding: 2px 4px;">${details.HoursSailed ?? 'N/A'}</td></tr>`;
    tooltip += `</tbody>`;
    tooltip += `</table>`;
  }

  tooltip += `</div>`;

  return tooltip;
}
