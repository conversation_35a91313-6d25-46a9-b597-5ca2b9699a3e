'use client';

import React, { useRef, useEffect, useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';

export type GaugeChartProps = {
  title: string;
  value: number;
  min: number;
  max: number;
  unit: string;
  isPositiveGood?: boolean;
  className?: string;
  isLoading?: boolean;
};

export default function GaugeChart({ title, value, min, max, unit, isPositiveGood = true, className, isLoading = false }: GaugeChartProps) {
  const [modulesLoaded, setModulesLoaded] = useState(false);
  const chartRef = useRef<Highcharts.Chart | null>(null);

  // Update the chart when the value changes
  useEffect(() => {
    if (chartRef.current && modulesLoaded && !isLoading) {
      try {
        const series = chartRef.current.series[0];
        if (series && series.points && series.points[0]) {
          series.points[0].update(value);
        }
      } catch (error) {
        console.error(`Error updating chart:`, error);
      }
    }
  }, [value, modulesLoaded, isLoading]);

  useEffect(() => {
    // Initialize modules only on client side
    if (typeof window !== 'undefined') {
      // Dynamically import Highcharts modules
      const loadModules = async () => {
        try {
          // Import the modules dynamically
          // We need to use the dynamic import with type assertion
          const highchartsMore = (await import('highcharts/highcharts-more')) as typeof import('highcharts/highcharts-more');
          const highchartsSolidGauge = (await import('highcharts/modules/solid-gauge')) as typeof import('highcharts/modules/solid-gauge');

          // Apply the modules to Highcharts
          if (typeof highchartsMore === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (highchartsMore as any)(Highcharts);
          } else if (typeof highchartsMore.default === 'function') {
            highchartsMore.default(Highcharts);
          }

          if (typeof highchartsSolidGauge === 'function') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (highchartsSolidGauge as any)(Highcharts);
          } else if (typeof highchartsSolidGauge.default === 'function') {
            highchartsSolidGauge.default(Highcharts);
          }

          setModulesLoaded(true);
        } catch (error) {
          console.error('Failed to load Highcharts modules:', error);
        }
      };

      loadModules();
    }
  }, []);

  const options: Highcharts.Options = {
    chart: {
      type: 'solidgauge',
      backgroundColor: 'transparent',
      height: 170,
      marginBottom: 20,
    },
    title: {
      text: undefined,
    },
    pane: {
      center: ['50%', '80%'],
      size: '140%',
      startAngle: -90,
      endAngle: 90,
      background: [
        {
          backgroundColor: '#fafafa',
          borderRadius: 5,
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc',
          borderWidth: 0,
        },
      ],
    },
    exporting: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    tooltip: {
      enabled: false,
    },
    yAxis: {
      min,
      max,
      stops: isPositiveGood
        ? [
            [0.1, '#DF5353'],
            [0.5, '#DDDF0D'],
            [0.9, '#55BF3B'],
          ]
        : [
            [0.1, '#55BF3B'],
            [0.5, '#DDDF0D'],
            [0.9, '#DF5353'],
          ],
      lineWidth: 0,
      tickWidth: 0,
      minorTickInterval: undefined,
      tickAmount: 2,
      title: {
        y: -70,
        text: undefined,
      },
      labels: {
        y: 30,
        distance: -18,
        style: {
          fontSize: '12px',
        },
      },
    },
    plotOptions: {
      solidgauge: {
        dataLabels: {
          y: 5,
          borderWidth: 0,
          format: `<div style="text-align:center">
                    <span style="font-size:25px">{y:.2f}</span><br/>
                    <span style="font-size:12px;opacity:0.4">${unit}</span>
                  </div>`,
          useHTML: true,
        },
        rounded: true,
      },
    },
    series: [
      {
        name: title,
        data: [value],
        type: 'solidgauge',
        dataLabels: {
          format: `<div style="text-align:center">
                  <span style="font-size:25px">{y:.2f}</span><br/>
                  <span style="font-size:12px;opacity:0.4">${unit}</span>
                </div>`,
        },
        tooltip: {
          valueSuffix: ` ${unit}`,
        },
      },
    ],
  };

  return (
    <div className={`bg-card text-card-foreground rounded-xl border p-4 ${className}`}>
      {isLoading || !modulesLoaded ? (
        <div className='flex h-[170px] items-center justify-center'>
          <CircleSpinner size='md' variant='primary' />
        </div>
      ) : (
        <>
          <HighchartsReact
            highcharts={Highcharts}
            options={options}
            callback={(chart: Highcharts.Chart) => {
              chartRef.current = chart;
            }}
          />
          <div className='mt-2 text-center text-base font-medium'>{title}</div>
        </>
      )}
    </div>
  );
}
