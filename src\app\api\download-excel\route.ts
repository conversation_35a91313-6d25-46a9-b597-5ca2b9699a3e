import { httpClient, HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  vessel_id: z.string().nonempty('vessel_id must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
  from_date: z.string().nonempty('from_date must be provided'),
  to_date: z.string().nonempty('to_date must be provided'),
  vessel_name: z.string().nonempty('vessel_name must be provided'),
  sorted_type: z.string().optional(),
  exclude_anomalies: z.string().optional(),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/export/excel', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const response = await httpClient.get<{ excel: string } | string>(request, url.toString());

    // Check if the response is an error
    if (response === 'Error') {
      return NextResponse.json({ error: 'Failed to generate Excel file' }, { status: 500 });
    }

    // Extract the base64 excel data from the response
    const excelBase64 = typeof response === 'string' ? response : response.excel;

    if (!excelBase64) {
      return NextResponse.json({ error: 'No Excel data received' }, { status: 500 });
    }

    // Decode the base64 string to binary data
    const binaryData = Buffer.from(excelBase64, 'base64');

    // Return the response with appropriate headers for Excel download
    return new NextResponse(binaryData, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="data-export.xlsx"',
        'Content-Length': binaryData.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
