'use client';

import { useVoyageStore } from '../voyage.store';
import { Button } from '@/components/ui/button';
import { Trash } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import React from 'react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
export function VoyageDelete({ voyageId }: { voyageId: number }) {
  const { deleteVoyage } = useVoyageStore();
  const [isLoading, setIsLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      setOpen(false);
      await deleteVoyage(voyageId);
    } catch (error) {
      console.error('Error deleting voyage:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant='ghost' size='icon' disabled={isLoading}>
          {isLoading ? <CircleSpinner size='xs' variant='error' /> : <Trash className='h-4 w-4 text-red-500' />}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Voyage?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently remove the voyage from your records.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant={'outline'} onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button variant={'destructive'} onClick={handleDelete} disabled={isLoading}>
            {isLoading && <CircleSpinner size='xs' />}
            Delete
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
