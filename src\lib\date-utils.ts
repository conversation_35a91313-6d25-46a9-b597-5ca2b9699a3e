/**
 * Date Utilities for Server Formatting
 * ------------------------------------
 * This module provides helper functions to standardize date handling for server-side communication. It includes:
 *
 * - `formatToServerDateTime`: Converts a JavaScript `Date` to the server's expected string format (`'YYYY-MM-DD HH:MM:SS'`).
 * - `startOfDay`: Returns a new `Date` object representing the start of the day (00:00:00.000) for the given input.
 * - `endOfDay`: Returns a new `Date` object representing the end of the day (23:59:59.999) for the given input.
 */

export function formatToServerDateTime(date: Date | undefined): string {
  if (!date) return '';

  const pad = (n: number) => String(n).padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

export function startOfDay(date: Date | undefined): Date | undefined {
  if (!date) return undefined;

  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  return d;
}

export function endOfDay(date: Date | undefined): Date | undefined {
  if (!date) return undefined;

  const d = new Date(date);
  d.setHours(23, 59, 59, 999);
  return d;
}
