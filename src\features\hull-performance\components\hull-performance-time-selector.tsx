'use client';
import { DateRangePicker } from '@/components/ui-extensions/date-range-picker';
import React from 'react';
import { useHullPerformanceStore } from '../hull-performance.store';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

export function HullPerformanceTimeSelector() {
  const { setFromValue, setToValue, isLoading, fromValue, toValue, hasAnyChartData } = useHullPerformanceStore();

  const dateRange: DateRange | undefined = React.useMemo(() => {
    if (!fromValue || !toValue) return undefined;
    return {
      from: new Date(fromValue),
      to: new Date(toValue),
    };
  }, [fromValue, toValue]);

  const handleChange = (range: DateRange | undefined) => {
    if (range?.from) {
      setFromValue(format(range.from, 'yyyy-MM-dd'));
    } else {
      setFromValue('');
    }

    if (range?.to) {
      setToValue(format(range.to, 'yyyy-MM-dd'));
    } else {
      setToValue('');
    }
  };

  return <DateRangePicker value={dateRange} onChange={handleChange} disabled={isLoading || !hasAnyChartData()} />;
}
