import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { z } from 'zod';
import { HomeVesselResponse } from '@/features/home-vessel/home-vessel.types';

// === SCHEMA ===
const querySchema = z.object({
  from_date: z.string().nonempty('from_date is required'),
  to_date: z.string().nonempty('to_date is required'),
  owner_vat: z.string().nonempty('owner_vat is required'),
  vessel_id: z.string().nonempty('vessel_id is required'),
  vessel_imo: z.string().nonempty('vessel_imo is required'),
  vessel_name: z.string().nonempty('vessel_name is required'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/home', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<HomeVesselResponse>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
