'use client';

import * as React from 'react';
import { SortingState, useReactTable, getCoreRowModel, getSortedRowModel, flexRender } from '@tanstack/react-table';
import { useUserStore } from '../user.store';
import BouncingBarLoader from '@/components/ui-extensions/bouncing-bar-loader';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { columns } from './user-columns';
import { UserTablePagination } from './user-table-pagination';

export function UserTable() {
  const { filteredUsers, pageSize, pageIndex, loadUsers, isLoading } = useUserStore();

  const [sorting, setSorting] = React.useState<SortingState>([]);

  React.useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const table = useReactTable({
    data: filteredUsers,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    state: { sorting, pagination: { pageIndex, pageSize } },
  });

  const sortedRows = table.getSortedRowModel().rows;
  const paginatedData = React.useMemo(() => {
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    return sortedRows.slice(start, end);
  }, [sortedRows, pageIndex, pageSize]);

  return (
    <div className='flex flex-col gap-2'>
      <div className='relative rounded-md border'>
        {isLoading && <BouncingBarLoader absolute={true} className='z-1' />}
        <Table>
          <TableHeader className='bg-muted'>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className='text-muted-foreground'>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  {isLoading ? 'Loading...' : 'No results.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell colSpan={columns.length} className='text-muted-foreground text-sm'>
                Displayed users: {paginatedData.length} of {filteredUsers.length}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>

      <UserTablePagination />
    </div>
  );
}
