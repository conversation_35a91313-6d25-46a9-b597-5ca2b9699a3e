'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useDataAnalyticsStore } from '../data-analytics.store';

export const DataAnalyticsCardFuelConsumption = () => {
  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const data = dataAnalytics?.reports['fpfc'];

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Fuel Consumption</div>
      <div className='space-y-3'>
        <StatValue label='Frugal Propulsion On' value={data?.on_percent_fc ?? 'N/A'} units='%' isLoading={isLoading} />
        <StatValue label='Frugal Propulsion Off' value={data?.off_percent_fc ?? 'N/A'} units='%' isLoading={isLoading} />
        <StatValue label='Fuel Consumption (FP On)' value={data?.on_propulsion_fc ?? 'N/A'} units='t/nm' isLoading={isLoading} />
        <StatValue label='Fuel Consumption (FP Off)' value={data?.off_propulsion_fc ?? 'N/A'} units='t/nm' isLoading={isLoading} />
      </div>
    </div>
  );
};
