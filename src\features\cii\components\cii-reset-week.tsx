import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useCIIStore } from '../cii.store';
import { Button } from '@/components/ui/button';
import { RotateCcw } from 'lucide-react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import React from 'react';
export function CiiResetWeek({ week }: { week: number }) {
  const { resetWeeklyCIIData } = useCIIStore();
  const [isLoading, setIsLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);

  const handleReset = async () => {
    try {
      setIsLoading(true);
      await resetWeeklyCIIData(week);
      setOpen(false);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant='destructive' disabled={isLoading || !week}>
          {isLoading ? <CircleSpinner size='xs' variant='error' /> : <RotateCcw className='h-4 w-4' />}
          Reset
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Reset CII data for week {week}?</AlertDialogTitle>
          <AlertDialogDescription>This action will reset all CII data for the week: {week}.</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant='outline' onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button variant='destructive' onClick={handleReset} disabled={isLoading}>
            {isLoading && <CircleSpinner size='xs' />}
            Reset
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
