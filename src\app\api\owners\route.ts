import { OwnerDTO } from '@/features/owners/owner.types';
import { httpClient } from '@/lib/http-client';
import { HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Build URL
  const url = new URL('/owners', process.env.API_URL);

  // Make request
  try {
    const res = await httpClient.get<OwnerDTO[]>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
