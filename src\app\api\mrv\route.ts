import { httpClient } from '@/lib/http-client';
import { HttpError } from '@/lib/http-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { MrvGetDto } from '@/features/mrv/mrv.types';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo is required'),
  owner_vat: z.string().nonempty('owner_vat is required'),
  year: z.string().nonempty('year is required'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URL
  const url = new URL('/mrv', process.env.API_URL);
  url.search = new URLSearchParams(validatedQuery.data).toString();

  // Make request
  try {
    const res = await httpClient.get<MrvGetDto[]>(request, url.toString());
    return NextResponse.json(res);
  } catch (error) {
    if (error instanceof HttpError) {
      if (error.message.includes('No data found')) {
        const emptyMrvData: MrvGetDto = {
          mrv_data: {},
          mrv_data_copy: {},
          year: '',
        };
        return NextResponse.json(emptyMrvData);
      }
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
