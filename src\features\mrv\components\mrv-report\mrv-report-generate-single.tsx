'use client';

import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { Button } from '@/components/ui/button';
import { FileDown } from 'lucide-react';
import React from 'react';
import { toast } from 'sonner';
import { useVesselStore } from '@/features/vessels/vessel.store';
import axios from 'axios';
import { useMrvTableStore } from '../mrv-table/mrv-table.store';
import { useMrvStore } from '../../mrv.store';

export function MrvReportGenerateSingle({ voyageId }: { voyageId: string }) {
  const [isLoading, setIsLoading] = React.useState(false);
  const { year } = useMrvTableStore();
  const { voyages } = useMrvStore();

  const handleClick = async () => {
    setIsLoading(true);

    try {
      // Find the voyage with the given ID to get its UUID
      const voyage = voyages[voyageId];
      const currentVessel = useVesselStore.getState().currentVessel;

      // Vessel, voyage, and year check
      if (!voyage || !year || !currentVessel) {
        toast.error('No voyage, year, or vessel selected');
        return;
      }

      // Generate the report URL with query parameters
      const requestUrl = new URL('/api/mrv/voyage-report', window.location.origin);
      requestUrl.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        vessel_name: currentVessel.vessel_name.toString(),
        voyage_id: voyage.voyage_id.toString(),
        year: year.toString(),
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      // Fetch the PDF file
      const response = await axios.get(requestUrl.toString(), {
        responseType: 'blob',
      });

      // Response check
      if (response.status !== 200) {
        toast.error('Failed to generate report');
        return;
      }

      // Create a link to download the file
      const blob = response.data;
      const downloadUrl = window.URL.createObjectURL(blob);
      const fileName = `MRV-${year}-${currentVessel.vessel_name}-${voyage.departure_port}-${voyage.destination_port}-${voyage.start_time}.pdf`;

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      // Success toast
      toast.success('Report download successful');
    } catch (error: unknown) {
      // Error toast
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('An unexpected error occurred');
      }
    } finally {
      // Reset loading state
      setIsLoading(false);
    }
  };

  return (
    <Button variant='ghost' size='icon' onClick={handleClick} disabled={isLoading}>
      {isLoading ? <CircleSpinner variant='primary' size='xs' /> : <FileDown className='h-4 w-4 text-blue-500' />}
    </Button>
  );
}
