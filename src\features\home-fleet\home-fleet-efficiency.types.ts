export interface FleetEfficiencyPostDTO {
  request: {
    vessel_id: number;
    vessel_imo: number;
    vessel_name: string;
    selected_owner_vat: string;
    from_date: string;
    to_date: string;
  };
  filters: {
    vessels: {
      id: number;
      imo: number;
      name: string;
      assigned_owner_vat: string;
    }[];
    type: 'fleet';
    fpOn: boolean;
    fpOff: boolean;
    loadCondition: string;
    beaufort: number;
  };
  owner_vat: string;
}

export type FleetEfficiencyGetDTO = Array<{
  [key: string]: {
    fuel: {
      efficiency: number;
      hours_sailed: string;
      speed: number;
    };
    propulsion: {
      efficiency: number;
      hours_sailed: string;
      speed: number;
    };
  };
}>;
