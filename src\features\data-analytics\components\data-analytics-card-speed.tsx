'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useDataAnalyticsStore } from '../data-analytics.store';

export const DataAnalyticsCardSpeed = () => {
  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const data = dataAnalytics?.reports['fpfc'];

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 text-lg font-semibold tracking-tight'>Average Speed</div>
      <div className='space-y-3'>
        <StatValue label='Avg. Speed (On)' value={data?.avg_speed_on ?? 'N/A'} units='kn' isLoading={isLoading} />
        <StatValue label='Avg. Speed (Off)' value={data?.avg_speed_off ?? 'N/A'} units='kn' isLoading={isLoading} />
      </div>
    </div>
  );
};
