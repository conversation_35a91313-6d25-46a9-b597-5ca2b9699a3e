export type VesselDTO = {
  id: number;
  vessel_name: string;
  assigned_owner_vat: string;
  available_dates: { timestamp: string }[];
  imo: number;
  ais_data: {
    id: number;
    vessel: number;
    timestamp: string;
    last_update_utc: string;
    navigation_status: string;
    latitude: number;
    longitude: number;
    vessel_type: string;
    destination: string;
    dest_port_unlo: string;
    dest_port_name: string;
    dep_port_unlo: string;
    dep_port_name: string;
    atd_epoch: number;
    atd_utc: string;
    eta_epoch: number;
    eta_utc: string;
    draught: string;
    heading: number;
    speed: number;
  }[];
  latest_row: {
    vessel: number;
    timestamp: string;
    log_speed: number;
    gps_speed: number;
    me_1_rpm: number;
    shaft_1_rpm: number;
    me_1_fc_mass: number;
    shaft_1_power: number;
    aux_1_fc_mass: number;
    aux_2_fc_mass: number;
    nav_status: string;
    frugal_status: number;
    destination: string;
    eta: string;
    prop_1_pitch: number;
    shaft_1_torque: number;
    shaft_1_thrust: number;
    me_1_load: number;
    me_1_fc_volume: number;
    aux_1_fc_volume: number;
    aux_2_fc_volume: number;
    boiler_1_fc_mass: number;
    boiler_2_fc_mass: number;
    boiler_1_fc_volume: number;
    boiler_2_fc_volume: number;
    flow_totals: {
      me_1_total_1: number;
      me_1_total_2: number;
      me_1_total_3: number;
    };
    consumption: {
      fuel_density: number;
      me_1_fc_mass: number;
      me_1_0_1_mass: number;
      me_1_fc_volume: number;
      me_1_0_1_volume: number;
      fuel_temperature: number;
      me_1_0_1_density: number;
      me_1_0_1_total_1: number;
      me_1_0_1_total_2: number;
      me_1_0_1_total_3: number;
      me_1_0_1_pressure: number;
      me_1_0_1_temperature: number;
    };
    fuel_density: number;
    fuel_temperature: number;
    fuel_type: null;
    frugal_mode: number;
    frugal_alarms: number;
    draft_fwd: number;
    draft_aft: number;
    latitude: number;
    longitude: number;
    heading: number;
    accel_x: number;
    accel_y: number;
    accel_z: number;
    gyro_x: number;
    gyro_y: number;
    gyro_z: number;
    thrust_cmd: number;
    rpm_cmd: number;
    pitch_cmd: number;
    sailing_check: boolean;
    sailing_status: number;
  }[];
};
