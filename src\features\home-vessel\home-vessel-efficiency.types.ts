export interface VesselEfficiencyPostDTO {
  request: {
    vessel_id: number;
    vessel_imo: number;
    vessel_name: string;
    selected_owner_vat: string;
    from_date: string;
    to_date: string;
  };
  filters: {
    type: 'vessel';
    fpOn: boolean;
    fpOff: boolean;
    loadCondition: string;
    beaufort: number;
  };
  owner_vat: string;
}

export interface VesselEfficiencyGetDTO {
  reports: {
    efficiencies_daily: {
      fuel_efficiency: Array<{
        [key: string]: {
          [key: string]: string | number;
          speed: number;
          time: string;
        };
      }>;
      propulsion_efficiency: Array<{
        [key: string]: {
          [key: string]: string | number;
          speed: number;
          time: string;
        };
      }>;
      speed_type: string;
    };
    efficiencies_monthly: {
      fuel_efficiency: Array<{
        [key: string]: {
          [key: string]: string | number;
          speed: number;
          time: string;
        };
      }>;
      propulsion_efficiency: Array<{
        [key: string]: {
          [key: string]: string | number;
          speed: number;
          time: string;
        };
      }>;
      speed_type: string;
    };
    speed_levels_log: {
      speed_levels_log_30: {
        speed_levels: {
          [key: string]: {
            me_fc: number;
            shaft_power: number;
            time_fc: string;
            time_power: string;
          };
        };
        speed_type: string;
      };
    };
  };
}
