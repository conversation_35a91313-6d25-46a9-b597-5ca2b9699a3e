import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';
import { UserRole } from '@/features/users/user.types';
import { authConfig, ExtendedSession } from './auth';

export async function ensureMinRole(minRole: UserRole): Promise<[NextResponse | null, ExtendedSession | null]> {
  const session = await getServerSession(authConfig);
  const extendedSession = session as ExtendedSession;

  if (!extendedSession?.current_owner) {
    return [NextResponse.json({ error: 'Unauthorized' }, { status: 401 }), null];
  }

  if (extendedSession.current_owner.role > minRole) {
    return [NextResponse.json({ error: 'Forbidden' }, { status: 403 }), extendedSession];
  }

  return [null, extendedSession];
}
