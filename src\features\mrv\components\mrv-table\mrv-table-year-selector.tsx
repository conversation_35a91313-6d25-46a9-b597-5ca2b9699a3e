'use client';

import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';

export function MrvTableYearSelector() {
  // Store & state
  const { year, setYear, availableYears, isLoading } = useMrvTableStore();

  // Handle year change
  const handleYearChange = (value: string) => {
    setYear(Number(value));
  };

  return (
    <Select value={year ? year.toString() : ''} onValueChange={handleYearChange} disabled={isLoading}>
      <SelectTrigger>
        <SelectValue placeholder='Select a year' />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Years</SelectLabel>
          {availableYears.length === 0 ? (
            <SelectItem disabled value='0'>
              No years available
            </SelectItem>
          ) : (
            availableYears.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))
          )}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
