'use client';

import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import React from 'react';
import { useCIIStore } from '../cii.store';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { CiiChartWeekEditRef } from './cii-chart-week-edit';

let isSyncing = false;

const COLORS: Record<string, string> = {
  blue: 'oklch(62.3% 0.214 259.815)',
  yellow: 'oklch(79.5% 0.184 86.047)',
};

function createSharedPointEvents(linkedRef: React.RefObject<Highcharts.Chart>) {
  return {
    mouseOver(this: Highcharts.Point) {
      const category = this.category;
      if (!linkedRef?.current) return;

      const pointsToHighlight: Highcharts.Point[] = [];
      linkedRef.current.series.forEach((series) => {
        const match = series.points.find((p) => p.category === category);
        if (match) {
          match.setState('hover');
          pointsToHighlight.push(match);
        }
      });

      if (pointsToHighlight.length) linkedRef.current.tooltip.refresh(pointsToHighlight);
    },
    mouseOut() {
      if (!linkedRef?.current) return;

      linkedRef.current.series.forEach((series) => {
        series.points.forEach((point) => point.setState(''));
      });
      linkedRef.current.tooltip.hide();
    },
  };
}

export default function CiiChartSpeedFuel({
  chartRef,
  linkedRef,
  weekEditRef,
}: {
  chartRef: React.RefObject<Highcharts.Chart>;
  linkedRef: React.RefObject<Highcharts.Chart>;
  weekEditRef: React.RefObject<CiiChartWeekEditRef>;
}) {
  const { weeklyDataMerged, yearData } = useCIIStore();

  const chartOptions = React.useMemo(() => {
    const weeks = weeklyDataMerged ? Object.keys(weeklyDataMerged).map(Number) : [];
    const mdoValues = weeks.map((week) => weeklyDataMerged[week]?.fuel_consumption?.MDO ?? 0);
    const averageSpeeds = weeks.map((week) => weeklyDataMerged[week]?.avg_speed ?? 0);

    return {
      chart: { zooming: { type: 'x' } },
      title: { text: null },
      credits: { enabled: false },
      exporting: { enabled: false },
      tooltip: {
        shared: true,
        useHTML: true,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formatter: function (this: any) {
          const week = Number(this.point.category);
          const data = weeklyDataMerged?.[week];

          let tooltipHtml = `<b>Week: ${week}</b>`;
          tooltipHtml += `<br/>Avg Speed: <b>${data?.avg_speed.toFixed(2)} knots</b>`;
          tooltipHtml += `<br/>Emissions: <b>${data?.emissions.toFixed(2)} mt</b>`;

          tooltipHtml += `<br/>`;
          if (data?.fuel_consumption) {
            tooltipHtml += '<br/><u>Fuel Consumption:</u>';
            const total = Object.values(data.fuel_consumption).reduce((sum, value) => sum + value, 0);
            Object.entries(data.fuel_consumption).forEach(([type, value]) => {
              tooltipHtml += `<br/>${type}: ${value.toFixed(2)} mt`;
            });
            tooltipHtml += `<br/><b>Total: ${total.toFixed(2)} mt</b>`;
          }

          return tooltipHtml;
        },
      },
      xAxis: [
        {
          categories: weeks.map(String),
          crosshair: true,
          title: { text: 'Week Number' },
          events: {
            setExtremes(e: Highcharts.AxisSetExtremesEventObject) {
              if (isSyncing) return;
              if (linkedRef?.current) {
                isSyncing = true;
                linkedRef.current.xAxis[0].setExtremes(e.min, e.max, true, false);
                isSyncing = false;
              }
            },
          },
        },
      ],
      yAxis: [
        {
          labels: { format: '{value}', style: { color: COLORS.blue } },
          title: { text: 'Average Speed (kn)', style: { color: COLORS.blue } },
          opposite: false,
        },
        {
          gridLineWidth: 0,
          labels: { format: '{value}', style: { color: COLORS.yellow } },
          title: { text: 'Fuel Consumption (mt)', style: { color: COLORS.yellow } },
          opposite: true,
        },
      ],
      series: [
        {
          name: 'Fuel Consumption (mt)',
          type: 'spline',
          yAxis: 1,
          data: mdoValues,
          marker: { enabled: true, symbol: 'circle' },
          color: COLORS.yellow,
          tooltip: { valueSuffix: ' mt', valueDecimals: 2 },
        },
        {
          name: 'Average Speed (kn)',
          type: 'spline',
          yAxis: 0,
          data: averageSpeeds,
          marker: { enabled: true, symbol: 'circle' },
          color: COLORS.blue,
          tooltip: { valueSuffix: ' kn', valueDecimals: 2 },
        },
      ],
      plotOptions: {
        series: {
          point: {
            events: {
              click: function (this: Highcharts.Point) {
                const week = Number(this.category);
                const data = weeklyDataMerged?.[week];

                if (!data) return;

                const payload = {
                  week_numb: week,
                  distance: data.distance,
                  fuel: Object.entries(data.fuel_consumption || {}).map(([type, value]) => ({
                    fuelType: type,
                    fuelConsumption: value,
                  })),
                };

                weekEditRef?.current?.open(payload);
              },
              ...createSharedPointEvents(linkedRef),
            },
          },
        },
      },
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 768,
            },
            chartOptions: {
              chart: {
                height: 300,
              },
            },
          },
          {
            condition: {
              maxWidth: 480,
            },
            chartOptions: {
              chart: {
                height: 220,
              },
            },
          },
        ],
      },
    };
  }, [weeklyDataMerged, yearData, linkedRef, weekEditRef]);

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Speed and Fuel Consumption</div>
      </div>

      {weeklyDataMerged ? (
        <HighchartsReact
          highcharts={Highcharts}
          options={chartOptions}
          callback={(chart: Highcharts.Chart) => (chartRef.current = chart)}
        />
      ) : (
        <div className='flex h-[220px] justify-center pt-8 md:h-[300px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      )}
    </div>
  );
}
