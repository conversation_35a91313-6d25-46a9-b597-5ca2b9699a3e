'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
export function LoginForm({}: React.ComponentProps<'div'>) {
  return (
    <div className={cn('flex flex-col gap-6')}>
      <Image src='/svg/logo-frugal-full.svg' alt='Logo' width={0} height={0} className='w-full' />
      <div className='after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t'>
        <span className='bg-background text-muted-foreground relative z-10 px-2'>Continue with</span>
      </div>
      <div className='grid gap-4'>
        <Button variant='outline' type='button' className='w-full cursor-pointer'>
          <Image src='/svg/microsoft.svg' alt='Logo' width={24} height={24} />
          Continue with Microsoft
        </Button>
        <Button
          variant='outline'
          type='button'
          className='w-full cursor-pointer'
          onClick={() => signIn('google', { callbackUrl: '/dashboard' })}
        >
          <Image src='/svg/google.svg' alt='Logo' width={24} height={24} />
          Continue with Google
        </Button>
      </div>
      <div className='text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4'>
        By clicking continue, you agree to our <a href='#'>Terms of Service</a> and <a href='#'>Privacy Policy</a>.
      </div>
    </div>
  );
}
