// DATA RAW
export type DataRaw = {
  [key: string]: UniqueGroup | DataRawAll | DataRawSearch | DataRawSpeedTrendLines;
  all: DataRawAll;
  search: DataRawSearch;
  speed_trendlines: DataRawSpeedTrendLines;
};

export type DataSearch = {
  [key: string]: UniqueGroupSearch | DataRawAll | GagesData | DataRawSpeedTrendLines;
  all: DataRawAll;
  gages: GagesData;
  speed_trendlines: DataRawSpeedTrendLines;
};

export type GagesData = {
  average_daily_rate_of_change: number;
  average_fuel_changes: number;
  average_shaft_power_changes: number;
  average_shaft_power_changes_percentage: number;
};

export type DataRawAll = {
  all_shaft_power: number[];
  all_timestamps: string[];
  trend_line: number[];
};

export type DataRawSearch = {
  benchmark_shaft: { [timestamp: string]: number };
  distance_from_benchmark_shaft: { [timestamp: string]: number };
  me_1_rpm: { [timestamp: string]: number };
  new_fuel_consumption_difference: { [timestamp: string]: number };
  prop_1_pitch: { [timestamp: string]: number };
  real_occurrence_count: { [timestamp: string]: number };
  shaft_1_power: { [timestamp: string]: number };
  speed_draft: { [timestamp: string]: string };
  unique_combination: { [timestamp: string]: string };
};

export type DataRawSpeedTrendLines = {
  [key: string]: DataRawSpeedTrendLinesObj;
};

export type DataRawSpeedTrendLinesObj = {
  combined_resampled_shaft_power: number[];
  combined_resampled_timestamps: string[];
  trend_line: number[];
};

export type UniqueGroup = {
  best_value: number;
  best_value_time: string;
  current_value: number;
  current_value_time: string;
  daily_rate_of_shaft_change: number;
  draft: number;
  overconsumption_fuel: number;
  resampled_shaft_power: number[];
  resampled_timestamps: string[];
  shaft_diff: number;
  shaft_diff_percentage: number;
  shaft_power: number;
  speed: number;
};

export type UniqueGroupSearch = {
  best_value: number;
  best_value_time: string;
  current_value: number;
  current_value_time: string;
  daily_rate_of_shaft_change: number;
  draft: number;
  overconsumption_fuel: number;
  pitch: number;
  resampled_shaft_power: number[];
  resampled_timestamps: string[];
  rpm: number;
  shaft_diff: number;
  shaft_diff_percentage: number;
  shaft_power: number;
  speed: number;
};

// DATA AI
export type DataAi = {
  all: DataAiObj;
};

export type DataAiObj = {
  timestamps: string[];
  real_shaft_powers: number[];
  predicted_shaft_powers: number[];
  upper_bounds: number[];
  lower_bounds: number[];
  average_shaft_power_changes_percentage: number;
  average_shaft_power_changes: number;
  average_fuel_changes: number;
  average_daily_rate_of_change: number;
  model_train_from: string;
  model_train_to: string;
  error_rate: number;
  fuel_consumptions: number[];
};
