import { create } from 'zustand';
import { VesselDTO } from './vessel.types';
import axios from 'axios';
import { toast } from 'sonner';

interface VesselStore {
  vessels: VesselDTO[];
  currentVessel: VesselDTO | null;
  isLoading: boolean;

  setCurrentVessel: (vessel: VesselDTO) => void;
  loadVessels: () => Promise<void>;
}

export const useVesselStore = create<VesselStore>((set) => ({
  vessels: [],
  currentVessel: null,
  isLoading: false,

  setCurrentVessel: (vessel: VesselDTO) => {
    localStorage.setItem('currentVesselId', vessel.id.toString());
    set({ currentVessel: vessel });
  },

  loadVessels: async () => {
    set({ isLoading: true });
    try {
      const { data } = await axios.get<VesselDTO[]>('/api/vessels');
      data.sort((a, b) => a.vessel_name.localeCompare(b.vessel_name));
      set({ vessels: data });

      const currentVesselId = localStorage.getItem('currentVesselId');
      const found = data.find((vessel) => vessel.id === Number(currentVesselId));
      localStorage.setItem('currentVesselId', found?.id.toString() ?? data[0].id.toString());
      set({ currentVessel: found ?? data[0] });
    } catch (_error) {
      toast.error('Error loading vessels');
    } finally {
      set({ isLoading: false });
    }
  },
}));
