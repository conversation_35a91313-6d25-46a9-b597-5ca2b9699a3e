import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { ShipIcon, TrendingUpIcon, FuelIcon, WifiOffIcon } from 'lucide-react';

export default function FleetSummaryCards() {
  return (
    <div className='mb-4 grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
      {/* Total Vessels Reporting */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Active Vessels</div>
          <Tooltip>
            <TooltipTrigger>
              <ShipIcon className='h-4 w-4 text-green-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Number of vessels currently reporting data</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>24 / 30</div>
          <p className='text-muted-foreground text-xs'>6 offline</p>
        </div>
      </div>

      {/* Avg Fuel Efficiency */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Avg Fuel Efficiency</div>
          <Tooltip>
            <TooltipTrigger>
              <FuelIcon className='h-4 w-4 text-blue-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Average fuel consumption per knot across fleet</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>0.82 t/nm</div>
          <p className='text-xs text-green-500'>-3.2% this week</p>
        </div>
      </div>

      {/* Data Connectivity */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Data Connectivity</div>
          <Tooltip>
            <TooltipTrigger>
              <WifiOffIcon className='h-4 w-4 text-amber-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Vessels not currently transmitting</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>4 offline</div>
          <p className='text-muted-foreground text-xs'>Last sync: 12:05 UTC</p>
        </div>
      </div>

      {/* Performance Index */}
      <div className='bg-card text-card-foreground rounded-xl border'>
        <div className='flex items-center justify-between space-y-0 p-4 pb-2'>
          <div className='text-sm font-medium tracking-tight'>Fleet Performance Index</div>
          <Tooltip>
            <TooltipTrigger>
              <TrendingUpIcon className='h-4 w-4 text-purple-500' />
            </TooltipTrigger>
            <TooltipContent>
              <p>Combined operational score based on fuel, speed & engine health</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className='p-4 pt-0'>
          <div className='text-2xl font-bold'>82.7%</div>
          <p className='text-xs text-green-500'>+4.1% improvement</p>
        </div>
      </div>
    </div>
  );
}
