'use client';

import { useRef } from 'react';
import Highcharts from 'highcharts';
import DataAnalyticsChartConsumption from './data-analytics-chart-consumption/data-analytics-chart-consumption';
import DataAnalyticsChartPerformance from './data-analytics-chart-performance/data-analytics-chart-performance';
import DataAnalyticsChartSpeed from './data-analytics-chart-speed/data-analytics-chart-speed';
import DataAnalyticsChartTimeline from './data-analytics-chart-timeline/data-analytics-chart-timeline';

export function DataAnalyticsCharts() {
  const chartRefs = {
    speed: useRef<Highcharts.Chart | null>(null),
    performance: useRef<Highcharts.Chart | null>(null),
    consumption: useRef<Highcharts.Chart | null>(null),
    timeline: useRef<Highcharts.Chart | null>(null),
  };

  return (
    <div className='grid grid-cols-1 gap-4'>
      <DataAnalyticsChartSpeed chartRef={chartRefs.speed} allChartRefs={chartRefs} chartId='speed' />
      <DataAnalyticsChartPerformance chartRef={chartRefs.performance} allChartRefs={chartRefs} chartId='performance' />
      <DataAnalyticsChartConsumption chartRef={chartRefs.consumption} allChartRefs={chartRefs} chartId='consumption' />
      <DataAnalyticsChartTimeline chartRef={chartRefs.timeline} allChartRefs={chartRefs} chartId='timeline' />
    </div>
  );
}
