'use client';

import * as React from 'react';
import { SortingState, useReactTable, getCoreRowModel, getSortedRowModel, flexRender } from '@tanstack/react-table';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { columns } from '@/features/mrv/components/mrv-table/mrv-table-columns';
import { MrvTablePaginator } from '@/features/mrv/components/mrv-table/mrv-table-paginator';
import BouncingBarLoader from '@/components/ui-extensions/bouncing-bar-loader';
import { useMrvStore } from '@/features/mrv/mrv.store';
import { useFilteredVoyages } from '@/features/mrv/components/mrv-table/hooks/use-filtered-voyages';
import { useMrvTableStore } from '@/features/mrv/components/mrv-table/mrv-table.store';

export function MrvTable() {
  // Store
  const { isLoading } = useMrvStore();
  const { pageSize, pageIndex } = useMrvTableStore();

  // State
  const voyages = useFilteredVoyages();

  // Table
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const table = useReactTable({
    data: voyages,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    state: { sorting, pagination: { pageIndex, pageSize } },
  });

  // Sorted Rows
  const sortedRows = table.getSortedRowModel().rows;

  // Paginated Data
  const paginatedData = React.useMemo(() => {
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    return sortedRows.slice(start, end);
  }, [sortedRows, pageIndex, pageSize]);

  return (
    <div className='flex flex-col gap-2'>
      <div className='relative rounded-md border'>
        {isLoading && <BouncingBarLoader absolute={true} className='z-1' />}
        <Table>
          <TableHeader className='bg-muted'>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className='text-muted-foreground'>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row) => (
                <TableRow key={row.original.voyage_id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  {isLoading ? 'Loading...' : 'No results.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell colSpan={columns.length} className='text-muted-foreground text-sm'>
                Displayed voyages: {paginatedData.length} of {voyages.length}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>

      <MrvTablePaginator />
    </div>
  );
}
