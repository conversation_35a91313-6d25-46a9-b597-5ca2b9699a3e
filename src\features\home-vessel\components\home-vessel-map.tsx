'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { LatLngExpression, divIcon } from 'leaflet';
import 'leaflet/dist/leaflet.css';

import { useHomeVesselStore } from '../home-vessel.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';

export default function HomeVesselMap() {
  const { vesselData, isLoadingVesselData } = useHomeVesselStore();
  const { currentVessel } = useVesselStore();

  if (!vesselData || isLoadingVesselData) {
    return (
      <div className='bg-card text-card-foreground flex h-[360px] w-full items-center justify-center rounded-xl border'>
        <CircleSpinner variant='primary' />
      </div>
    );
  }

  const ais = vesselData?.latest_ais_data;
  const frugal = vesselData?.latest_row?.[0];

  const isAisNewer = ais?.timestamp && frugal?.timestamp ? new Date(ais.timestamp) > new Date(frugal.timestamp) : true;

  const latestData = isAisNewer ? ais : frugal;
  const position: LatLngExpression = [latestData?.latitude ?? 0, latestData?.longitude ?? 0];
  const heading = latestData?.heading ?? 0;

  const updatedFrom = isAisNewer ? 'AIS' : 'Frugal Systems';
  const navStatus = isAisNewer ? ais?.navigation_status : (frugal?.nav_status ?? 'unknown');
  const etaRaw = isAisNewer ? ais?.eta_utc : (frugal?.eta ?? '');
  const eta = etaRaw?.replace('T', ' ').slice(0, 19) || 'unknown';
  const timestamp = latestData?.timestamp?.replace('T', ' ').slice(0, 19) ?? 'unknown';

  const shipIcon = divIcon({
    className: '',
    html: `
      <div style="position: relative; width: 20px; height: 20px;">
        <div style="transform: rotate(${heading}deg); width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
          <img src="/map/pointer.svg" style="width: 12px; height: 20px;" />
        </div>
        <div style="position: absolute; top: 80%; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 11px; background: white; padding: 1px 4px; border-radius: 3px; margin-top: 2px; box-shadow: 0 0 2px rgba(0, 0, 0, 0.2); pointer-events: none;">
          ${currentVessel?.vessel_name ?? ''}
        </div>
      </div>
    `,
    iconSize: [20, 20],
    iconAnchor: [10, 10],
  });

  return (
    <div className='bg-card text-card-foreground relative overflow-hidden rounded-xl border'>
      <div className='absolute top-2 right-2 z-[1000] rounded border bg-white px-2 py-1 text-xs text-green-500'>
        Last update ({updatedFrom}): {timestamp}
      </div>
      <MapContainer center={position} zoom={5} scrollWheelZoom style={{ height: '360px', width: '100%' }}>
        <TileLayer
          url='https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png'
          attribution='&copy; <a href="https://carto.com/">CARTO</a>'
        />
        <Marker position={position} icon={shipIcon}>
          <Popup>
            <div className='flex flex-col gap-1 text-sm'>
              <PopupRow label='Position' value={`[${position[0]}, ${position[1]}]`} />
              <PopupRow label='IMO' value={currentVessel?.imo} />
              <PopupRow label='Frugal Status' value={vesselData?.summaryData?.status} />
              <PopupRow label='Navigation Status' value={navStatus} />
              <PopupRow label='Destination' value={ais?.dest_port_name} />
              <PopupRow label='ETA' value={eta} />
            </div>
          </Popup>
        </Marker>
      </MapContainer>
    </div>
  );
}

function PopupRow({ label, value }: { label: string; value?: string | number }) {
  return (
    <div className='flex justify-between gap-2'>
      <span className='font-medium'>{label}:</span>
      <span>{value ?? '—'}</span>
    </div>
  );
}
