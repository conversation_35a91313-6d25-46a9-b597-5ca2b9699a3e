'use client';

// Define consistent colors matching the CII charts and Tailwind colors
export const COLORS = {
  // Keep original colors for consistency with other charts
  blue: 'oklch(62.3% 0.214 259.815)',
  yellow: 'oklch(79.5% 0.184 86.047)',
  // Use Tailwind CSS variables for draft series
  draft1: 'var(--chart-1)',
  draft2: 'var(--chart-2)',
  draft3: 'var(--chart-3)',
  draft4: 'var(--chart-4)',
  draft5: 'var(--chart-5)',
};

// Function to format timestamps for display
export const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.getTime(); // Return timestamp as milliseconds for Highcharts
};
