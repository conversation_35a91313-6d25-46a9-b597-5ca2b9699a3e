'use client';

import { StatValue } from '@/components/ui-extensions/stat-value';
import { useHomeFleetStore } from '../home-fleet.store';

export const HomeFleetSummaryEmissions = () => {
  const { fleetData, isLoadingFleetData } = useHomeFleetStore();

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <h2 className='text-md mb-4 font-semibold tracking-tight'>Emissions Summary</h2>
      <div className='space-y-3'>
        <StatValue
          label='Main Engine'
          value={fleetData?.fleet_fc_yearly_overview.co2_emissions.me ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Auxiliary Engines'
          value={fleetData?.fleet_fc_yearly_overview.co2_emissions.aux ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Boilers'
          value={fleetData?.fleet_fc_yearly_overview.co2_emissions.boiler ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
        <StatValue
          label='Total'
          value={fleetData?.fleet_fc_yearly_overview.co2_emissions.total ?? 'N/A'}
          units='t'
          isLoading={isLoadingFleetData}
        />
      </div>
    </div>
  );
};
