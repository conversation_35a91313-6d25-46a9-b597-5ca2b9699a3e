'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { UserRole } from '../user.types';
import { useUserStore } from '../user.store';
import { Plus } from 'lucide-react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';

export function UserAdd() {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { createUser } = useUserStore();

  const form = useForm<{ email: string; role: UserRole }>({
    defaultValues: {
      email: '',
      role: UserRole.Basic,
    },
  });

  const onSubmit = async (data: { email: string; role: UserRole }) => {
    setIsLoading(true);
    try {
      await createUser(data.email, data.role);
      setOpen(false);
      form.reset();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant='outline'>
          <Plus />
        </Button>
      </PopoverTrigger>

      <PopoverContent className='w-80 space-y-4'>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='email'>Email</Label>
            <Input id='email' type='email' {...form.register('email', { required: true })} placeholder='<EMAIL>' />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='role'>Role</Label>
            <Select value={form.watch('role').toString()} onValueChange={(value) => form.setValue('role', Number(value) as UserRole)}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Select Role' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole['Basic'].toString()}>Basic</SelectItem>
                <SelectItem value={UserRole['Admin'].toString()}>Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className='flex justify-end gap-2'>
            <Button type='button' variant='outline' onClick={() => setOpen(false)}>
              Close
            </Button>
            <Button type='submit' disabled={isLoading || !form.formState.isValid}>
              {isLoading && <CircleSpinner size='xs' />}
              Invite
            </Button>
          </div>
        </form>
      </PopoverContent>
    </Popover>
  );
}
