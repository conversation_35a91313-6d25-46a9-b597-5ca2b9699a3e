'use client';

import { Input } from '@/components/ui/input';
import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import { Control, FieldValues, Path } from 'react-hook-form';

interface MrvVoyageFieldDistanceProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
}

export function MrvVoyageFieldDistance<T extends FieldValues>({ control, name, label }: MrvVoyageFieldDistanceProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      rules={{ required: 'This field is required' }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className='relative'>
              <Input
                type='number'
                {...field}
                value={field.value === 0 ? '' : (field.value ?? '')}
                onChange={(e) => {
                  const value = e.target.value === '' ? 0 : Number(e.target.value);
                  field.onChange(value);
                }}
                className='pr-12'
              />
              <div className='text-muted-foreground pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3'>nm</div>
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
