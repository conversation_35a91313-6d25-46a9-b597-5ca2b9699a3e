// ===== MAIN ENGINE HEALTH RESPONSE =====
export interface MainEngineHealthResponse {
  hasAlarmData: boolean;
  notEnoughData: boolean;
  radarChartCylinder: RadarChartCylinder;
  cylinderLineChart: Cylinder<PERSON>ineChart;
  heatmapCylinder: HeatmapCylinder;
  heatmapCylinderDeviation: HeatmapCylinder;
  heatmapRanges: HeatmapRanges;
  mainChart: MainChart;
  overconsumptionEngineChart: OverconsumptionEngineChart;
  daysDiff: number;
  endTime: string;
  mainEngineConsistency: number;
  mainEngineHealthScore: number;
  mainEngineHealthScoreMax: number;
  mainEngineHealthScoreMin: number;
  mainEngineHealthVolatility: number;
  startTime: string;
}

// ===== RADAR CHART CYLINDER =====
export interface RadarChartCylinder {
  all: Record<string, number>;
}

// ===== CYLINDER LINE CHART =====
export interface CylinderLineChart {
  year_month: Record<string, CylinderLineChartData>;
}

export interface CylinderLineChartData {
  cylinder: Record<string, number[]>;
  timestamp_strings: string[];
}

// ===== HEATMAP CYLINDER + DEVIATION =====
export interface HeatmapCylinder {
  cylinder: Record<string, number[]>;
  month_names: string[];
}

// ===== HEATMAP RANGES =====
export interface HeatmapRanges {
  deviation_ranges: Range[];
  temperature_ranges: Range[];
}

export interface Range {
  color: string;
  from: number;
  name: string;
  to: number;
}

// ===== MAIN CHART =====
export interface MainChart {
  all: MainChartData;
  forecast: ForecastData;
}

export interface MainChartData {
  bsfc: number[];
  end_date: string;
  error_lower: number[];
  error_upper: number[];
  predicted_bsfc: number[];
  start_date: string;
  timestamp: string[];
}

export interface ForecastData {
  forecast_lower: number[];
  forecast_upper: number[];
  timestamp: string[];
}

// ===== OVERCONSUMPTION ENGINE CHART =====
export interface OverconsumptionEngineChart {
  speed_draft: Record<string, OverconsumptionSpeedData[]>;
  average_daily_rate_overconsumption: number;
  average_overconsumption: number;
}

export interface OverconsumptionSpeedData {
  daily_rate_of_overconsumption: number;
  draft: number;
  monthly_points: Record<string, number>;
  overconsumption: number;
  raw_data: OverconsumptionRawDataPoint[];
  shaft_power: number;
  speed: number;
}

export interface OverconsumptionRawDataPoint {
  speed: number;
  draught: number;
  shaft_1_power: number;
  me_1_fc_mass: number;
  me_1_load: number;
  fuel_consumption_without_prop_fouling: number;
  real_occurrence_count: number;
  timestamp: string;
}
