/* eslint-disable @typescript-eslint/no-explicit-any */
import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

type HttpMethod = 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';

interface RequestOptions extends Omit<RequestInit, 'method'> {
  method?: HttpMethod;
  body?: any;
  responseType?: 'json' | 'blob' | 'arrayBuffer';
}

class HttpClient {
  async request<T>(
    req: NextRequest,
    url: string,
    { method, body, headers, responseType = 'json', ...rest }: RequestOptions = {}
  ): Promise<T> {
    const token = await getToken({ req, raw: true });

    const res = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'X-API-Key': process.env.INTERNAL_API_TOKEN || '',
        ...headers,
      },
      body: body ? JSON.stringify(body) : undefined,
      ...rest,
    });

    if (!res.ok) {
      const errorBody = await res.json().catch(() => ({}));
      const message = errorBody.message || `Request failed: ${res.status}`;
      throw new HttpError(message, res.status);
    }

    if (responseType === 'blob') {
      return res.blob() as Promise<T>;
    } else if (responseType === 'arrayBuffer') {
      return res.arrayBuffer() as Promise<T>;
    }

    return res.json();
  }

  get<T>(req: NextRequest, url: string, options?: RequestOptions) {
    return this.request<T>(req, url, { ...options, method: 'GET' });
  }

  post<T>(req: NextRequest, url: string, body: any, options?: RequestOptions) {
    return this.request<T>(req, url, { ...options, method: 'POST', body });
  }

  put<T>(req: NextRequest, url: string, body: any, options?: RequestOptions) {
    return this.request<T>(req, url, { ...options, method: 'PUT', body });
  }

  patch<T>(req: NextRequest, url: string, body: any, options?: RequestOptions) {
    return this.request<T>(req, url, { ...options, method: 'PATCH', body });
  }

  delete<T>(req: NextRequest, url: string, options?: RequestOptions) {
    return this.request<T>(req, url, { ...options, method: 'DELETE' });
  }

  // Method specifically for binary data like PDFs
  async getBinary(req: NextRequest, url: string, options?: RequestOptions) {
    const token = await getToken({ req, raw: true });

    const res = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'X-API-Key': process.env.INTERNAL_API_TOKEN || '',
        ...options?.headers,
      },
      ...options,
    });

    if (!res.ok) {
      const errorBody = await res.text().catch(() => '');
      const message = errorBody || `Request failed: ${res.status}`;
      throw new HttpError(message, res.status);
    }

    return res;
  }
}

export const httpClient = new HttpClient();

export class HttpError extends Error {
  status: number;
  constructor(message: string, status: number) {
    super(message);
    this.status = status;
  }
}
