'use client';
import dynamic from 'next/dynamic';
import HomeFleetFpUsage from './home-fleet-fp-usage';
import React from 'react';
import { useHomeFleetStore } from '../home-fleet.store';
import { useVesselStore } from '@/features/vessels/vessel.store';
import HomeFleetChartEfficiency from './home-fleet-chart-efficiency/home-fleet-chart-efficiency';
import { HomeFleetSummaryFuelConsumption } from './home-fleet-summary-fuel-consumption';
import { HomeFleetSummaryEmissions } from './home-fleet-summary-emissions';
import { HomeFleetSummaryTravel } from './home-fleet-summary-travel';

const HomeFleetMap = dynamic(() => import('./home-fleet-map'), {
  ssr: false,
});

export default function HomeFleetContent() {
  const { fetchFleetData } = useHomeFleetStore();
  const { currentVessel } = useVesselStore();

  React.useEffect(() => {
    if (currentVessel?.imo) {
      fetchFleetData();
    }
  }, [currentVessel, fetchFleetData]);

  return (
    <div className='flex flex-col gap-4'>
      <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
        <div className='flex flex-col gap-4'>
          <HomeFleetSummaryFuelConsumption />
          <HomeFleetSummaryEmissions />
          <HomeFleetSummaryTravel />
        </div>
        <HomeFleetMap />
      </div>

      <div className='grid grid-cols-1 gap-4 xl:grid-cols-2'>
        <HomeFleetFpUsage />
        <HomeFleetChartEfficiency />
      </div>
    </div>
  );
}
