'use client';

import { PageHeader } from '@/components/layout/page-header';
import { CIIYearSelector } from '@/features/cii/components/cii-year-selector';
import CIIOverview from '@/features/cii/components/cii-overview';
import CIIRating from '@/features/cii/components/cii-rating';
import CIIGrade from '@/features/cii/components/cii-grade';
import CIICharts from '@/features/cii/components/cii-charts';
import { Badge } from '@/components/ui/badge';
import React from 'react';
import { useVesselStore } from '@/features/vessels/vessel.store';
import { useCIIStore } from '@/features/cii/cii.store';
import { CiiResetAll } from '@/features/cii/components/cii-reset-all';
export default function CIIPage() {
  const { year, fetchAllCIIData } = useCIIStore();
  const { currentVessel } = useVesselStore();

  React.useEffect(() => {
    if (currentVessel?.imo) {
      fetchAllCIIData();
    }
  }, [currentVessel?.imo, year]);

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <PageHeader title='CII' />
          <Badge variant='outline' className='rounded-md border border-green-400 bg-green-50 px-3 py-1 text-green-800'>
            Estimated
          </Badge>
        </div>
        <div className='flex items-center gap-4'>
          <CiiResetAll />
          <CIIYearSelector />
        </div>
      </div>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3'>
        <CIIOverview />
        <CIIRating />
        <CIIGrade />
      </div>
      <CIICharts />
    </div>
  );
}
