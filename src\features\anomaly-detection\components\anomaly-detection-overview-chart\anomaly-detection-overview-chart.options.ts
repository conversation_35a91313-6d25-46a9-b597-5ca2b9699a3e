import { TimeSeriesStats } from '../../anomaly-detection.types';
import { getSensorLabelWithUnit } from '../../anomaly-detection.store';

// Helper function to format y-axis labels for large numbers
const formatYAxisLabel = (value: number): string => {
  if (!Number.isFinite(value)) return 'N/A';

  const absValue = Math.abs(value);

  // Use scientific notation for numbers >= 1 million
  if (absValue >= 1_000_000) {
    return value.toExponential(2);
  }

  // Use regular formatting for smaller numbers
  return value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

export function getChartOptions(
  selectedSensor: string,
  xAxis: string[],
  yAxis: number[],
  yAxisDetails: TimeSeriesStats[],
  onPointClick?: (dateKey: string, dayData: TimeSeriesStats) => void
) {
  const series: Highcharts.SeriesOptionsType[] = [
    {
      name: getSensorLabelWithUnit(selectedSensor),
      data: yAxis.map((value, index) => {
        const hasAnomalies = yAxisDetails[index]?.total_anomalies_count > 0;
        return {
          x: new Date(xAxis[index]).getTime(),
          y: value,
          color: hasAnomalies ? '#DC2626' : '#DCCD3B', // Red for anomalies, yellow for normal
        };
      }),
      type: 'line',
      color: '#DCCD3B', // Default line color
      marker: { enabled: true },
    },
  ];

  const options: Highcharts.Options = {
    title: { text: '' },
    tooltip: {
      shared: true,
      useHTML: true,
      backgroundColor: 'transparent',
      borderWidth: 0,
      shadow: false,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        const pointIndex = this.points?.[0]?.point?.index;
        const dayData = yAxisDetails[pointIndex];
        const value = this.points?.[0]?.y;
        const date = new Date(this.x).toISOString().split('T')[0]; // Format as YYYY-MM-DD

        if (!dayData || value === undefined) return '';

        const hasAnomalies = dayData.total_anomalies_count > 0;
        const sensorLabel = getSensorLabelWithUnit(selectedSensor);

        // Format value using the same logic as y-axis labels
        const formattedValue = formatYAxisLabel(value);

        if (hasAnomalies) {
          // Anomalous point tooltip
          return `
            <div style="background-color: white; color: black; border: 2px solid #DC2626; border-radius: 4px; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
              <div style="background-color: white; color: black; padding: 8px; border-radius: 2px 2px 0 0; font-weight: bold;">${date}</div>
              <hr style="margin: 0; border: none; border-top: 1px solid #DC2626;" />
              <div style="padding: 8px;">
                <div style="margin-bottom: 2px;">${sensorLabel} (Avg.): ${formattedValue}</div>
                <div style="margin-bottom: 2px;">Hours With Frozen Values: ${dayData.total_frozen_count}</div>
                <div>Hours With Outlier Values: ${dayData.total_outlier_count}</div>
              </div>
            </div>
          `;
        } else {
          // Non-anomalous point tooltip
          return `
            <div style="background-color: white; color: black; border: 2px solid #DCCD3B; border-radius: 4px; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
              <div style="background-color: white; color: black; padding: 8px; border-radius: 2px 2px 0 0; font-weight: bold;">${date}</div>
              <hr style="margin: 0; border: none; border-top: 1px solid #DCCD3B;" />
              <div style="padding: 8px;">
                <div>${sensorLabel} (Avg.): ${formattedValue}</div>
              </div>
            </div>
          `;
        }
      },
    },
    exporting: { enabled: false },
    credits: { enabled: false },
    chart: { zooming: { type: 'x' } },
    series,
    plotOptions: {
      series: {
        point: {
          events: {
            click: function (this: Highcharts.Point) {
              const pointIndex = this.index;
              const dateKey = xAxis[pointIndex];
              const dayData = yAxisDetails[pointIndex];

              // Only trigger click for anomaly points (red points)
              if (dayData?.total_anomalies_count > 0 && onPointClick) {
                onPointClick(dateKey, dayData);
              }
            },
          },
        },
        // Series events will be added by the ChartRenderer component
        events: {},
      },
    },
    xAxis: {
      crosshair: true,
      type: 'datetime',
      labels: { style: { color: 'black' } },
    },
    yAxis: [
      {
        title: { text: getSensorLabelWithUnit(selectedSensor), style: { color: 'black' } },
        labels: {
          style: { color: 'black' },
          formatter: function (this: Highcharts.AxisLabelsFormatterContextObject) {
            return formatYAxisLabel(Number(this.value));
          },
        },
      },
    ],
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
            },
          },
        },
      ],
    },
  };

  return options;
}
