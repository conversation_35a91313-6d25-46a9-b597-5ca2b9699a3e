import { TimestampRecord } from '../../anomaly-detection.types';
import { getSensorLabelWithUnit } from '../../anomaly-detection.store';

// Helper function to format y-axis labels for large numbers
const formatYAxisLabel = (value: number): string => {
  if (!Number.isFinite(value)) return 'N/A';

  const absValue = Math.abs(value);

  // Use scientific notation for numbers >= 1 million
  if (absValue >= 1_000_000) {
    return value.toExponential(2);
  }

  // Use regular formatting for smaller numbers
  return value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

export function getPopupChartOptions(selectedSensor: string, xAxis: string[], yAxis: (number | undefined)[], records: TimestampRecord[]) {
  const chartData = yAxis.map((value, index) => {
    const record = records[index];
    const isAnomaly = record?.is_anomalies;

    // Color coding: red for anomalies, yellow for normal
    let color = '#DCCD3B'; // Default yellow
    if (isAnomaly) {
      color = '#DC2626'; // Red for anomalies
    }

    const point = {
      x: new Date(xAxis[index]).getTime(),
      y: value,
      color: color,
    };

    return point;
  });

  const series: Highcharts.SeriesOptionsType[] = [
    {
      name: getSensorLabelWithUnit(selectedSensor),
      data: chartData,
      type: 'line',
      color: '#DCCD3B', // Default line color
      marker: { enabled: true, radius: 4 },
    },
  ];

  return {
    title: { text: '' },
    tooltip: {
      shared: true,
      useHTML: true,
      backgroundColor: 'transparent',
      borderWidth: 0,
      shadow: false,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formatter: function (this: any) {
        const pointIndex = this.points?.[0]?.point?.index;
        const record = records[pointIndex];

        if (!record) return '';

        const date = new Date(record.timestamp);
        const formattedDate =
          date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0');
        const formattedTime =
          String(date.getHours()).padStart(2, '0') +
          ':' +
          String(date.getMinutes()).padStart(2, '0') +
          ':' +
          String(date.getSeconds()).padStart(2, '0');
        const timestamp = `${formattedDate} ${formattedTime}`;
        const value = this.points?.[0]?.y;
        const sensorLabel = getSensorLabelWithUnit(selectedSensor);

        // Format value using the same logic as y-axis labels
        const formattedValue = formatYAxisLabel(value);

        // Determine if this is an anomalous point and get border color
        const isAnomaly = record.is_anomalies;
        const hasOutlier = record.has_outlier;
        const hasFrozen = record.has_frozen;

        let borderColor = '#DCCD3B'; // Default yellow
        if (isAnomaly) {
          borderColor = '#DC2626'; // Red for anomalies
        }

        if (isAnomaly || hasOutlier || hasFrozen) {
          // Anomalous/Outlier/Frozen point tooltip
          return `
            <div style="background-color: white; color: black; border: 2px solid ${borderColor}; border-radius: 4px; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
              <div style="background-color: white; color: black; padding: 8px; border-radius: 2px 2px 0 0; font-weight: bold;">${timestamp}</div>
              <hr style="margin: 0; border: none; border-top: 1px solid ${borderColor};" />
              <div style="padding: 8px;">
                <div style="margin-bottom: 2px;">${sensorLabel} (Avg.): ${formattedValue}</div>
                ${record.frozen_count > 0 ? `<div style="margin-bottom: 2px;">Minutes Of Frozen Values: ${record.frozen_count}</div>` : ''}
                ${record.outlier_count > 0 ? `<div>Minutes Of Outlier Values: ${record.outlier_count}</div>` : ''}
              </div>
            </div>
          `;
        } else {
          // Normal point tooltip
          return `
            <div style="background-color: white; color: black; border: 2px solid ${borderColor}; border-radius: 4px; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
              <div style="background-color: white; color: black; padding: 8px; border-radius: 2px 2px 0 0; font-weight: bold;">${timestamp}</div>
              <hr style="margin: 0; border: none; border-top: 1px solid ${borderColor};" />
              <div style="padding: 8px;">
                <div>${sensorLabel} (Avg.): ${formattedValue}</div>
              </div>
            </div>
          `;
        }
      },
    },
    exporting: { enabled: false },
    credits: { enabled: false },
    chart: {
      zooming: { type: 'x' },
      width: null, // Let it use container width
      spacingLeft: 10,
      spacingRight: 10,
    },
    series,
    xAxis: {
      crosshair: true,
      type: 'datetime',
      labels: {
        style: { color: 'black' },
        formatter: function (this: Highcharts.AxisLabelsFormatterContextObject) {
          const date = new Date(this.value);
          return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          });
        },
      },
    },
    yAxis: [
      {
        title: { text: getSensorLabelWithUnit(selectedSensor), style: { color: 'black' } },
        labels: {
          style: { color: 'black' },
          formatter: function (this: Highcharts.AxisLabelsFormatterContextObject) {
            return formatYAxisLabel(Number(this.value));
          },
        },
      },
    ],
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'top',
      layout: 'horizontal',
    },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768,
          },
          chartOptions: {
            chart: {
              height: 300,
            },
          },
        },
        {
          condition: {
            maxWidth: 480,
          },
          chartOptions: {
            chart: {
              height: 220,
            },
          },
        },
      ],
    },
  };
}
