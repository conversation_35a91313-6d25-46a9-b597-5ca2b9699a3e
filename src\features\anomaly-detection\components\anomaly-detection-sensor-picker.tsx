'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { FilterIcon } from 'lucide-react';
import { useAnomalyDetectionStore } from '../anomaly-detection.store';

export function AnomalyDetectionSensorPicker() {
  const { availableSensorOptions, selectedSensors, setSelectedSensors, setSelectedSensor, isLoading } = useAnomalyDetectionStore();

  const selectAllRef = React.useRef<HTMLButtonElement>(null);

  const allSelected = selectedSensors.length === availableSensorOptions.length;
  const isPartialSelection = selectedSensors.length > 0 && !allSelected;

  const toggleSensor = (value: string) => {
    const newSelectedSensors = selectedSensors.includes(value)
      ? selectedSensors.filter((item) => item !== value)
      : [...selectedSensors, value];

    setSelectedSensors(newSelectedSensors);

    // Update selectedSensor to the first selected sensor, or clear if none selected
    if (newSelectedSensors.length > 0) {
      setSelectedSensor(newSelectedSensors[0]);
    } else {
      setSelectedSensor('');
    }
  };

  const toggleSelectAll = () => {
    if (allSelected) {
      setSelectedSensors([]);
      setSelectedSensor('');
    } else {
      const allSensorValues = availableSensorOptions.map((opt) => opt.value);
      setSelectedSensors(allSensorValues);
      // Set the first available sensor as the selected sensor for the chart
      if (allSensorValues.length > 0) {
        setSelectedSensor(allSensorValues[0]);
      }
    }
  };

  React.useEffect(() => {
    if (selectAllRef.current) {
      (selectAllRef.current as unknown as HTMLInputElement).indeterminate = isPartialSelection;
    }
  }, [isPartialSelection]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline' className='flex items-center gap-2' disabled={isLoading}>
          <FilterIcon className='h-5 w-5' />
          {selectedSensors.length > 0 ? (
            <div className='flex items-center gap-1'>
              {selectedSensors.slice(0, 2).map((value) => (
                <span key={value} className='rounded bg-gray-200 px-2 py-0.5 text-xs'>
                  {availableSensorOptions.find((opt) => opt.value === value)?.label}
                </span>
              ))}
              {selectedSensors.length > 2 && <span className='text-muted-foreground text-xs'>+{selectedSensors.length - 2} more</span>}
            </div>
          ) : (
            <span>Select sensors</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-62 p-4'>
        <h4 className='mb-2 text-sm font-medium'>Sensors</h4>
        <div className='mb-2 flex items-center gap-2'>
          <Checkbox id='select-all' ref={selectAllRef} checked={allSelected} onCheckedChange={toggleSelectAll} disabled={isLoading} />
          <label htmlFor='select-all' className='text-sm font-medium'>
            Select All
          </label>
        </div>
        <div className='max-h-64 space-y-2 overflow-y-auto pr-1'>
          {availableSensorOptions.map((option) => (
            <div key={option.value} className='flex items-center gap-2'>
              <Checkbox
                id={option.value}
                checked={selectedSensors.includes(option.value)}
                onCheckedChange={() => toggleSensor(option.value)}
                disabled={isLoading}
              />
              <label htmlFor={option.value} className='text-sm'>
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
