import { FleetEfficiencyGetDTO } from '../../home-fleet-efficiency.types';
import { VesselDTO } from '@/features/vessels/vessel.types';
import { Series } from './home-fleet-chart-efficiency.types';

export function useChartData(data: FleetEfficiencyGetDTO | null, vessels: VesselDTO[]): Series | null {
  if (!Array.isArray(data) || vessels.length === 0) return null;

  const vesselNames: string[] = [];
  const sfoc: number[] = [];
  const propulsionEfficiency: number[] = [];
  const sfocHours: string[] = [];
  const propulsionHours: string[] = [];
  const sfocSpeed: number[] = [];
  const propulsionSpeed: number[] = [];

  data.forEach((entry) => {
    const imo = Object.keys(entry)?.[0];
    const vesselData = entry?.[imo];

    if (!imo || !vesselData || !vesselData.fuel || !vesselData.propulsion) return;

    const vessel = vessels.find((v) => v?.imo?.toString() === imo);
    const vesselName = vessel?.vessel_name ?? `Unknown (${imo})`;

    vesselNames.push(vesselName);

    sfoc.push(typeof vesselData.fuel.efficiency === 'number' ? vesselData.fuel.efficiency : 0);
    propulsionEfficiency.push(typeof vesselData.propulsion.efficiency === 'number' ? vesselData.propulsion.efficiency : 0);

    sfocHours.push(typeof vesselData.fuel.hours_sailed === 'string' ? vesselData.fuel.hours_sailed : 'N/A');
    propulsionHours.push(typeof vesselData.propulsion.hours_sailed === 'string' ? vesselData.propulsion.hours_sailed : 'N/A');

    sfocSpeed.push(typeof vesselData.fuel.speed === 'number' ? vesselData.fuel.speed : 0);
    propulsionSpeed.push(typeof vesselData.propulsion.speed === 'number' ? vesselData.propulsion.speed : 0);
  });

  return {
    vesselNames,
    sfoc,
    propulsionEfficiency,
    sfocHours,
    propulsionHours,
    sfocSpeed,
    propulsionSpeed,
  };
}
