'use client';

import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useVoyageStore } from '../voyage.store';

export function VoyageYearSelector() {
  const { year, setYear, isLoading, availableYears } = useVoyageStore();

  return (
    <Select defaultValue={year} onValueChange={(value) => setYear(value)} disabled={isLoading}>
      <SelectTrigger>
        <SelectValue placeholder='Select a year' />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Years</SelectLabel>
          {availableYears.map((yearStr: string) => (
            <SelectItem key={yearStr} value={yearStr}>
              {yearStr}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
