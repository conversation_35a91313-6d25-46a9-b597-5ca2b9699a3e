import { NextRequest, NextResponse } from 'next/server';
import { httpClient, HttpError } from '@/lib/http-client';
import { z } from 'zod';

// === SCHEMA ===
const querySchema = z.object({
  vessel_imo: z.string().nonempty('vessel_imo must be provided'),
  owner_vat: z.string().nonempty('owner_vat must be provided'),
});

// === ENDPOINT ===
export async function GET(request: NextRequest) {
  // Validate query params
  const validatedQuery = querySchema.safeParse(Object.fromEntries(request.nextUrl.searchParams));
  if (!validatedQuery.success) {
    return NextResponse.json({ errors: validatedQuery.error.flatten().fieldErrors }, { status: 400 });
  }

  // Build URLs
  const fromDateUrl = new URL('/export/cylinder/from', process.env.API_URL);
  const toDateUrl = new URL('/export/cylinder/to', process.env.API_URL);

  // Set query params
  [fromDateUrl, toDateUrl].forEach((url) => {
    url.search = new URLSearchParams(validatedQuery.data).toString();
  });

  // Make requests
  try {
    const [fromDateRes, toDateRes] = await Promise.all([
      httpClient.get<string[]>(request, fromDateUrl.toString()),
      httpClient.get<string[]>(request, toDateUrl.toString()),
    ]);

    return NextResponse.json({
      from_date: fromDateRes[0] ?? null,
      to_date: toDateRes[0] ?? null,
    });
  } catch (error) {
    if (error instanceof HttpError) {
      return NextResponse.json({ error: error.message }, { status: error.status });
    }
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
