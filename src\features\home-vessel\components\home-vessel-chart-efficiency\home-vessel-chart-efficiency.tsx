'use client';

import React from 'react';
import { useHomeVesselStore } from '../../home-vessel.store';
import { useChartDataDaily } from './home-vessel-chart-efficiency.data';
import { getChartOptions } from './home-vessel-chart-efficiency.options';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import HomeVesselChartEfficiencyFilter from './home-vessel-chart-efficiency-filter';

export default function HomeVesselChartEfficiency() {
  const [mode, setMode] = React.useState<'daily' | 'monthly'>('daily');

  const { vesselEfficiency, isLoadingVesselEfficiency } = useHomeVesselStore();

  const chartData = useChartDataDaily(vesselEfficiency ?? null, mode);

  const isEmpty = !isLoadingVesselEfficiency && !chartData;

  const options = chartData ? getChartOptions(chartData) : undefined;

  return (
    <div className='bg-card rounded-xl border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='relative text-lg font-semibold tracking-tight'>{`Efficiency - ${mode === 'daily' ? 'Daily' : 'Monthly'}`}</div>
        <div className='flex items-center gap-2'>
          <Tabs value={mode} onValueChange={(value) => setMode(value as 'daily' | 'monthly')}>
            <TabsList className='flex'>
              <TabsTrigger value='daily' disabled={isLoadingVesselEfficiency}>
                Daily
              </TabsTrigger>
              <TabsTrigger value='monthly' disabled={isLoadingVesselEfficiency}>
                Monthly
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <HomeVesselChartEfficiencyFilter />
        </div>
      </div>
      {isLoadingVesselEfficiency ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        <HighchartsReact key={JSON.stringify(options)} highcharts={Highcharts} options={options} />
      )}
    </div>
  );
}
