'use client';

import { useAnomalyDetectionStore } from '../../anomaly-detection.store';
import { AnomalyMetricsBySignal, SignalAnomalySummary, TimeSeriesStats } from '../../anomaly-detection.types';
import { useChartData } from './anomaly-detection-overview-chart.data';
import { getChartOptions } from './anomaly-detection-overview-chart.options';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import { useState } from 'react';
import AnomalyDetectionDetailPopup from '../anomaly-detection-detail-popup/anomaly-detection-detail-popup';
import ChartRenderer from './anomaly-detection-overview-chart-renderer';

export default function AnomalyDetectionOverviewChart() {
  const { anomalyDetectionData, isLoading, selectedSensor } = useAnomalyDetectionStore();
  const [selectedDayData, setSelectedDayData] = useState<{ date: string; data: TimeSeriesStats; sensor: string } | null>(null);
  const [hasVisibleSeries, setHasVisibleSeries] = useState(true);
  const [userDisabledSeries, setUserDisabledSeries] = useState(false);

  const chartData = useChartData(
    selectedSensor ? (anomalyDetectionData?.[selectedSensor as keyof AnomalyMetricsBySignal] as SignalAnomalySummary) : null
  );

  // Handle point click for anomaly details
  const handlePointClick = (dateKey: string, dayData: TimeSeriesStats) => {
    if (dayData.total_anomalies_count > 0) {
      setSelectedDayData({
        date: dateKey,
        data: dayData,
        sensor: selectedSensor,
      });
    }
  };

  const options = chartData
    ? getChartOptions(selectedSensor, chartData?.xAxis || [], chartData?.yAxis || [], chartData?.yAxisDetails || [], handlePointClick)
    : undefined;

  const isEmpty = !isLoading && (!chartData || !chartData.xAxis?.length || !chartData.yAxis?.length);
  const hasData = !isEmpty;

  return (
    <>
      <div className='bg-card text-card-foreground relative min-h-[300px] rounded-md border p-4'>
        <div className='text-lg font-semibold tracking-tight'>Anomalies</div>
        {isLoading ? (
          <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
            <ChartLoader />
          </div>
        ) : isEmpty ? (
          <NoDataPlaceholder message='No Data Available' />
        ) : (
          options && (
            <ChartRenderer
              chartOptions={options}
              chartKey={0}
              hasVisibleSeries={hasVisibleSeries}
              userDisabledSeries={userDisabledSeries}
              hasData={hasData}
              setHasVisibleSeries={setHasVisibleSeries}
              setUserDisabledSeries={setUserDisabledSeries}
            />
          )
        )}
      </div>

      {/* Anomaly Detail Popup */}
      <AnomalyDetectionDetailPopup isOpen={!!selectedDayData} onClose={() => setSelectedDayData(null)} dayData={selectedDayData} />
    </>
  );
}
