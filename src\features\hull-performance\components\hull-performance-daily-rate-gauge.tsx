'use client';

import React from 'react';
import GaugeChart from '@/components/ui-extensions/gauge-chart';
import { useHullPerformanceStore } from '../hull-performance.store';

export default function DailyRateGauge() {
  const { dailyRateOfShaftChange, isLoading } = useHullPerformanceStore();

  return (
    <GaugeChart
      title='Daily Rate of Shaft Change'
      value={dailyRateOfShaftChange}
      min={-200}
      max={200}
      unit='kW/h'
      isPositiveGood={false}
      isLoading={isLoading}
    />
  );
}
