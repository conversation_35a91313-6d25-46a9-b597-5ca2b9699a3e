import { VesselEfficiencyGetDTO } from '../../home-vessel-efficiency.types';
import { Series } from './home-vessel-chart-efficiency.types';

export function useChartDataDaily(data: VesselEfficiencyGetDTO | null, mode: 'daily' | 'monthly'): Series | null {
  if (!data || !data.reports) return null;

  const reports = data.reports;

  const fuelEfficiency =
    mode === 'daily' ? (reports?.efficiencies_daily?.fuel_efficiency ?? []) : (reports?.efficiencies_monthly?.fuel_efficiency ?? []);

  const propulsionEfficiency =
    mode === 'daily'
      ? (reports?.efficiencies_daily?.propulsion_efficiency ?? [])
      : (reports?.efficiencies_monthly?.propulsion_efficiency ?? []);

  if (!Array.isArray(fuelEfficiency) || !Array.isArray(propulsionEfficiency)) return null;

  const dayLabels: string[] = [];
  const sfocValues: number[] = [];
  const speedValues: number[] = [];
  const propulsionValues: number[] = [];
  const sfocTimeValues: string[] = [];
  const propulsionTimeValues: string[] = [];

  const speedTypeRaw = reports?.efficiencies_daily?.speed_type;
  const speedType: 'Log Speed' | 'GPS Speed' | 'Unknown' =
    speedTypeRaw === 'log_speed' ? 'Log Speed' : speedTypeRaw === 'gps_speed' ? 'GPS Speed' : 'Unknown';

  fuelEfficiency.forEach((entry) => {
    const first = Object.entries(entry)?.[0];
    if (!first) return;

    const [dateKey, details] = first;
    const date = new Date(dateKey);
    const formattedDate = isNaN(date.getTime())
      ? dateKey // fallback to raw date string
      : mode === 'daily'
        ? date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        : date.toLocaleDateString('en-US', { month: 'short' });

    const sfocValue = Number(details?.[dateKey]);
    const speed = Number(details?.speed);
    const time = typeof details?.time === 'string' ? details.time : 'N/A';

    dayLabels.push(formattedDate);
    sfocValues.push(isFinite(sfocValue) ? sfocValue : 0);
    speedValues.push(isFinite(speed) ? speed : 0);
    sfocTimeValues.push(time);
  });

  propulsionEfficiency.forEach((entry) => {
    const first = Object.entries(entry)?.[0];
    if (!first) return;

    const [, details] = first;
    const propulsion = Number(details?.[Object.keys(details)[0]]);
    const time = typeof details?.time === 'string' ? details.time : 'N/A';

    propulsionValues.push(isFinite(propulsion) ? propulsion : 0);
    propulsionTimeValues.push(time);
  });

  return {
    dayLabels,
    sfocValues,
    speedValues,
    propulsionValues,
    sfocTimeValues,
    propulsionTimeValues,
    speedType,
  };
}
