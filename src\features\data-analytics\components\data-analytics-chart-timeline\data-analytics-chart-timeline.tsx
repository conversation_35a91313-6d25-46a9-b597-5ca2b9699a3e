'use client';

import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useDataAnalyticsStore } from '../../data-analytics.store';
import { useChartData } from './data-analytics-chart-timeline.data';
import { getChartOptions } from './data-analytics-chart-timeline.options';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import React from 'react';

interface Props {
  chartId: string;
  chartRef: React.RefObject<Highcharts.Chart | null>;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
}

// Used in getChartOptions(). Is actually a let (not const)
const isSyncing = false;

export default function DataAnalyticsChartTimeline({ chartId, chartRef, allChartRefs }: Props) {
  const { dataAnalytics, isLoading, fromDate } = useDataAnalyticsStore();

  const chartData = useChartData(dataAnalytics);

  // Dynamically load Highcharts modules
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const loadModules = async () => {
        try {
          const [xrangeModule] = await Promise.all([import('highcharts/modules/xrange')]);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const applyModule = (module: any, HighchartsRef: typeof Highcharts) => {
            if (typeof module === 'function') {
              module(HighchartsRef);
            } else if (typeof module.default === 'function') {
              module.default(HighchartsRef);
            }
          };
          applyModule(xrangeModule, Highcharts);
        } catch (error) {
          console.error('Failed to load Highcharts modules:', error);
        }
      };
      loadModules();
    }
  }, []);

  const options = chartData ? getChartOptions({ ...chartData, fromDate, chartId, allChartRefs, isSyncing }) : undefined;

  const isEmpty = !isLoading && !chartData;

  return (
    <div className='bg-card text-card-foreground relative min-h-[300px] rounded-md border p-4'>
      <div className='mb-4 flex flex-col gap-1 md:flex-row md:items-center md:justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Timeline</div>
      </div>
      {isLoading ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[200px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        options && (
          <HighchartsReact
            key={JSON.stringify(options)}
            highcharts={Highcharts}
            options={options}
            callback={(chart: Highcharts.Chart) => {
              chartRef.current = chart;
            }}
          />
        )
      )}
    </div>
  );
}
