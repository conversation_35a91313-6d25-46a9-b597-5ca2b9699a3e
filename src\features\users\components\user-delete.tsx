'use client';

import { AlertDialog, AlertDialogFooter } from '@/components/ui/alert-dialog';
import { AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { AlertDialogContent } from '@/components/ui/alert-dialog';
import { AlertDialogDescription, AlertDialogHeader } from '@/components/ui/alert-dialog';
import { AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useUserStore } from '../user.store';
import { Button } from '@/components/ui/button';
import { Trash } from 'lucide-react';
import React from 'react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { UserGetDTO, UserRole } from '../user.types';
import { useSession } from 'next-auth/react';
import { ExtendedSession } from '@/lib/auth';

export function UserDelete({ user }: { user: UserGetDTO }) {
  const { deleteUser } = useUserStore();
  const [isLoading, setIsLoading] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const { data: session } = useSession();

  if (!session) {
    return (
      <Button variant='ghost' size='icon'>
        <CircleSpinner variant='primary' size='xs' />
      </Button>
    );
  }

  const extendedSession = session as ExtendedSession;
  const role = extendedSession.current_owner.role;

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      await deleteUser({ ...user });
      setOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant='ghost' size='icon' disabled={isLoading || role === UserRole.Basic}>
          {isLoading ? <CircleSpinner size='xs' variant='error' /> : <Trash className='h-4 w-4 text-red-500' />}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete {user.email}?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently remove the user from your records.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant={'outline'} onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button variant={'destructive'} onClick={handleDelete} disabled={isLoading}>
            {isLoading && <CircleSpinner size='xs' />}
            Delete
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
