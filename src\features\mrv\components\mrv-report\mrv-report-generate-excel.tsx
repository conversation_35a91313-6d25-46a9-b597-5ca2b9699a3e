'use client';

import { Button } from '@/components/ui/button';
import React from 'react';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { FileDown } from 'lucide-react';
import { useMrvTableStore } from '../mrv-table/mrv-table.store';
import { toast } from 'sonner';
import { useVesselStore } from '@/features/vessels/vessel.store';
import axios from 'axios';

export function MrvReportGenerateExcel() {
  const [isLoading, setIsLoading] = React.useState(false);
  const { year } = useMrvTableStore();

  const handleGenerateReport = async () => {
    setIsLoading(true);

    try {
      // Get the current vessel after ensuring it's loaded
      const currentVessel = useVesselStore.getState().currentVessel;

      // Vessel and year check
      if (!currentVessel || !year) {
        toast.error('No vessel selected or year not set.');
        return;
      }

      // Generate the report URL with query parameters
      const requestUrl = new URL('/api/mrv/report/excel', window.location.origin);
      requestUrl.search = new URLSearchParams({
        vessel_imo: currentVessel.imo.toString(),
        vessel_name: currentVessel.vessel_name,
        year: year.toString(),
        owner_vat: currentVessel.assigned_owner_vat,
      }).toString();

      // Fetch the PDF file
      const response = await axios.get(requestUrl.toString(), {
        responseType: 'blob',
      });

      // Response check
      if (response.status !== 200) {
        toast.error('Failed to generate report');
        return;
      }

      // Create a link to download the file
      const blob = response.data;
      const downloadUrl = window.URL.createObjectURL(blob);
      const fileName = `MRV-${year}-${currentVessel.vessel_name}.xlsx`;

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      // Success toast
      toast.success('Report download successful');
    } catch (error: unknown) {
      // Error toast
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('An unexpected error occurred');
      }
    } finally {
      // Reset loading state
      setIsLoading(false);
    }
  };

  return (
    <Button variant='default' onClick={handleGenerateReport} disabled={isLoading || !year}>
      {isLoading ? <CircleSpinner size='xs' variant='default' /> : <FileDown className='h-4 w-4' />}
      Download Excel
    </Button>
  );
}
