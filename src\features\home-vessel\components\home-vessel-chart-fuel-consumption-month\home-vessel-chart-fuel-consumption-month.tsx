import React from 'react';
import { useChartDataDaily } from './home-vessel-chart-fuel-consumption-month.data';
import { getChartOptionsDaily } from './home-vessel-chart-fuel-consumption-month.option';

import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';

import { SeriesDaily } from './home-vessel-chart-fuel-consumption-month.types';
import { MonthlyFuelReport } from '../../home-vessel.types';

export default function HomeVesselChartFuelConsumptionMonth({
  data,
  month,
  type,
  speedType,
}: {
  data: MonthlyFuelReport;
  month: string;
  type: 'main' | 'aux' | 'boiler';
  speedType: 'Log Speed' | 'GPS Speed' | 'Unknown';
}) {
  if (!month || !data) return null;

  const chartData = useChartDataDaily(data, type, speedType);

  const options = getChartOptionsDaily(chartData as SeriesDaily, month, speedType);

  return <HighchartsReact key={JSON.stringify(options)} highcharts={Highcharts} options={options} />;
}
