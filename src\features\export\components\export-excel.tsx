'use client';

import { FormDateTimePicker } from '@/components/ui-extensions/form-date-time-picker';
import { Button } from '@/components/ui/button';
import { Table } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { toast } from 'sonner';
import { formatDateTime } from '../utils/date-formatter';
import { useVesselStore } from '@/features/vessels/vessel.store';

interface ExportExcelFormData {
  fromDate: Date;
  toDate: Date;
  excludeAnomalies: boolean;
  sortAlphabetically: boolean;
}

export default function ExportExcel() {
  const [isExporting, setIsExporting] = useState(false);
  const { currentVessel } = useVesselStore();

  const form = useForm<ExportExcelFormData>({
    defaultValues: {
      fromDate: new Date(new Date().setDate(new Date().getDate() - 7)), // 7 days ago
      toDate: new Date(),
      excludeAnomalies: false,
      sortAlphabetically: false,
    },
  });

  const handleExport = async (data: ExportExcelFormData) => {
    if (!currentVessel) {
      toast.error('No vessel selected');
      return;
    }

    setIsExporting(true);
    try {
      // Format dates for API call
      const fromDate = formatDateTime(data.fromDate);
      const toDate = formatDateTime(data.toDate);

      // Create download URL with boolean values converted to strings
      const downloadUrl = `/api/download-excel?vessel_imo=${currentVessel.imo}&vessel_id=${currentVessel.id}&owner_vat=${encodeURIComponent(
        currentVessel.assigned_owner_vat
      )}&from_date=${encodeURIComponent(fromDate)}&to_date=${encodeURIComponent(toDate)}&vessel_name=${encodeURIComponent(
        currentVessel.vessel_name
      )}&sorted_type=${data.sortAlphabetically ? 'true' : 'false'}&exclude_anomalies=${data.excludeAnomalies ? 'true' : 'false'}`;

      // Fetch the Excel file
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        // Try to get error message from response
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to download Excel data');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Format dates for filename
      const fromDateFormatted = data.fromDate.toISOString().split('T')[0];
      const toDateFormatted = data.toDate.toISOString().split('T')[0];

      // Create a link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `Excel-Data-${currentVessel.vessel_name || 'vessel'}-${fromDateFormatted}-to-${toDateFormatted}.xlsx`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Excel export completed successfully');
    } catch (error) {
      console.error('Error exporting Excel:', error);
      toast.error('Failed to export Excel data');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-2 flex items-center gap-2'>
        <div className='text-lg font-semibold tracking-tight'>Excel</div>
        <Table className='h-4 w-4' />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleExport)} className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormDateTimePicker control={form.control} name='fromDate' label='From' />
            <FormDateTimePicker control={form.control} name='toDate' label='To' />
          </div>
          <div className='flex items-center justify-end gap-6'>
            <FormField
              control={form.control}
              name='excludeAnomalies'
              render={({ field }) => (
                <FormItem className='flex items-center gap-2 space-y-0'>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className='text-sm font-normal'>Excl. anomalies?</FormLabel>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='sortAlphabetically'
              render={({ field }) => (
                <FormItem className='flex items-center gap-2 space-y-0'>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className='text-sm font-normal'>Sort alph.?</FormLabel>
                </FormItem>
              )}
            />
            <Button type='submit' disabled={isExporting}>
              {isExporting ? <>Exporting...</> : <>Export</>}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
