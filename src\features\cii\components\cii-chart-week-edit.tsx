'use client';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ExternalLink, Plus, Trash2 } from 'lucide-react';
import React from 'react';
import { CiiResetWeek } from './cii-reset-week';
import { useCIIStore } from '../cii.store';

export type CiiChartWeekEditRef = {
  open: (data: { week_numb: number; distance: number; fuel: { fuelType: string; fuelConsumption: number }[] }) => void;
};

const CiiChartWeekEdit = forwardRef<CiiChartWeekEditRef>((_, ref) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<{
    week_numb: number;
    distance: number;
    fuel: { fuelType: string; fuelConsumption: number }[];
  } | null>(null);
  const { updateWeeklyCIIData } = useCIIStore();

  useImperativeHandle(ref, () => ({
    open: (data) => {
      const roundedDistance = Math.round(data.distance * 1000) / 1000;
      const roundedFuels = data.fuel.map((fuel) => ({
        ...fuel,
        value: Math.round(fuel.fuelConsumption * 1000) / 1000,
      }));
      setFormData({ ...data, distance: roundedDistance, fuel: roundedFuels });
      setOpen(true);
    },
  }));

  const updateFuel = (index: number, key: 'fuelType' | 'fuelConsumption', value: string | number) => {
    if (!formData) return;
    const updatedFuels = [...formData.fuel];
    updatedFuels[index] = { ...updatedFuels[index], [key]: value };
    setFormData({ ...formData, fuel: updatedFuels });
  };

  const deleteFuel = (index: number) => {
    if (!formData) return;
    const updatedFuels = formData.fuel.filter((_, i) => i !== index);
    setFormData({ ...formData, fuel: updatedFuels });
  };

  const addFuel = () => {
    if (!formData) return;
    const updatedFuels = [...formData.fuel, { fuelType: '', fuelConsumption: 0 }];
    setFormData({ ...formData, fuel: updatedFuels });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger />
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Week {formData?.week_numb}</DialogTitle>
          <DialogDescription>Update the fuel types and their values for this week.</DialogDescription>
        </DialogHeader>

        <div>
          <label className='text-muted-foreground block text-sm font-medium'>Distance Traveled (nm)</label>
          <Input
            type='number'
            value={formData?.distance ?? ''}
            onChange={(e) => {
              if (!formData) return;
              const rawValue = parseFloat(e.target.value);
              const roundedValue = Math.round(rawValue * 1000) / 1000;
              setFormData({ ...formData, distance: roundedValue });
            }}
            placeholder='Enter distance in nautical miles'
          />
        </div>

        <div className='grid'>
          <div className='grid grid-cols-[120px_1fr_min-content] items-center gap-4'>
            <div className='text-muted-foreground text-sm font-medium'>Fuel Type</div>
            <div className='text-muted-foreground col-span-2 text-sm font-medium'>Value (mt)</div>
          </div>

          <div className='grid grid-cols-[120px_1fr_min-content] items-center gap-4'>
            {formData?.fuel.map((fuel, index) => (
              <React.Fragment key={index}>
                <Select value={fuel.fuelType} onValueChange={(value) => updateFuel(index, 'fuelType', value)}>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select Fuel' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='MDO'>MDO</SelectItem>
                    <SelectItem value='HFO'>HFO</SelectItem>
                    <SelectItem value='LNG'>LNG</SelectItem>
                    <SelectItem value='VLSFO'>VLSFO</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  type='number'
                  value={fuel.fuelConsumption === 0 ? '' : fuel.fuelConsumption}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '') {
                      updateFuel(index, 'fuelConsumption', '');
                    } else {
                      const parsed = parseFloat(value);
                      if (!isNaN(parsed)) {
                        const rounded = Math.round(parsed * 1000) / 1000;
                        updateFuel(index, 'fuelConsumption', rounded);
                      }
                    }
                  }}
                  placeholder='Fuel Value'
                />

                <Button variant='outline' size='icon' onClick={() => deleteFuel(index)}>
                  <Trash2 className='h-4 w-4' />
                </Button>
              </React.Fragment>
            ))}

            <div className='col-span-3 text-end'>
              <Button variant='outline' size='icon' onClick={addFuel}>
                <Plus className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </div>

        <div className='flex justify-between'>
          <div className='flex space-x-2'>
            <Button variant='outline'>
              <ExternalLink className='h-4 w-4' />
              Data Analytics
            </Button>
            <CiiResetWeek week={formData?.week_numb ?? 0} />
          </div>
          <div className='flex space-x-2'>
            <Button variant='outline' onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => updateWeeklyCIIData(formData)}>Save</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

CiiChartWeekEdit.displayName = 'CiiChartWeekEdit';
export default CiiChartWeekEdit;
