'use client';

import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import ChartLoader from '@/components/ui-extensions/chart-loader';
import { useDataAnalyticsStore } from '../../data-analytics.store';
import { useChartData } from './data-analytics-chart-consumption.data';
import { getChartOptions } from './data-analytics-chart-consumption.options';
import { NoDataPlaceholder } from '@/components/ui-extensions/chart-no-data';
import { Switch } from '@/components/ui/switch';

interface Props {
  chartId: string;
  chartRef: React.RefObject<Highcharts.Chart | null>;
  allChartRefs: Record<string, React.RefObject<Highcharts.Chart | null>>;
}

// Used in getChartOptions(). Is actually a let (not const)
const isSyncing = false;

export default function DataAnalyticsChartConsumption({ chartId, chartRef, allChartRefs }: Props) {
  const { dataAnalytics, isLoading } = useDataAnalyticsStore();
  const [showFrugal, setShowFrugal] = React.useState(true);
  const chartData = useChartData(dataAnalytics);

  const isEmpty =
    !isLoading && (!chartData || !chartData.mefcmSeries?.length || !chartData.boifcmSeries?.length || !chartData.auxfcmSeries?.length);

  const options = chartData
    ? getChartOptions({
        ...chartData,
        plotBands: showFrugal ? chartData.plotBands : [],
        chartId,
        allChartRefs,
        isSyncing,
      })
    : undefined;

  return (
    <div className='bg-card text-card-foreground relative min-h-[300px] rounded-md border p-4'>
      <div className='mb-4 flex flex-col gap-1 md:flex-row md:items-center md:justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Main Engine Fuel Consumption (kg/h) And Boiler Fuel Consumption (kg/h)</div>
        <div className='relative right-3 flex items-center gap-2'>
          <Switch checked={showFrugal} onCheckedChange={() => setShowFrugal(!showFrugal)} />
          <label className='text-sm'>Display Frugal Mode</label>
        </div>
      </div>
      {isLoading ? (
        <div className='flex h-[220px] justify-center pt-8 md:h-[200px] md:pt-14 lg:h-[400px] lg:pt-25'>
          <ChartLoader />
        </div>
      ) : isEmpty ? (
        <NoDataPlaceholder message='No Data Available' />
      ) : (
        options && (
          <HighchartsReact
            key={JSON.stringify(options)}
            highcharts={Highcharts}
            options={options}
            callback={(chart: Highcharts.Chart) => {
              chartRef.current = chart;
            }}
          />
        )
      )}
    </div>
  );
}
