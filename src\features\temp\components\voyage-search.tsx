'use client';

import { Input } from '@/components/ui/input';
import { useVoyageStore } from '../voyage.store';

export function VoyageSearch() {
  const { searchQuery, setSearchQuery, isLoading } = useVoyageStore();

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchQuery(value);
  };

  return (
    <Input
      type='text'
      placeholder='Search by port...'
      value={searchQuery}
      onChange={handleInputChange}
      className='w-80'
      disabled={isLoading}
    />
  );
}
