import { create } from 'zustand';
import { OwnerDTO } from './owner.types';
import axios from 'axios';
import { toast } from 'sonner';

interface OwnerStore {
  owners: OwnerDTO[];
  isLoading: boolean;

  loadOwners: () => Promise<void>;
}

export const useOwnerStore = create<OwnerStore>((set) => ({
  owners: [],
  isLoading: false,

  loadOwners: async () => {
    set({ isLoading: true });
    try {
      const { data: owners } = await axios.get<OwnerDTO[]>('/api/owners');
      owners.sort((a, b) => a.name.localeCompare(b.name));
      set({ owners: owners });
    } catch (_error) {
      toast.error('Error loading owners');
    } finally {
      set({ isLoading: false });
    }
  },
}));
