/*'use client';

import { DateRangePicker } from '@/components/ui-extensions/date-range-picker';
import { Button } from '@/components/ui/button';
import { Loader2, Search } from 'lucide-react';
import React, { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useAnomalyDetectionStore } from '../anomaly-detection.store';
import { useVesselStore } from '../../vessels/vessel.store';

function formatDateTime(date: Date): string {
  const pad = (n: number) => String(n).padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
export default function AnomalyDetectionDateSearch() {
  const { isLoading, minDate, maxDate, setDates, fetchAnomalyDetectionData } = useAnomalyDetectionStore();
  const { currentVessel } = useVesselStore();

  const [dateRange, setDateRange] = useState(() => {
    const to = new Date();
    to.setHours(23, 59, 59, 999);

    const from = new Date();
    from.setDate(from.getDate() - 14);
    from.setHours(0, 0, 0, 0);

    return { from, to };
  });

  React.useEffect(() => {
    if (currentVessel?.imo) {
      fetchAnomalyDetectionData(currentVessel.assigned_owner_vat, currentVessel.imo.toString());
    }
  }, [currentVessel?.imo]);

  const handleDateChange = (dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange?.to) {
      const normalized = {
        from: new Date(dateRange.from.setHours(0, 0, 0, 0)),
        to: new Date(dateRange.to.setHours(23, 59, 59, 999)),
      };
      setDateRange(normalized);
    }
  };

  const handleClick = () => {
    setDates(formatDateTime(dateRange.from), formatDateTime(dateRange.to));
  };

  return (
    <div className='flex gap-4'>
      <DateRangePicker
        maxDate={new Date(maxDate)}
        minDate={new Date(minDate)}
        value={dateRange}
        onChange={(range) => handleDateChange(range)}
        disabled={isLoading}
      />
      <Button variant='default' onClick={handleClick} disabled={isLoading}>
        {isLoading ? <Loader2 className='animate-spin' /> : <Search />}
      </Button>
    </div>
  );
}
*/
