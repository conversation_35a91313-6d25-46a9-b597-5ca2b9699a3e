'use client';

import React from 'react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import CircleSpinner from '@/components/ui-extensions/circle-spinner';
import { useMrvStore } from '@/features/mrv/mrv.store';
import { toast } from 'sonner';
import { useMrvVoyageDeleteStore } from '@/features/mrv/components/mrv-voyage-delete.ts/mrv.voyage.delete.store';

export function MrvVoyageDelete() {
  // State
  const [isLoading, setIsLoading] = React.useState(false);

  // Store
  const { deleteVoyage } = useMrvStore();
  const { isOpen, voyage, closeDialog } = useMrvVoyageDeleteStore();

  // Handle the delete action
  const handleDelete = async () => {
    if (voyage == null) return;
    try {
      setIsLoading(true);
      await deleteVoyage(voyage.voyage_id);
      closeDialog();
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('Error deleting voyage');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={closeDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Delete Voyage: {voyage?.departure_port} - {voyage?.destination_port}
          </AlertDialogTitle>
          <AlertDialogDescription>This action cannot be undone. It will permanently remove the voyage.</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button variant='outline' onClick={closeDialog}>
            Cancel
          </Button>
          <Button variant='destructive' onClick={handleDelete} disabled={isLoading}>
            {isLoading && <CircleSpinner size='xs' />}
            Delete
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
