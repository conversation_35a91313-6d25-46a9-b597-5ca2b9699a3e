'use client';

import { useCIIStore } from '../cii.store';
import { DotLoader } from '@/components/ui-extensions/dot-loader';

const formatNumber = (val: unknown, decimals = 2): string => {
  const num = Number(val);
  return Number.isFinite(num) ? num.toFixed(decimals) : 'N/A';
};

const formatTotalFuel = (fuelData: Record<string, number> | undefined): string => {
  if (!fuelData || typeof fuelData !== 'object') return 'N/A';
  const total = Object.values(fuelData).reduce((sum, val) => {
    const num = Number(val);
    return Number.isFinite(num) ? sum + num : sum;
  }, 0);
  return Number.isFinite(total) ? `${total.toFixed(2)} mt` : 'N/A';
};

const VesselStats = ({ label, value, loading }: { label: string; value: string; loading: boolean }) => (
  <div className='flex justify-between'>
    <p className='text-muted-foreground text-sm font-medium'>{label}:</p>
    {loading ? <DotLoader size='0.5rem' /> : <p className='text-sm font-medium'>{value}</p>}
  </div>
);

export default function CIIOverview() {
  const { isLoading, yearData } = useCIIStore();

  return (
    <div className='bg-card text-card-foreground rounded-md border p-4'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='text-lg font-semibold tracking-tight'>Vessel Stats</div>
      </div>

      <div className='space-y-3'>
        <VesselStats
          label='Dead Weight'
          value={yearData?.capacity?.capacity ? `${formatNumber(yearData.capacity.capacity, 0)} mt` : 'N/A'}
          loading={isLoading}
        />
        <VesselStats label='Distance Sailed' value={`${formatNumber(yearData?.distance)} nm`} loading={isLoading} />
        <VesselStats label='Total Fuel Consumption' value={formatTotalFuel(yearData?.fuel_consumption)} loading={isLoading} />
        <VesselStats label='CO₂ Emitted' value={`${formatNumber(yearData?.emissions)} mt`} loading={isLoading} />
      </div>
    </div>
  );
}
