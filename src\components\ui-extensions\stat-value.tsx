import { cn } from '@/lib/utils';
import { DotLoader } from './dot-loader';

export const StatValue = ({
  label,
  value,
  units,
  isLoading,
  className,
}: {
  label: string;
  value?: string | number | null;
  units?: string;
  isLoading?: boolean;
  className?: string;
}) => (
  <div className={cn('flex justify-between', className)}>
    <p className='text-muted-foreground text-sm font-medium'>{label}</p>
    {isLoading ? (
      <DotLoader />
    ) : value != null ? (
      <p className='text-sm font-medium'>
        {typeof value === 'number' ? value.toFixed(2) : value} {units}
      </p>
    ) : (
      <span className='text-muted-foreground text-sm font-medium'>N/A</span>
    )}
  </div>
);
